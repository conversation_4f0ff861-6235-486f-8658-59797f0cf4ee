import { NextRequest, NextResponse } from 'next/server';
import { createServiceRoleClient } from '@/lib/supabase/server';
import { createErrorResponse } from '@/lib/auth/api-auth';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

// Background Job Processing System for Scheduled Posts
// This endpoint processes scheduled posts that are due for publication

export async function POST(request: NextRequest) {
  console.log('⚡ Background job processing started - Scheduled Posts');

  try {
    // Create Supabase client with service role for background processing
    // Use service role to bypass RLS for background jobs
    const supabase = createServiceRoleClient();

    // Get current time for comparison
    const now = new Date();
    console.log('🕐 Current time:', now.toISOString());

    // Find posts that are due for publication
    // Use left join instead of inner join to avoid user filtering in background processing
    const { data: duePosts, error: fetchError } = await supabase
      .from('scheduled_posts_queue')
      .select(`
        id,
        post_id,
        scheduled_for,
        timezone,
        status,
        retry_count,
        priority,
        error_message,
        posts(
          id,
          content,
          media_urls,
          user_id,
          status
        )
      `)
      .eq('status', 'pending')
      .lte('scheduled_for', now.toISOString())
      .order('priority', { ascending: false }) // HIGH priority first
      .order('scheduled_for', { ascending: true }) // Older posts first
      .limit(10); // Process max 10 posts per run

    if (fetchError) {
      console.error('❌ Error fetching due posts:', fetchError);
      return createErrorResponse('فشل في جلب المنشورات المستحقة', 500, fetchError);
    }

    if (!duePosts || duePosts.length === 0) {
      console.log('✅ No posts due for publication');
      return NextResponse.json({
        success: true,
        message: 'No posts due for publication',
        processed: 0,
        timestamp: now.toISOString()
      });
    }

    console.log(`📋 Found ${duePosts.length} posts due for publication`);

    const processingResults = [];

    // Process each due post
    for (const queueEntry of duePosts) {
      console.log(`🔄 Processing post: ${queueEntry.post_id}`);
      
      try {
        // Update status to processing
        await supabase
          .from('scheduled_posts_queue')
          .update({
            status: 'processing',
            updated_at: new Date().toISOString()
          })
          .eq('id', queueEntry.id);

        // Get social accounts for this post
        const { data: postSocialAccounts, error: psaError } = await supabase
          .from('post_social_accounts')
          .select(`
            id,
            social_account_id,
            platform,
            status,
            social_accounts!inner(
              id,
              platform,
              account_id,
              account_name,
              access_token,
              page_access_token,
              instagram_business_account_id,
              metadata
            )
          `)
          .eq('post_id', queueEntry.post_id);

        if (psaError || !postSocialAccounts || postSocialAccounts.length === 0) {
          console.error(`❌ No social accounts found for post ${queueEntry.post_id}:`, psaError);
          
          await updateQueueEntryStatus(supabase, queueEntry.id, 'failed', 'لا توجد حسابات اجتماعية مرتبطة');
          processingResults.push({
            post_id: queueEntry.post_id,
            queue_id: queueEntry.id,
            success: false,
            error: 'No social accounts linked'
          });
          continue;
        }

        console.log(`📱 Found ${postSocialAccounts.length} social accounts for post ${queueEntry.post_id}`);

        // Publish to each social account
        const publishingResults = [];
        
        for (const psa of postSocialAccounts) {
          const account = psa.social_accounts;
          console.log(`📤 Publishing to ${account.platform} (${account.account_name})`);

          try {
            let publishResult;

            // Route to appropriate publisher based on platform
            switch (account.platform.toUpperCase()) {
              case 'INSTAGRAM':
                publishResult = await publishToInstagramScheduled(
                  queueEntry.posts.content,
                  queueEntry.posts.media_urls,
                  account
                );
                break;
              case 'FACEBOOK':
                publishResult = await publishToFacebookScheduled(
                  queueEntry.posts.content,
                  queueEntry.posts.media_urls,
                  account
                );
                break;
              default:
                publishResult = {
                  success: false,
                  error: `Platform ${account.platform} not supported for scheduled publishing`,
                  step: 'platform_validation'
                };
            }

            // Update post_social_accounts with result
            const updateData: any = {
              status: publishResult.success ? 'published' : 'failed',
              updated_at: new Date().toISOString()
            };

            if (publishResult.success) {
              updateData.platform_post_id = publishResult.postId;
              updateData.platform_url = publishResult.postUrl;
              updateData.published_at = new Date().toISOString();
            } else {
              updateData.error_message = publishResult.error;
            }

            await supabase
              .from('post_social_accounts')
              .update(updateData)
              .eq('id', psa.id);

            publishingResults.push({
              social_account_id: account.id,
              platform: account.platform,
              account_name: account.account_name,
              success: publishResult.success,
              error: publishResult.error || null,
              postId: publishResult.postId || null,
              postUrl: publishResult.postUrl || null
            });

            console.log(`${publishResult.success ? '✅' : '❌'} ${account.platform} publishing result:`, publishResult.success ? 'SUCCESS' : publishResult.error);

          } catch (publishError) {
            console.error(`❌ Publishing error for ${account.platform}:`, publishError);
            
            await supabase
              .from('post_social_accounts')
              .update({
                status: 'failed',
                error_message: publishError.message,
                updated_at: new Date().toISOString()
              })
              .eq('id', psa.id);

            publishingResults.push({
              social_account_id: account.id,
              platform: account.platform,
              account_name: account.account_name,
              success: false,
              error: publishError.message
            });
          }
        }

        // Determine overall success
        const allSuccessful = publishingResults.every(r => r.success);
        const someSuccessful = publishingResults.some(r => r.success);

        // Update post status
        let finalPostStatus = 'DRAFT';
        if (allSuccessful) {
          finalPostStatus = 'PUBLISHED';
        } else if (someSuccessful) {
          finalPostStatus = 'PUBLISHED'; // Partially successful
        }

        await supabase
          .from('posts')
          .update({
            status: finalPostStatus,
            published_at: someSuccessful ? new Date().toISOString() : null,
            updated_at: new Date().toISOString()
          })
          .eq('id', queueEntry.post_id);

        // Update queue entry status
        if (allSuccessful) {
          await updateQueueEntryStatus(supabase, queueEntry.id, 'completed', null);
        } else if (someSuccessful) {
          await updateQueueEntryStatus(supabase, queueEntry.id, 'completed', 'نشر جزئي - نجح على بعض المنصات');
        } else {
          // Check if we should retry
          const newRetryCount = queueEntry.retry_count + 1;
          const maxRetries = 3;

          if (newRetryCount < maxRetries) {
            // Schedule for retry (add 5 minutes)
            const retryTime = new Date(Date.now() + 5 * 60 * 1000);

            await supabase
              .from('scheduled_posts_queue')
              .update({
                status: 'pending',
                retry_count: newRetryCount,
                scheduled_for: retryTime.toISOString(),
                error_message: `محاولة ${newRetryCount} من ${maxRetries}`,
                updated_at: new Date().toISOString()
              })
              .eq('id', queueEntry.id);

            console.log(`🔄 Scheduled retry ${newRetryCount}/${maxRetries} for post ${queueEntry.post_id} at ${retryTime.toISOString()}`);
          } else {
            await updateQueueEntryStatus(supabase, queueEntry.id, 'failed', 'فشل في النشر بعد عدة محاولات');
          }
        }

        processingResults.push({
          post_id: queueEntry.post_id,
          queue_id: queueEntry.id,
          success: allSuccessful,
          partial_success: someSuccessful && !allSuccessful,
          publishing_results: publishingResults,
          final_status: allSuccessful ? 'COMPLETED' : someSuccessful ? 'PARTIAL' : 'FAILED'
        });

      } catch (processingError) {
        console.error(`❌ Error processing post ${queueEntry.post_id}:`, processingError);
        
        await updateQueueEntryStatus(supabase, queueEntry.id, 'failed', `خطأ في المعالجة: ${processingError.message}`);
        
        processingResults.push({
          post_id: queueEntry.post_id,
          queue_id: queueEntry.id,
          success: false,
          error: processingError.message
        });
      }
    }

    const successfulPosts = processingResults.filter(r => r.success).length;
    const partialPosts = processingResults.filter(r => r.partial_success).length;
    const failedPosts = processingResults.filter(r => !r.success && !r.partial_success).length;

    console.log(`📊 Processing summary: ${successfulPosts} successful, ${partialPosts} partial, ${failedPosts} failed`);

    return NextResponse.json({
      success: true,
      message: `تم معالجة ${duePosts.length} منشور مجدول`,
      processed: duePosts.length,
      results: {
        successful: successfulPosts,
        partial: partialPosts,
        failed: failedPosts
      },
      details: processingResults,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Background job processing error:', error);
    return createErrorResponse('خطأ في معالجة المنشورات المجدولة', 500, error);
  }
}

// Helper function to update queue entry status
async function updateQueueEntryStatus(supabase: any, queueId: string, status: string, errorMessage: string | null) {
  const updateData: any = {
    status,
    updated_at: new Date().toISOString()
  };

  if (errorMessage) {
    updateData.error_message = errorMessage;
  }

  await supabase
    .from('scheduled_posts_queue')
    .update(updateData)
    .eq('id', queueId);
}

// Instagram publishing function for scheduled posts
async function publishToInstagramScheduled(content: string, mediaUrls: string[], account: any) {
  console.log('📸 Publishing scheduled post to Instagram...');
  
  try {
    if (!mediaUrls || mediaUrls.length === 0) {
      return {
        success: false,
        error: 'منشورات انستغرام تتطلب صورة أو فيديو واحد على الأقل',
        step: 'media_validation'
      };
    }

    const mediaUrl = mediaUrls[0];
    const caption = content.replace(/<[^>]*>/g, ''); // Strip HTML tags
    const instagramAccountId = account.metadata?.instagramAccountId || account.account_id;

    // Step 1: Create media container with appsecret_proof
    const crypto = require('crypto');
    const appSecret = process.env.FACEBOOK_APP_SECRET;
    const appsecret_proof = crypto.createHmac('sha256', appSecret).update(account.access_token).digest('hex');
    
    const containerData = {
      image_url: mediaUrl,
      caption: caption,
      access_token: account.access_token,
      appsecret_proof: appsecret_proof
    };

    const containerResponse = await fetch(
      `https://graph.facebook.com/v18.0/${instagramAccountId}/media`,
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(containerData),
      }
    );

    const containerResult = await containerResponse.json();

    if (!containerResponse.ok) {
      return {
        success: false,
        error: `فشل في إنشاء حاوية الوسائط: ${containerResult.error?.message || 'خطأ غير معروف'}`,
        details: containerResult,
        step: 'container_creation'
      };
    }

    const creationId = containerResult.id;

    // Step 2: Publish the media
    const publishData = {
      creation_id: creationId,
      access_token: account.access_token,
      appsecret_proof: appsecret_proof
    };

    const publishResponse = await fetch(
      `https://graph.facebook.com/v18.0/${instagramAccountId}/media_publish`,
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(publishData),
      }
    );

    const publishResult = await publishResponse.json();

    if (!publishResponse.ok) {
      return {
        success: false,
        error: `فشل في النشر على انستغرام: ${publishResult.error?.message || 'خطأ غير معروف'}`,
        details: publishResult,
        step: 'publishing'
      };
    }

    return {
      success: true,
      postId: publishResult.id,
      postUrl: `https://instagram.com/p/${publishResult.id}`,
      step: 'completed'
    };

  } catch (error) {
    console.error('❌ Instagram scheduled publishing error:', error);
    return {
      success: false,
      error: `خطأ في النشر المجدول على انستغرام: ${error.message}`,
      step: 'critical_error'
    };
  }
}

// Facebook publishing function for scheduled posts (placeholder)
async function publishToFacebookScheduled(content: string, mediaUrls: string[], account: any) {
  console.log('📘 Publishing scheduled post to Facebook...');
  
  // TODO: Implement Facebook scheduled publishing
  return {
    success: false,
    error: 'Facebook scheduled publishing not yet implemented',
    step: 'not_implemented'
  };
}
