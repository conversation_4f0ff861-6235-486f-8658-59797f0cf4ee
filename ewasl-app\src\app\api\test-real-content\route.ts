import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { getAuthenticatedUser, createAuthenticatedResponse, createErrorResponse } from '@/lib/auth/api-auth';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

// Real Content Testing Endpoint for Phase 2B.1 Validation
export async function POST(request: NextRequest) {
  console.log('🧪 REAL CONTENT TESTING - Phase 2B.1 Validation Started');
  
  try {
    // Use unified authentication
    const { user, supabase } = await getAuthenticatedUser(request);
    console.log('✅ User authenticated for real content testing:', user.id);

    const body = await request.json();
    const { testType, content, mediaFiles, platforms } = body;

    console.log('🧪 Real content test request:', {
      testType,
      contentLength: content?.length,
      mediaFilesCount: mediaFiles?.length,
      platforms
    });

    // Test different scenarios based on testType
    switch (testType) {
      case 'ARABIC_RTL_TEXT':
        return await testArabicRTLContent(supabase, user, content, mediaFiles);
      
      case 'SUPABASE_STORAGE_MEDIA':
        return await testSupabaseStorageMedia(supabase, user, content, mediaFiles);
      
      case 'VARIOUS_MEDIA_FORMATS':
        return await testVariousMediaFormats(supabase, user, content, mediaFiles);
      
      case 'DATABASE_VALIDATION':
        return await testDatabaseValidation(supabase, user);
      
      case 'COMPREHENSIVE_REAL_TEST':
        return await testComprehensiveRealContent(supabase, user, content, mediaFiles, platforms);
      
      default:
        return createErrorResponse('نوع اختبار غير صحيح', 400, 'Invalid test type');
    }

  } catch (error) {
    console.error('❌ Real content testing error:', error);
    return createErrorResponse('خطأ في اختبار المحتوى الحقيقي', 500, error);
  }
}

// Test Arabic RTL text rendering in Instagram posts
async function testArabicRTLContent(supabase: any, user: any, content: string, mediaFiles: string[]) {
  console.log('🔤 Testing Arabic RTL content rendering...');
  
  const arabicTestContent = content || `
🌟 اختبار المحتوى العربي الحقيقي 🌟

مرحباً بكم في منصة إي وصل للتواصل الاجتماعي! 

✨ المميزات الجديدة:
• نشر فوري على انستغرام
• دعم كامل للغة العربية
• واجهة من اليمين إلى اليسار
• تحميل الصور والفيديوهات
• جدولة المنشورات

🚀 تجربة سلسة ومتطورة لإدارة حساباتك الاجتماعية

#إي_وصل #التواصل_الاجتماعي #انستغرام #العربية
#eWasl #SocialMedia #Instagram #Arabic

تم النشر: ${new Date().toLocaleString('ar-SA', { timeZone: 'Asia/Riyadh' })}
  `.trim();

  // Test with real Instagram publishing
  const testPayload = {
    content: arabicTestContent,
    media_urls: mediaFiles || ['https://images.unsplash.com/photo-*************-80b023f02d71?w=800&h=600&fit=crop'], // Arabic calligraphy image
    status: 'PUBLISHED',
    social_account_ids: ['9b99c9ad-e11f-4f9a-8f19-51f5d696563c'],
    timezone: 'Asia/Riyadh'
  };

  try {
    // Call the main posts endpoint directly (internal call)
    const postsModule = await import('../posts/route');
    const mockRequest = new Request(`${process.env.NEXT_PUBLIC_APP_URL}/api/posts`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testPayload)
    });

    const result = await postsModule.POST(mockRequest as NextRequest);
    const responseData = await result.json();

    return createAuthenticatedResponse({
      testType: 'ARABIC_RTL_TEXT',
      success: result.ok,
      status: result.status,
      result: responseData,
      analysis: {
        arabicContentLength: arabicTestContent.length,
        hasArabicCharacters: /[\u0600-\u06FF]/.test(arabicTestContent),
        hasRTLMarkers: arabicTestContent.includes('‏') || arabicTestContent.includes('‎'),
        publishingSuccess: responseData.data?.publishing_results?.[0]?.success || false,
        instagramPostUrl: responseData.data?.publishing_results?.[0]?.postUrl || null
      }
    });

  } catch (error) {
    console.error('❌ Arabic RTL test failed:', error);
    return createErrorResponse('فشل في اختبار المحتوى العربي', 500, error);
  }
}

// Test Supabase Storage media upload and publishing
async function testSupabaseStorageMedia(supabase: any, user: any, content: string, mediaFiles: string[]) {
  console.log('📁 Testing Supabase Storage media integration...');
  
  // Check if media files are from Supabase Storage
  const supabaseStorageFiles = mediaFiles?.filter(url => 
    url.includes('supabase') || url.includes('storage')
  ) || [];

  if (supabaseStorageFiles.length === 0) {
    return createErrorResponse(
      'لا توجد ملفات من تخزين Supabase للاختبار',
      400,
      'No Supabase Storage files provided for testing'
    );
  }

  const testContent = content || `
📁 اختبار تحميل الوسائط من تخزين Supabase

🔧 اختبار التكامل مع:
• تخزين Supabase للملفات
• رفع الصور والفيديوهات
• النشر المباشر على انستغرام
• معالجة أنواع الملفات المختلفة

✅ جودة عالية وأداء ممتاز

#supabase_storage #media_upload #ewasl_platform

تم الاختبار: ${new Date().toLocaleString('ar-SA')}
  `.trim();

  const testPayload = {
    content: testContent,
    media_urls: supabaseStorageFiles,
    status: 'PUBLISHED',
    social_account_ids: ['9b99c9ad-e11f-4f9a-8f19-51f5d696563c'],
    timezone: 'Asia/Riyadh'
  };

  try {
    const postsModule = await import('../posts/route');
    const mockRequest = new Request(`${process.env.NEXT_PUBLIC_APP_URL}/api/posts`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testPayload)
    });

    const result = await postsModule.POST(mockRequest as NextRequest);
    const responseData = await result.json();

    return createAuthenticatedResponse({
      testType: 'SUPABASE_STORAGE_MEDIA',
      success: result.ok,
      status: result.status,
      result: responseData,
      analysis: {
        supabaseFilesCount: supabaseStorageFiles.length,
        supabaseFiles: supabaseStorageFiles,
        publishingSuccess: responseData.data?.publishing_results?.[0]?.success || false,
        instagramPostUrl: responseData.data?.publishing_results?.[0]?.postUrl || null,
        mediaProcessingTime: responseData.responseTime || 0
      }
    });

  } catch (error) {
    console.error('❌ Supabase Storage media test failed:', error);
    return createErrorResponse('فشل في اختبار وسائط Supabase', 500, error);
  }
}

// Test various media formats (JPEG, PNG) and sizes
async function testVariousMediaFormats(supabase: any, user: any, content: string, mediaFiles: string[]) {
  console.log('🖼️ Testing various media formats and sizes...');
  
  // Predefined test media URLs with different formats and sizes
  const testMediaUrls = mediaFiles || [
    'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=600&fit=crop&fm=jpg', // JPEG
    'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=1080&h=1080&fit=crop&fm=png', // PNG Square
    'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=1200&h=800&fit=crop&fm=webp', // WebP Landscape
  ];

  const testContent = content || `
🖼️ اختبار أنواع وأحجام الوسائط المختلفة

📊 تنسيقات مختبرة:
• JPEG - جودة عالية
• PNG - شفافية ووضوح
• WebP - ضغط متقدم
• أحجام متنوعة (مربع، أفقي، عمودي)

✅ توافق كامل مع Instagram Graph API

#media_formats #image_testing #ewasl_quality

تم الاختبار: ${new Date().toLocaleString('ar-SA')}
  `.trim();

  // Test each media format separately
  const formatTests = [];

  for (let i = 0; i < Math.min(testMediaUrls.length, 2); i++) { // Limit to 2 tests to avoid rate limiting
    const mediaUrl = testMediaUrls[i];
    const format = mediaUrl.includes('fm=jpg') ? 'JPEG' : 
                  mediaUrl.includes('fm=png') ? 'PNG' : 
                  mediaUrl.includes('fm=webp') ? 'WebP' : 'Unknown';
    
    const testPayload = {
      content: `${testContent}\n\n🔍 اختبار تنسيق: ${format}`,
      media_urls: [mediaUrl],
      status: 'PUBLISHED',
      social_account_ids: ['9b99c9ad-e11f-4f9a-8f19-51f5d696563c'],
      timezone: 'Asia/Riyadh'
    };

    try {
      const postsModule = await import('../posts/route');
      const mockRequest = new Request(`${process.env.NEXT_PUBLIC_APP_URL}/api/posts`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(testPayload)
      });

      const result = await postsModule.POST(mockRequest as NextRequest);
      const responseData = await result.json();

      formatTests.push({
        format,
        mediaUrl,
        success: result.ok,
        status: result.status,
        publishingSuccess: responseData.data?.publishing_results?.[0]?.success || false,
        instagramPostUrl: responseData.data?.publishing_results?.[0]?.postUrl || null,
        error: responseData.error || null
      });

      // Wait between tests to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 3000));

    } catch (error) {
      formatTests.push({
        format,
        mediaUrl,
        success: false,
        error: error.message
      });
    }
  }

  return createAuthenticatedResponse({
    testType: 'VARIOUS_MEDIA_FORMATS',
    formatTests,
    analysis: {
      totalFormats: testMediaUrls.length,
      successfulFormats: formatTests.filter(t => t.success).length,
      failedFormats: formatTests.filter(t => !t.success).length,
      supportedFormats: formatTests.filter(t => t.publishingSuccess).map(t => t.format),
      unsupportedFormats: formatTests.filter(t => !t.publishingSuccess).map(t => t.format)
    }
  });
}

// Test database validation - verify all tables are properly updated
async function testDatabaseValidation(supabase: any, user: any) {
  console.log('🗄️ Testing database validation...');
  
  try {
    // Check recent posts
    const { data: recentPosts, error: postsError } = await supabase
      .from('posts')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })
      .limit(5);

    // Check post_social_accounts relationships
    const { data: postSocialAccounts, error: psaError } = await supabase
      .from('post_social_accounts')
      .select('*')
      .in('post_id', recentPosts?.map(p => p.id) || [])
      .order('created_at', { ascending: false });

    // Check publishing_results if table exists
    const { data: publishingResults, error: prError } = await supabase
      .from('publishing_results')
      .select('*')
      .in('post_id', recentPosts?.map(p => p.id) || [])
      .order('created_at', { ascending: false });

    // Check scheduled_posts_queue
    const { data: scheduledPosts, error: spqError } = await supabase
      .from('scheduled_posts_queue')
      .select('*')
      .in('post_id', recentPosts?.map(p => p.id) || [])
      .order('created_at', { ascending: false });

    return createAuthenticatedResponse({
      testType: 'DATABASE_VALIDATION',
      success: !postsError && !psaError,
      tables: {
        posts: {
          count: recentPosts?.length || 0,
          error: postsError,
          sample: recentPosts?.[0] || null
        },
        post_social_accounts: {
          count: postSocialAccounts?.length || 0,
          error: psaError,
          sample: postSocialAccounts?.[0] || null
        },
        publishing_results: {
          count: publishingResults?.length || 0,
          error: prError,
          sample: publishingResults?.[0] || null
        },
        scheduled_posts_queue: {
          count: scheduledPosts?.length || 0,
          error: spqError,
          sample: scheduledPosts?.[0] || null
        }
      },
      analysis: {
        allTablesAccessible: !postsError && !psaError && !spqError,
        publishingResultsTableExists: !prError,
        recentPostsWithInstagramData: postSocialAccounts?.filter(psa => 
          psa.platform === 'INSTAGRAM' && psa.platform_post_id
        ).length || 0
      }
    });

  } catch (error) {
    console.error('❌ Database validation test failed:', error);
    return createErrorResponse('فشل في اختبار قاعدة البيانات', 500, error);
  }
}

// Comprehensive real content test combining all aspects
async function testComprehensiveRealContent(supabase: any, user: any, content: string, mediaFiles: string[], platforms: string[]) {
  console.log('🎯 Running comprehensive real content test...');
  
  const comprehensiveContent = content || `
🎯 اختبار شامل للمحتوى الحقيقي - منصة إي وصل

🌟 اختبار جميع المميزات:
✅ المحتوى العربي مع دعم RTL
✅ تحميل الوسائط عالية الجودة  
✅ النشر الفوري على انستغرام
✅ تحديث قاعدة البيانات
✅ معالجة الأخطاء باللغة العربية

🚀 النتائج المتوقعة:
• نشر ناجح على Instagram
• حفظ البيانات في جميع الجداول
• عرض صحيح للنص العربي
• روابط صالحة للمنشورات

📊 إحصائيات الاختبار:
- وقت الاستجابة: < 15 ثانية
- معدل النجاح: 100%
- دعم التنسيقات: JPEG, PNG, WebP
- المنصات: Instagram (مع إمكانية التوسع)

#comprehensive_test #real_content #ewasl_platform #instagram_publishing #arabic_support

🕐 تم الاختبار: ${new Date().toLocaleString('ar-SA', { timeZone: 'Asia/Riyadh' })}
📍 المنطقة الزمنية: آسيا/الرياض
🆔 معرف الاختبار: COMPREHENSIVE-${Date.now()}
  `.trim();

  const testPayload = {
    content: comprehensiveContent,
    media_urls: mediaFiles || ['https://images.unsplash.com/photo-*************-80b023f02d71?w=1080&h=1080&fit=crop'], // High-quality Arabic calligraphy
    status: 'PUBLISHED',
    social_account_ids: ['9b99c9ad-e11f-4f9a-8f19-51f5d696563c'],
    timezone: 'Asia/Riyadh'
  };

  try {
    const startTime = Date.now();

    const postsModule = await import('../posts/route');
    const mockRequest = new Request(`${process.env.NEXT_PUBLIC_APP_URL}/api/posts`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testPayload)
    });

    const result = await postsModule.POST(mockRequest as NextRequest);
    const endTime = Date.now();
    const responseTime = endTime - startTime;
    const responseData = await result.json();

    // Validate database updates
    const dbValidation = await testDatabaseValidation(supabase, user);

    return createAuthenticatedResponse({
      testType: 'COMPREHENSIVE_REAL_TEST',
      success: result.ok,
      status: result.status,
      responseTime,
      result: responseData,
      databaseValidation: dbValidation.data,
      analysis: {
        publishingSuccess: responseData.data?.publishing_results?.[0]?.success || false,
        instagramPostUrl: responseData.data?.publishing_results?.[0]?.postUrl || null,
        instagramPostId: responseData.data?.publishing_results?.[0]?.postId || null,
        arabicContentLength: comprehensiveContent.length,
        hasArabicCharacters: /[\u0600-\u06FF]/.test(comprehensiveContent),
        responseTimeAcceptable: responseTime < 15000, // Under 15 seconds
        databaseTablesUpdated: dbValidation.data?.analysis?.allTablesAccessible || false,
        overallSuccess: result.ok && (responseData.data?.publishing_results?.[0]?.success || false),
        phase2b1Validation: {
          instagramPublishing: responseData.data?.publishing_results?.[0]?.success || false,
          arabicRTLSupport: /[\u0600-\u06FF]/.test(comprehensiveContent),
          databaseIntegration: dbValidation.data?.analysis?.allTablesAccessible || false,
          errorHandling: !result.ok ? 'Arabic error messages working' : 'No errors to test',
          productionReady: result.ok && (responseData.data?.publishing_results?.[0]?.success || false)
        }
      }
    });

  } catch (error) {
    console.error('❌ Comprehensive real content test failed:', error);
    return createErrorResponse('فشل في الاختبار الشامل للمحتوى الحقيقي', 500, error);
  }
}
