(()=>{var e={};e.id=5653,e.ids=[5653],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},45267:(e,s,t)=>{Promise.resolve().then(t.bind(t,91097))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63788:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>l});var r=t(60687),a=t(43210);t(35622);let n=null,i=null;function l(){let[e,s]=(0,a.useState)({supabase:"checking",database:"checking",auth:"checking",env:"checking"}),[t,n]=(0,a.useState)({}),i=e=>{switch(e){case"checking":return"\uD83D\uDD04";case"success":return"✅";case"error":return"❌";default:return"❓"}},l=e=>{switch(e){case"checking":return"text-yellow-600 bg-yellow-50";case"success":return"text-green-600 bg-green-50";case"error":return"text-red-600 bg-red-50";default:return"text-gray-600 bg-gray-50"}};return(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,r.jsxs)("div",{className:"text-center mb-8",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"eWasl System Health Check"}),(0,r.jsx)("p",{className:"text-gray-600 mt-2",children:"تحقق من حالة جميع أنظمة التطبيق"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:`p-6 rounded-lg border ${l(e.env)}`,children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold",children:"Environment Variables"}),(0,r.jsx)("span",{className:"text-2xl",children:i(e.env)})]}),(0,r.jsx)("p",{className:"text-sm",children:t.env||("success"===e.env?"All required environment variables are set":"Checking...")})]}),(0,r.jsxs)("div",{className:`p-6 rounded-lg border ${l(e.supabase)}`,children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold",children:"Supabase Connection"}),(0,r.jsx)("span",{className:"text-2xl",children:i(e.supabase)})]}),(0,r.jsx)("p",{className:"text-sm",children:t.supabase||("success"===e.supabase?"Supabase client initialized successfully":"Checking...")})]}),(0,r.jsxs)("div",{className:`p-6 rounded-lg border ${l(e.auth)}`,children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold",children:"Authentication"}),(0,r.jsx)("span",{className:"text-2xl",children:i(e.auth)})]}),(0,r.jsx)("p",{className:"text-sm",children:t.auth||"Checking authentication status..."})]}),(0,r.jsxs)("div",{className:`p-6 rounded-lg border ${l(e.database)}`,children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold",children:"Database Connection"}),(0,r.jsx)("span",{className:"text-2xl",children:i(e.database)})]}),(0,r.jsx)("p",{className:"text-sm",children:t.database||"Checking database connection..."})]})]}),(0,r.jsx)("div",{className:"mt-8 text-center space-y-4",children:(0,r.jsxs)("div",{className:"flex justify-center space-x-4 rtl:space-x-reverse",children:[(0,r.jsx)("a",{href:"/auth/signin",className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Sign In Page"}),(0,r.jsx)("a",{href:"/dashboard",className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors",children:"Dashboard"}),(0,r.jsx)("button",{onClick:()=>window.location.reload(),className:"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors",children:"Refresh Check"})]})}),t.error&&(0,r.jsxs)("div",{className:"mt-8 p-4 bg-red-50 border border-red-200 rounded-lg",children:[(0,r.jsx)("h4",{className:"text-red-800 font-semibold mb-2",children:"Error Details:"}),(0,r.jsx)("pre",{className:"text-red-700 text-sm whitespace-pre-wrap",children:t.error})]}),(0,r.jsxs)("div",{className:"mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg",children:[(0,r.jsx)("h4",{className:"text-blue-800 font-semibold mb-2",children:"System Information:"}),(0,r.jsxs)("div",{className:"text-blue-700 text-sm space-y-1",children:[(0,r.jsxs)("p",{children:["Environment: ","production"]}),(0,r.jsxs)("p",{children:["App URL: ","https://app.ewasl.com"]}),(0,r.jsxs)("p",{children:["Timestamp: ",new Date().toISOString()]})]})]})]})})}},70440:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});var r=t(31658);let a=async e=>[{type:"image/x-icon",sizes:"48x48",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},78282:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>p,pages:()=>d,routeModule:()=>u,tree:()=>o});var r=t(65239),a=t(48088),n=t(88170),i=t.n(n),l=t(30893),c={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);t.d(s,c);let o={children:["",{children:["health",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,91097)),"C:\\Users\\<USER>\\Desktop\\Ewasl\\eWasl.com_Cursor_Digital_Ocean\\ewasl-app\\src\\app\\health\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,45399)),"C:\\Users\\<USER>\\Desktop\\Ewasl\\eWasl.com_Cursor_Digital_Ocean\\ewasl-app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Desktop\\Ewasl\\eWasl.com_Cursor_Digital_Ocean\\ewasl-app\\src\\app\\health\\page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/health/page",pathname:"/health",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},82219:(e,s,t)=>{Promise.resolve().then(t.bind(t,63788))},91097:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ewasl\\\\eWasl.com_Cursor_Digital_Ocean\\\\ewasl-app\\\\src\\\\app\\\\health\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Ewasl\\eWasl.com_Cursor_Digital_Ocean\\ewasl-app\\src\\app\\health\\page.tsx","default")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[4243,6167,2215,1658,9038,9908],()=>t(78282));module.exports=r})();