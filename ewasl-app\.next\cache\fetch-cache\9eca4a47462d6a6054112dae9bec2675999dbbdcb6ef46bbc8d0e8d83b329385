{"kind": "FETCH", "data": {"headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "95e05a3ccef5e323-IST", "connection": "keep-alive", "content-encoding": "gzip", "content-location": "/scheduled_posts_queue?limit=10&order=scheduled_for.asc&scheduled_for=lte.2025-07-12T11%3A55%3A34.079Z&select=%2A&status=eq.pending", "content-profile": "public", "content-range": "*/*", "content-type": "application/json; charset=utf-8", "date": "Sat, 12 Jul 2025 11:55:35 GMT", "sb-gateway-version": "1", "sb-project-ref": "nnxfzhxqzmriggulsudr", "server": "cloudflare", "set-cookie": "__cf_bm=TrheJP_attvKw3cTUkPiVwstEhEbct_X4QgDA2Srkaw-1752321335-*******-FZ13oBpWmNVaI_FTOPbHW8nTfIKFyXi5hBAu4J_gNUdcwD9X1Z6oWQQsfQbUzbm8HUWsf91mDdzbAIVQ2_958CekC5r4XsjV4O4fKIlgi0Y; path=/; expires=Sat, 12-Jul-25 12:25:35 GMT; domain=.supabase.co; HttpOnly; Secure; SameSite=None", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "transfer-encoding": "chunked", "vary": "Accept-Encoding", "x-content-type-options": "nosniff", "x-envoy-attempt-count": "1", "x-envoy-upstream-service-time": "1"}, "body": "W10=", "status": 200, "url": "https://nnxfzhxqzmriggulsudr.supabase.co/rest/v1/scheduled_posts_queue?select=*&status=eq.pending&scheduled_for=lte.2025-07-12T11%3A55%3A34.079Z&order=scheduled_for.asc&limit=10"}, "revalidate": 31536000, "tags": []}