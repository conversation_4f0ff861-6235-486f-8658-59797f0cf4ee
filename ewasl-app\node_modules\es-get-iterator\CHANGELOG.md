# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

Generated by [`auto-changelog`](https://github.com/CookPete/auto-changelog).

## [v1.1.3](https://github.com/ljharb/es-get-iterator/compare/v1.1.2...v1.1.3) - 2023-01-12

### Commits

- [actions] reuse common workflows [`c97cb76`](https://github.com/ljharb/es-get-iterator/commit/c97cb764624f8c0e263695f1dcc9351b11000ea4)
- [actions] use `node/install` instead of `node/run`; use `codecov` action [`6d09911`](https://github.com/ljharb/es-get-iterator/commit/6d09911097b54f59e6b3f3961f57dc594b3c649a)
- [meta] use `npmignore` to autogenerate an npmignore file [`c7e0e85`](https://github.com/ljharb/es-get-iterator/commit/c7e0e85212a756b0989f8ff24896f2a936a3fe20)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog`, `es5-shim`, `object-inspect`, `tape` [`1353190`](https://github.com/ljharb/es-get-iterator/commit/13531904d91ee41ea22f02fd2bafd3034fba3758)
- [Refactor] extract logic to `stop-iteration-iterator` [`ab19956`](https://github.com/ljharb/es-get-iterator/commit/ab199561031139e4d5c8249cda77196ff2590aaf)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog`, `es5-shim`, `object-inspect`, `safe-publish-latest`, `tape` [`de2ae73`](https://github.com/ljharb/es-get-iterator/commit/de2ae73a1c4395f4459450c11cd146fb73bee90c)
- [Tests] start testing more variants [`e059f33`](https://github.com/ljharb/es-get-iterator/commit/e059f33c5ab89043d731a3ea7c301301ed1b315b)
- [actions] update codecov uploader [`c8ffcec`](https://github.com/ljharb/es-get-iterator/commit/c8ffcec4ff8bfbab82e039f43d3283a345e7c94c)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `es5-shim`, `has-bigints`, `object-inspect`, `tape` [`8cd2e87`](https://github.com/ljharb/es-get-iterator/commit/8cd2e8716d5b175c5f90cce3999e5c0de3b5be69)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `object-inspect`, `tape` [`7676030`](https://github.com/ljharb/es-get-iterator/commit/7676030b4aa2d41cb3579c1aaea55911a62ca9ee)
- [actions] update checkout action [`bdbe6c9`](https://github.com/ljharb/es-get-iterator/commit/bdbe6c9664eae9c87fa98128419b2d086a40988f)
- [Dev Deps] update `@ljharb/eslint-config`, `aud`, `es6-shim` [`67cddd6`](https://github.com/ljharb/es-get-iterator/commit/67cddd66e4d9ad51fb9142ff3b1871b1b2fb1cf9)
- [Tests] fix debug output issues in FF 24 [`a726fdc`](https://github.com/ljharb/es-get-iterator/commit/a726fdce1defeb2e0fec0dcc7a645668d574a1ac)
- [Deps] update `has-symbols`, `is-arguments`, `is-string` [`044907b`](https://github.com/ljharb/es-get-iterator/commit/044907b42a2c1950855e9a2d1f455ba3595b2980)
- [Deps] update `get-intrinsic`, `has-symbols` [`e492f8f`](https://github.com/ljharb/es-get-iterator/commit/e492f8f3a1a1d47ed032303bcfebb5d75b756267)
- [meta] use `prepublishOnly` script for npm 7+ [`eccda6b`](https://github.com/ljharb/es-get-iterator/commit/eccda6bbfd20ed1c2ec1ad5c92c02169b50608e6)
- [Dev Deps] update `object-inspect` [`c24dfa5`](https://github.com/ljharb/es-get-iterator/commit/c24dfa542267132515128172955a1d4a4049c14e)
- [Deps] update `get-intrinsic` [`1bd68ce`](https://github.com/ljharb/es-get-iterator/commit/1bd68ceb11bc078edafb80a50631149056e8ffdf)

## [v1.1.2](https://github.com/ljharb/es-get-iterator/compare/v1.1.1...v1.1.2) - 2021-01-26

### Commits

- [meta] npmignore github action workflows [`0cd2f21`](https://github.com/ljharb/es-get-iterator/commit/0cd2f218f16b08efccbc29daf3831f4f37e30a74)
- [readme] remove travis badge [`357065b`](https://github.com/ljharb/es-get-iterator/commit/357065b649cca3122cc32c73ef97739e3ab6cf6c)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `has-bigints`, `object-inspect`, `tape` [`13a77f2`](https://github.com/ljharb/es-get-iterator/commit/13a77f279cda7ddffbb769ea57933ffc3cac62f0)
- [Deps] update `get-intrinsic`, `is-arguments`, `is-map`, `is-set` [`5f8d7f1`](https://github.com/ljharb/es-get-iterator/commit/5f8d7f14c71bffd470bb61f6f0e125da41bfcf06)
- [meta] update actions, dotfiles [`5ea3e50`](https://github.com/ljharb/es-get-iterator/commit/5ea3e506d0ca1d80df6b37836c62e85934804f89)
- [Tests] fix ESM test matrix [`9ab614c`](https://github.com/ljharb/es-get-iterator/commit/9ab614ce13b1a210d18827e47d4ad631a431dd39)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `es5-shim` [`e843ad9`](https://github.com/ljharb/es-get-iterator/commit/e843ad96802c3579a79b82ef49d98239f8288db8)
- [Deps] update `call-bind`, `get-intrinsic` [`4301b3e`](https://github.com/ljharb/es-get-iterator/commit/4301b3e70982434feda67e5868d7a50f5101ae8f)
- [meta] avoid upcoming deprecation warning in node; add "browser" field [`57297b1`](https://github.com/ljharb/es-get-iterator/commit/57297b19b54b0970fe986890be6c7a97fa4fdd3a)
- [Tests] skip `npm ls` in node 0.x tests [`1409196`](https://github.com/ljharb/es-get-iterator/commit/1409196062de66d84d3cf1d368bed18488e767f2)
- [Dev Deps] update `eslint` [`e4dcea4`](https://github.com/ljharb/es-get-iterator/commit/e4dcea49104de45a0bcf861f9aa2923f0209ed66)

## [v1.1.1](https://github.com/ljharb/es-get-iterator/compare/v1.1.0...v1.1.1) - 2020-11-06

### Commits

- [Tests] migrate tests to Github Actions [`e10fd31`](https://github.com/ljharb/es-get-iterator/commit/e10fd31909fc6451e4be5d8d9fb031d04ab72267)
- [Fix] Support iterators defined by es6-shim when loaded after es-get-iterator [`f2ef7e1`](https://github.com/ljharb/es-get-iterator/commit/f2ef7e1d1cf5fa3357e460fc0023eaf11e79b573)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog`, `es6-shim`, `object-inspect`, `tape` [`1ee86fc`](https://github.com/ljharb/es-get-iterator/commit/1ee86fcf0ff0fa115e78ef589d3a76cd299fe89e)
- [actions] add "Allow Edits" workflow [`c785c69`](https://github.com/ljharb/es-get-iterator/commit/c785c69933afd98a670250f1d52e3b514cbd1d7a)
- [Refactor] use `get-intrinsic` and `call-bind` instead of `es-abstract` [`65f4ef5`](https://github.com/ljharb/es-get-iterator/commit/65f4ef5018688432ca87a4b5aa971fee182722df)
- [Dev Deps] update `auto-changelog`, `es5-shim`, `tape`; add `aud` [`91301ed`](https://github.com/ljharb/es-get-iterator/commit/91301edd87d6b753e0129ac7007e39d410030340)
- [Dev Deps] update `aud` [`afc91d9`](https://github.com/ljharb/es-get-iterator/commit/afc91d98ae243c8563ac7295b8775c5a4b37c92f)
- [actions] switch Automatic Rebase workflow to `pull_request_target` event [`1962743`](https://github.com/ljharb/es-get-iterator/commit/19627437efac78d71d78d5e2ef0192052598bc1b)
- [Deps] update `es-abstract` [`d2b57c8`](https://github.com/ljharb/es-get-iterator/commit/d2b57c8896b22eb90b0b894d80ba34f69ed68c3d)

## [v1.1.0](https://github.com/ljharb/es-get-iterator/compare/v1.0.2...v1.1.0) - 2020-01-25

### Commits

- [New] add native ESM variant via conditional exports [`325629d`](https://github.com/ljharb/es-get-iterator/commit/325629d43b6b8d4f2f5ff7d6623e81e01080dde7)
- [Tests] fix test matrix [`01c20cf`](https://github.com/ljharb/es-get-iterator/commit/01c20cf6ed810e567f5fba5c29425df7f2aceb7a)
- [Docs] Add modern browser example for Rollup [`ab9f17d`](https://github.com/ljharb/es-get-iterator/commit/ab9f17da94542940086280d8792d4e6c71186b47)
- [Deps] update `is-map`, `is-set`, `es-abstract`, `is-string` [`a1b9645`](https://github.com/ljharb/es-get-iterator/commit/a1b964517cbd5b16a34fb15df50ec48d684c34c1)
- [Fix] `node.js` only runs where "exports" is supported, and arguments is iterable there [`ccc7646`](https://github.com/ljharb/es-get-iterator/commit/ccc76469077f2fbc82fd4647894ebd660d21a2cb)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `tape` [`4281453`](https://github.com/ljharb/es-get-iterator/commit/42814531965adb169abb3186a78c0926d4146232)
- [Dev Deps] update `@ljharb/eslint-config` [`f4fc99c`](https://github.com/ljharb/es-get-iterator/commit/f4fc99c83935d0c03aade04030f103d5328abf15)
- [Deps] update `es-abstract` [`70b0423`](https://github.com/ljharb/es-get-iterator/commit/70b042317239eb79df71b16a9531900bdad812f4)
- [Tests] add string coverage for a lone surrogate not followed by a proper surrogate ending [`796e497`](https://github.com/ljharb/es-get-iterator/commit/796e4979168b6ee8ec323d54ca157296166e36d0)

## [v1.0.2](https://github.com/ljharb/es-get-iterator/compare/v1.0.1...v1.0.2) - 2019-12-16

### Commits

- [Deps] update `es-abstract` [`1554229`](https://github.com/ljharb/es-get-iterator/commit/15542291b91d82ccf9da063d1350e7fe685f6bcd)
- [Dev Deps] update `eslint` [`577bbb1`](https://github.com/ljharb/es-get-iterator/commit/577bbb136f7c44cc2d774b0360061b1f1bb10f30)

## [v1.0.1](https://github.com/ljharb/es-get-iterator/compare/v1.0.0...v1.0.1) - 2019-11-27

### Commits

- [Fix] fix bugs in pre-Symbol environments [`592f78a`](https://github.com/ljharb/es-get-iterator/commit/592f78a1d38a0e3e3c4c3dafe1552899decd8c34)

## v1.0.0 - 2019-11-25

### Commits

- Initial tests. [`71f5fdd`](https://github.com/ljharb/es-get-iterator/commit/71f5fdd9c1fdd7b34b5c6f4e1a14cb0cbffc0d9c)
- Initial implementation [`d7e0480`](https://github.com/ljharb/es-get-iterator/commit/d7e04808b322fb6648f4890d86df7f3384b53421)
- Initial commit [`eb5372c`](https://github.com/ljharb/es-get-iterator/commit/eb5372c438b3ca4136e8253ffc4cc7834a4c8ca8)
- readme [`8d6ad14`](https://github.com/ljharb/es-get-iterator/commit/8d6ad14a7f17339ccc20143562f0618773aba3b8)
- npm init [`9b84446`](https://github.com/ljharb/es-get-iterator/commit/9b84446a4e346d4e12c59da5f2f928e1f71d3d69)
- [meta] add `auto-changelog` [`e2d2e4f`](https://github.com/ljharb/es-get-iterator/commit/e2d2e4f55245b786581ef5d42d03cd0efb62db12)
- [meta] add `funding` field; create FUNDING.yml [`5a31c77`](https://github.com/ljharb/es-get-iterator/commit/5a31c7722fc54edfe56975f5a4b7414c48136d36)
- [actions] add automatic rebasing / merge commit blocking [`644429e`](https://github.com/ljharb/es-get-iterator/commit/644429e791abc1b85b65c90d0ee4aac57416ee90)
- [Tests] add `npm run lint` [`f22172f`](https://github.com/ljharb/es-get-iterator/commit/f22172f2dcdd6f41ca45862698b8ea496134b164)
- Only apps should have lockfiles [`fcf8441`](https://github.com/ljharb/es-get-iterator/commit/fcf8441df29d902647fd87d14224c7af19e40c31)
- [meta] add `safe-publish-latest` [`946befa`](https://github.com/ljharb/es-get-iterator/commit/946befa7eb4a91ca648b98660b086ed7813cd3b1)
- [Tests] only test on majors, since travis has a 200 build limit [`aeb5f09`](https://github.com/ljharb/es-get-iterator/commit/aeb5f09a66957c2cff0af22cb1a731ecafb82f24)
