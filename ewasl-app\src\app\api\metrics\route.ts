import { NextRequest, NextResponse } from 'next/server';
import { createClient, createServiceRoleClient } from '@/lib/supabase/server';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

/**
 * Metrics Collection Endpoint
 * Provides system performance and usage metrics
 * 
 * GET /api/metrics
 * Query params: ?period=24h|7d|30d&detailed=true
 */
export async function GET(request: NextRequest) {
  try {
    console.log('📊 Metrics collection requested');

    // Get authenticated user (optional for system metrics)
    const supabase = createClient();
    const { data: { user } } = await supabase.auth.getUser();

    const { searchParams } = new URL(request.url);
    const period = searchParams.get('period') || '24h';
    const detailed = searchParams.get('detailed') === 'true';
    const userId = searchParams.get('userId') || user?.id;

    console.log('📊 Metrics request:', { period, detailed, userId: !!userId });

    // Create service role client for database operations
    const supabaseService = createServiceRoleClient();

    // Calculate time range
    const now = new Date();
    const timeRange = getTimeRange(period);
    const startTime = new Date(now.getTime() - timeRange);

    console.log('📊 Time range:', { startTime: startTime.toISOString(), endTime: now.toISOString() });

    const metrics = {
      timestamp: now.toISOString(),
      period,
      timeRange: {
        start: startTime.toISOString(),
        end: now.toISOString()
      },
      system: await getSystemMetrics(supabaseService, startTime, now),
      posts: await getPostMetrics(supabaseService, startTime, now, userId),
      publishing: await getPublishingMetrics(supabaseService, startTime, now, userId),
      users: await getUserMetrics(supabaseService, startTime, now),
      performance: await getPerformanceMetrics(supabaseService, startTime, now)
    };

    if (detailed) {
      metrics.detailed = {
        platforms: await getPlatformMetrics(supabaseService, startTime, now, userId),
        errors: await getErrorMetrics(supabaseService, startTime, now),
        queue: await getQueueMetrics(supabaseService, startTime, now)
      };
    }

    console.log('📊 Metrics collected successfully');

    return NextResponse.json(metrics, {
      headers: {
        'Cache-Control': 'public, max-age=300', // Cache for 5 minutes
        'Content-Type': 'application/json'
      }
    });

  } catch (error) {
    console.error('❌ Error collecting metrics:', error);
    return NextResponse.json(
      { 
        error: 'فشل في جمع المقاييس',
        message: 'Failed to collect metrics',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * Get time range in milliseconds
 */
function getTimeRange(period: string): number {
  switch (period) {
    case '1h': return 60 * 60 * 1000;
    case '24h': return 24 * 60 * 60 * 1000;
    case '7d': return 7 * 24 * 60 * 60 * 1000;
    case '30d': return 30 * 24 * 60 * 60 * 1000;
    default: return 24 * 60 * 60 * 1000;
  }
}

/**
 * Get system-wide metrics
 */
async function getSystemMetrics(supabase: any, startTime: Date, endTime: Date) {
  try {
    // Get total counts
    const [postsResult, usersResult, accountsResult] = await Promise.all([
      supabase.from('posts').select('id', { count: 'exact' }),
      supabase.from('users').select('id', { count: 'exact' }),
      supabase.from('social_accounts').select('id', { count: 'exact' }).eq('is_active', true)
    ]);

    return {
      totalPosts: postsResult.count || 0,
      totalUsers: usersResult.count || 0,
      totalSocialAccounts: accountsResult.count || 0,
      uptime: Math.floor(process.uptime()),
      memoryUsage: process.memoryUsage(),
      nodeVersion: process.version
    };
  } catch (error) {
    console.error('Error getting system metrics:', error);
    return {
      totalPosts: 0,
      totalUsers: 0,
      totalSocialAccounts: 0,
      uptime: 0,
      error: 'Failed to collect system metrics'
    };
  }
}

/**
 * Get post creation metrics
 */
async function getPostMetrics(supabase: any, startTime: Date, endTime: Date, userId?: string) {
  try {
    let query = supabase
      .from('posts')
      .select('id, status, created_at')
      .gte('created_at', startTime.toISOString())
      .lte('created_at', endTime.toISOString());

    if (userId) {
      query = query.eq('user_id', userId);
    }

    const { data: posts, error } = await query;

    if (error) throw error;

    const totalPosts = posts?.length || 0;
    const publishedPosts = posts?.filter(p => p.status === 'PUBLISHED').length || 0;
    const scheduledPosts = posts?.filter(p => p.status === 'SCHEDULED').length || 0;
    const draftPosts = posts?.filter(p => p.status === 'DRAFT').length || 0;

    return {
      total: totalPosts,
      published: publishedPosts,
      scheduled: scheduledPosts,
      drafts: draftPosts,
      publishRate: totalPosts > 0 ? (publishedPosts / totalPosts * 100).toFixed(2) : '0.00'
    };
  } catch (error) {
    console.error('Error getting post metrics:', error);
    return {
      total: 0,
      published: 0,
      scheduled: 0,
      drafts: 0,
      publishRate: '0.00',
      error: 'Failed to collect post metrics'
    };
  }
}

/**
 * Get publishing success metrics
 */
async function getPublishingMetrics(supabase: any, startTime: Date, endTime: Date, userId?: string) {
  try {
    let query = supabase
      .from('post_social_accounts')
      .select('id, status, platform, published_at, error_message')
      .gte('created_at', startTime.toISOString())
      .lte('created_at', endTime.toISOString());

    if (userId) {
      // Join with posts to filter by user
      query = supabase
        .from('post_social_accounts')
        .select(`
          id, status, platform, published_at, error_message,
          posts!inner(user_id)
        `)
        .eq('posts.user_id', userId)
        .gte('created_at', startTime.toISOString())
        .lte('created_at', endTime.toISOString());
    }

    const { data: publishingAttempts, error } = await query;

    if (error) throw error;

    const total = publishingAttempts?.length || 0;
    const successful = publishingAttempts?.filter(p => p.status === 'published').length || 0;
    const failed = publishingAttempts?.filter(p => p.status === 'failed').length || 0;
    const pending = publishingAttempts?.filter(p => p.status === 'pending').length || 0;

    return {
      total,
      successful,
      failed,
      pending,
      successRate: total > 0 ? (successful / total * 100).toFixed(2) : '0.00',
      failureRate: total > 0 ? (failed / total * 100).toFixed(2) : '0.00'
    };
  } catch (error) {
    console.error('Error getting publishing metrics:', error);
    return {
      total: 0,
      successful: 0,
      failed: 0,
      pending: 0,
      successRate: '0.00',
      failureRate: '0.00',
      error: 'Failed to collect publishing metrics'
    };
  }
}

/**
 * Get user activity metrics
 */
async function getUserMetrics(supabase: any, startTime: Date, endTime: Date) {
  try {
    const { data: activities, error } = await supabase
      .from('activities')
      .select('id, user_id, action, created_at')
      .gte('created_at', startTime.toISOString())
      .lte('created_at', endTime.toISOString());

    if (error) throw error;

    const totalActivities = activities?.length || 0;
    const uniqueUsers = new Set(activities?.map(a => a.user_id)).size;
    const postCreated = activities?.filter(a => a.action === 'POST_CREATED').length || 0;
    const postPublished = activities?.filter(a => a.action === 'POST_PUBLISHED').length || 0;

    return {
      totalActivities,
      uniqueActiveUsers: uniqueUsers,
      postCreatedActivities: postCreated,
      postPublishedActivities: postPublished
    };
  } catch (error) {
    console.error('Error getting user metrics:', error);
    return {
      totalActivities: 0,
      uniqueActiveUsers: 0,
      postCreatedActivities: 0,
      postPublishedActivities: 0,
      error: 'Failed to collect user metrics'
    };
  }
}

/**
 * Get performance metrics
 */
async function getPerformanceMetrics(supabase: any, startTime: Date, endTime: Date) {
  try {
    // This is a placeholder for performance metrics
    // In a real implementation, you might collect response times, error rates, etc.
    return {
      averageResponseTime: '< 2s',
      errorRate: '< 0.1%',
      uptime: '99.9%',
      note: 'Performance metrics collection not fully implemented'
    };
  } catch (error) {
    console.error('Error getting performance metrics:', error);
    return {
      averageResponseTime: 'unknown',
      errorRate: 'unknown',
      uptime: 'unknown',
      error: 'Failed to collect performance metrics'
    };
  }
}

/**
 * Get platform-specific metrics (detailed)
 */
async function getPlatformMetrics(supabase: any, startTime: Date, endTime: Date, userId?: string) {
  try {
    let query = supabase
      .from('post_social_accounts')
      .select('platform, status')
      .gte('created_at', startTime.toISOString())
      .lte('created_at', endTime.toISOString());

    if (userId) {
      query = supabase
        .from('post_social_accounts')
        .select(`
          platform, status,
          posts!inner(user_id)
        `)
        .eq('posts.user_id', userId)
        .gte('created_at', startTime.toISOString())
        .lte('created_at', endTime.toISOString());
    }

    const { data: platformData, error } = await query;

    if (error) throw error;

    const platforms = ['FACEBOOK', 'INSTAGRAM', 'TWITTER', 'LINKEDIN'];
    const metrics = {};

    platforms.forEach(platform => {
      const platformPosts = platformData?.filter(p => p.platform === platform) || [];
      const total = platformPosts.length;
      const successful = platformPosts.filter(p => p.status === 'published').length;
      const failed = platformPosts.filter(p => p.status === 'failed').length;

      metrics[platform.toLowerCase()] = {
        total,
        successful,
        failed,
        successRate: total > 0 ? (successful / total * 100).toFixed(2) : '0.00'
      };
    });

    return metrics;
  } catch (error) {
    console.error('Error getting platform metrics:', error);
    return {
      error: 'Failed to collect platform metrics'
    };
  }
}

/**
 * Get error metrics (detailed)
 */
async function getErrorMetrics(supabase: any, startTime: Date, endTime: Date) {
  try {
    const { data: errors, error } = await supabase
      .from('post_social_accounts')
      .select('error_message, platform, created_at')
      .not('error_message', 'is', null)
      .gte('created_at', startTime.toISOString())
      .lte('created_at', endTime.toISOString());

    if (error) throw error;

    const totalErrors = errors?.length || 0;
    const errorsByPlatform = {};
    const commonErrors = {};

    errors?.forEach(err => {
      // Count by platform
      if (!errorsByPlatform[err.platform]) {
        errorsByPlatform[err.platform] = 0;
      }
      errorsByPlatform[err.platform]++;

      // Count common error messages
      const errorKey = err.error_message?.substring(0, 50) || 'Unknown error';
      if (!commonErrors[errorKey]) {
        commonErrors[errorKey] = 0;
      }
      commonErrors[errorKey]++;
    });

    return {
      totalErrors,
      errorsByPlatform,
      commonErrors: Object.entries(commonErrors)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 5)
        .reduce((obj, [key, value]) => ({ ...obj, [key]: value }), {})
    };
  } catch (error) {
    console.error('Error getting error metrics:', error);
    return {
      totalErrors: 0,
      errorsByPlatform: {},
      commonErrors: {},
      error: 'Failed to collect error metrics'
    };
  }
}

/**
 * Get queue metrics (detailed)
 */
async function getQueueMetrics(supabase: any, startTime: Date, endTime: Date) {
  try {
    const { data: queueItems, error } = await supabase
      .from('scheduled_posts_queue')
      .select('status, scheduled_for, retry_count, created_at')
      .gte('created_at', startTime.toISOString())
      .lte('created_at', endTime.toISOString());

    if (error) throw error;

    const total = queueItems?.length || 0;
    const pending = queueItems?.filter(q => q.status === 'pending').length || 0;
    const processing = queueItems?.filter(q => q.status === 'processing').length || 0;
    const completed = queueItems?.filter(q => q.status === 'completed').length || 0;
    const failed = queueItems?.filter(q => q.status === 'failed').length || 0;

    const now = new Date();
    const overdue = queueItems?.filter(q => 
      q.status === 'pending' && new Date(q.scheduled_for) < now
    ).length || 0;

    return {
      total,
      pending,
      processing,
      completed,
      failed,
      overdue,
      completionRate: total > 0 ? (completed / total * 100).toFixed(2) : '0.00'
    };
  } catch (error) {
    console.error('Error getting queue metrics:', error);
    return {
      total: 0,
      pending: 0,
      processing: 0,
      completed: 0,
      failed: 0,
      overdue: 0,
      completionRate: '0.00',
      error: 'Failed to collect queue metrics'
    };
  }
}
