(()=>{var e={};e.id=323,e.ids=[323],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30549:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>_,routeModule:()=>h,serverHooks:()=>m,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>g});var a={};r.r(a),r.d(a,{GET:()=>d,POST:()=>p});var s=r(96559),o=r(48088),n=r(37719),i=r(32190),c=r(94035),u=r(80726),l=r(44999);async function p(e){try{let{platform:t}=await e.json();if(!t)return i.NextResponse.json({error:"Platform is required"},{status:400});let r=await c.g.generateAuthUrl(t);if(!r)return i.NextResponse.json({error:`Auth URL generation failed for platform: ${t}`},{status:400});let a=await (0,l.cookies)();return a.set(`oauth_state_${t}`,r.state,{httpOnly:!0,secure:!0,sameSite:"lax",maxAge:600}),a.set(`oauth_verifier_${t}`,r.codeVerifier,{httpOnly:!0,secure:!0,sameSite:"lax",maxAge:600}),i.NextResponse.json({success:!0,authUrl:r.url,platform:t})}catch(e){return console.error("[Enhanced Connect API] Error:",e),i.NextResponse.json({error:"Failed to initiate OAuth flow",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}async function d(e){try{let{searchParams:t}=new URL(e.url),r=t.get("platform"),a=t.get("code"),s=t.get("state"),o=t.get("error");if(o)return i.NextResponse.json({error:`OAuth error: ${o}`},{status:400});if(!r||!a||!s)return i.NextResponse.json({error:"Missing required OAuth parameters"},{status:400});let n=await (0,l.cookies)(),p=n.get(`oauth_state_${r}`)?.value,d=n.get(`oauth_verifier_${r}`)?.value;if(!p||p!==s)return i.NextResponse.json({error:"Invalid OAuth state"},{status:400});if(!d)return i.NextResponse.json({error:"Missing code verifier"},{status:400});let h=await c.g.authenticate(r,{code:a,codeVerifier:d}),f=u.sY,g=c.g.getProvider(r);if(g?.isBetweenSteps){let e=await c.g.getBusinessAccounts(r,h.accessToken);return i.NextResponse.json({success:!0,requiresAccountSelection:!0,accounts:e,tempToken:h.accessToken,platform:r})}let{data:m,error:_}=await f.from("social_accounts").upsert({user_id:"demo-user-id",platform:r,account_id:h.id,account_name:h.name,account_username:h.username,account_picture:h.picture,access_token:h.accessToken,refresh_token:h.refreshToken,expires_at:h.expiresIn?new Date(Date.now()+1e3*h.expiresIn).toISOString():null,account_type:"personal",is_active:!0,created_at:new Date().toISOString(),updated_at:new Date().toISOString()},{onConflict:"user_id,platform,account_id"}).select().single();if(_)throw _;return n.delete(`oauth_state_${r}`),n.delete(`oauth_verifier_${r}`),i.NextResponse.json({success:!0,integration:{id:m.id,platform:m.platform,accountName:m.account_name,accountUsername:m.account_username,accountPicture:m.account_picture,accountType:m.account_type,isActive:m.is_active}})}catch(e){return console.error("[Enhanced Connect API] Callback error:",e),i.NextResponse.json({error:"Failed to complete OAuth flow",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}let h=new s.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/social/enhanced/connect/route",pathname:"/api/social/enhanced/connect",filename:"route",bundlePath:"app/api/social/enhanced/connect/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Ewasl\\eWasl.com_Cursor_Digital_Ocean\\ewasl-app\\src\\app\\api\\social\\enhanced\\connect\\route.ts",nextConfigOutput:"standalone",userland:a}),{workAsyncStorage:f,workUnitAsyncStorage:g,serverHooks:m}=h;function _(){return(0,n.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:g})}},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94035:(e,t,r)=>{"use strict";r.d(t,{g:()=>l});var a=r(15900),s=r(96606),o=r(27002);class n extends o.g{async refreshToken(e){return{refreshToken:"",expiresIn:0,accessToken:"",id:"",name:"",picture:"",username:""}}async generateAuthUrl(){let e=`instagram_${this.makeId(6)}`,t=this.makeId(10);return{url:`https://www.facebook.com/v20.0/dialog/oauth?client_id=${process.env.FACEBOOK_APP_ID}&redirect_uri=${encodeURIComponent("https://app.ewasl.com/api/facebook/callback")}&state=${e}&scope=${this.scopes.join(",")}`,codeVerifier:t,state:e}}async authenticate(e){try{let t=await this.fetch(`https://graph.facebook.com/v20.0/oauth/access_token?client_id=${process.env.FACEBOOK_APP_ID}&redirect_uri=${encodeURIComponent(`https://app.ewasl.com/api/facebook/callback${e.refresh?`?refresh=${e.refresh}`:""}`)}&client_secret=${process.env.FACEBOOK_APP_SECRET}&code=${e.code}`),{access_token:r}=await t.json(),a=await this.fetch(`https://graph.facebook.com/v20.0/me?fields=id,name,picture&access_token=${r}`),{id:s,name:o,picture:{data:{url:n}}}=await a.json();return{id:s,name:o,accessToken:r,refreshToken:r,expiresIn:5184e3,picture:n,username:""}}catch(e){throw console.error("[InstagramEnhanced] Authentication error:",e),Error(`Instagram authentication failed: ${e instanceof Error?e.message:e}`)}}async getInstagramBusinessAccounts(e){try{let t=await this.fetch(`https://graph.facebook.com/v20.0/me/accounts?fields=id,name,instagram_business_account&access_token=${e}`),{data:r}=await t.json(),a=[];for(let e of r)if(e.instagram_business_account)try{let t=await this.fetch(`https://graph.facebook.com/v20.0/${e.instagram_business_account.id}?fields=id,username,name,profile_picture_url&access_token=${e.access_token}`),r=await t.json();a.push({id:r.id,name:r.name||r.username,username:r.username,picture:r.profile_picture_url,type:"business",pageId:e.id,pageAccessToken:e.access_token})}catch(e){console.error("[InstagramEnhanced] Error fetching IG account details:",e)}return a}catch(e){return console.error("[InstagramEnhanced] Get business accounts error:",e),[]}}async post(e,t,r,a){try{let[a]=r;if(!a.media||0===a.media.length)throw Error("Instagram posts require at least one media item");let s=[];if(1===a.media.length){let r=a.media[0],o=await this.createMediaContainer(e,t,r,a.message),n=await this.publishMedia(e,t,o);s.push({id:a.id,postId:n.id,releaseURL:`https://www.instagram.com/p/${n.id}/`,status:"posted"})}else{let r=await Promise.all(a.media.map(r=>this.createMediaContainer(e,t,r))),o=await this.createCarouselContainer(e,t,r,a.message),n=await this.publishMedia(e,t,o);s.push({id:a.id,postId:n.id,releaseURL:`https://www.instagram.com/p/${n.id}/`,status:"posted"})}return s}catch(e){throw console.error("[InstagramEnhanced] Post error:",e),Error(`Instagram posting failed: ${e instanceof Error?e.message:e}`)}}async createMediaContainer(e,t,r,a){try{let t="video"===r.type||r.url.includes(".mp4"),s={image_url:t?void 0:r.url,video_url:t?r.url:void 0,media_type:t?"VIDEO":"IMAGE"};a&&(s.caption=a);let o=await this.fetch(`https://graph.facebook.com/v20.0/${e}/media`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(s)}),{id:n}=await o.json();return n}catch(e){throw console.error("[InstagramEnhanced] Create media container error:",e),e}}async createCarouselContainer(e,t,r,a){try{let t=await this.fetch(`https://graph.facebook.com/v20.0/${e}/media`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({media_type:"CAROUSEL",children:r.join(","),caption:a})}),{id:s}=await t.json();return s}catch(e){throw console.error("[InstagramEnhanced] Create carousel container error:",e),e}}async publishMedia(e,t,r){try{let t=await this.fetch(`https://graph.facebook.com/v20.0/${e}/media_publish`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({creation_id:r})});return await t.json()}catch(e){throw console.error("[InstagramEnhanced] Publish media error:",e),e}}async getMediaInsights(e,t){try{let r=await this.fetch(`https://graph.facebook.com/v20.0/${e}/insights?metric=impressions,reach,engagement&access_token=${t}`);return await r.json()}catch(e){return console.error("[InstagramEnhanced] Get media insights error:",e),null}}async getAccountInsights(e,t,r="day",a,s){try{let o=`https://graph.facebook.com/v20.0/${e}/insights?metric=impressions,reach,profile_views&period=${r}&access_token=${t}`;a&&(o+=`&since=${a}`),s&&(o+=`&until=${s}`);let n=await this.fetch(o);return await n.json()}catch(e){return console.error("[InstagramEnhanced] Get account insights error:",e),null}}constructor(...e){super(...e),this.identifier="instagram",this.name="Instagram Business",this.isBetweenSteps=!0,this.scopes=["instagram_basic","instagram_content_publish","pages_show_list","pages_read_engagement","business_management"]}}var i=r(68575),c=r(80726);class u{constructor(){this.providers=new Map,this.supabase=c.sY,this.initializeProviders()}initializeProviders(){let e=new a.K;this.providers.set(e.identifier,e);let t=new s.i;this.providers.set(t.identifier,t);let r=new n;this.providers.set(r.identifier,r);let o=new i.$;this.providers.set(o.identifier,o)}getProvider(e){return this.providers.get(e)||null}getAllProviders(){return Array.from(this.providers.values())}async generateAuthUrl(e){let t=this.getProvider(e);if(!t)throw Error(`Provider not found for platform: ${e}`);try{return await t.generateAuthUrl()}catch(t){throw console.error(`[IntegrationManager] Auth URL generation failed for ${e}:`,t),t}}async authenticate(e,t){let r=this.getProvider(e);if(!r)throw Error(`Provider not found for platform: ${e}`);try{return await r.authenticate(t)}catch(t){throw console.error(`[IntegrationManager] Authentication failed for ${e}:`,t),t}}async refreshToken(e,t){let r=this.getProvider(e);if(!r)throw Error(`Provider not found for platform: ${e}`);try{return await r.refreshToken(t)}catch(t){throw console.error(`[IntegrationManager] Token refresh failed for ${e}:`,t),t}}async post(e,t){try{let{data:r,error:a}=await this.supabase.from("social_accounts").select("*").eq("id",e).single();if(a||!r)throw Error(`Integration not found: ${e}`);let s=this.getProvider(r.platform);if(!s)throw Error(`Provider not found for platform: ${r.platform}`);if(r.expires_at&&new Date(r.expires_at)<=new Date)if(r.refresh_token){let t=await s.refreshToken(r.refresh_token);await this.supabase.from("social_accounts").update({access_token:t.accessToken,refresh_token:t.refreshToken,expires_at:t.expiresIn?new Date(Date.now()+1e3*t.expiresIn).toISOString():null,updated_at:new Date().toISOString()}).eq("id",e),r.access_token=t.accessToken}else throw Error(`Token expired and no refresh token available for ${r.platform}`);let o=await s.post(r.account_id,r.access_token,t,r);return await this.logPostActivity(r.user_id,r.platform,t,o),o}catch(t){throw console.error(`[IntegrationManager] Post failed for integration ${e}:`,t),t}}async testConnection(e){try{let{data:t,error:r}=await this.supabase.from("social_accounts").select("*").eq("id",e).single();if(r||!t)return{success:!1,error:`Integration not found: ${e}`};if(!this.getProvider(t.platform))return{success:!1,error:`Provider not found for platform: ${t.platform}`};return{success:!0,accountInfo:{platform:t.platform,accountName:t.account_name,accountUsername:t.account_username,lastUsed:t.updated_at}}}catch(e){return{success:!1,error:e instanceof Error?e.message:"Unknown error"}}}async getBusinessAccounts(e,t){let r=this.getProvider(e);if(!r)throw Error(`Provider not found for platform: ${e}`);try{switch(e){case"linkedin":if(r.getCompanyPages)return(await r.getCompanyPages(t)).map(e=>({id:e.id,name:e.name,username:e.vanityName,picture:e.logoUrl,type:"business"}));break;case"facebook":if(r.getBusinessPages)return await r.getBusinessPages(t);break;case"instagram":if(r.getInstagramBusinessAccounts)return await r.getInstagramBusinessAccounts(t)}return[]}catch(t){throw console.error(`[IntegrationManager] Failed to get business accounts for ${e}:`,t),t}}async logPostActivity(e,t,r,a){try{await this.supabase.from("activity_logs").insert({user_id:e,platform:t,action:"post_published",details:{post_count:r.length,success_count:a.filter(e=>"posted"===e.status).length,failed_count:a.filter(e=>"failed"===e.status).length,post_ids:a.map(e=>e.postId)},created_at:new Date().toISOString()})}catch(e){console.error("[IntegrationManager] Failed to log post activity:",e)}}async getIntegrationStats(e){try{let{data:t,error:r}=await this.supabase.from("social_accounts").select("platform, is_active, updated_at").eq("user_id",e);if(r)throw r;let{data:a,error:s}=await this.supabase.from("activity_logs").select("platform, action, created_at, details").eq("user_id",e).order("created_at",{ascending:!1}).limit(10);if(s)throw s;let o=t?.reduce((e,t)=>(e[t.platform]=(e[t.platform]||0)+1,e),{})||{};return{totalIntegrations:t?.length||0,activeIntegrations:t?.filter(e=>e.is_active).length||0,platformBreakdown:o,recentActivity:a||[]}}catch(e){throw console.error("[IntegrationManager] Failed to get integration stats:",e),e}}}let l=new u},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4243,6167,580,4999,2721],()=>r(30549));module.exports=a})();