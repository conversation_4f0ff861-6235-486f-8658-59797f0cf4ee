{"kind": "FETCH", "data": {"headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "95e05a3f4fe06121-IST", "connection": "keep-alive", "content-encoding": "gzip", "content-location": "/scheduled_posts_queue?limit=10&order=scheduled_for.asc&scheduled_for=lte.2025-07-12T11%3A55%3A33.893Z&select=%2A&status=eq.pending", "content-profile": "public", "content-range": "*/*", "content-type": "application/json; charset=utf-8", "date": "Sat, 12 Jul 2025 11:55:36 GMT", "sb-gateway-version": "1", "sb-project-ref": "nnxfzhxqzmriggulsudr", "server": "cloudflare", "set-cookie": "__cf_bm=6Tu0pfT5F.FzWYYPw5pynotRWT1HS1UyuS_adOVVA_Y-1752321336-*******-L2UMAzVBpO876cuCNIR0j4hdM_yiUqBo4itMpweKsEJ3rmOS7tTREO7vOIjS0.4P3jhB9mxpxyP5R0ohXYMXgR_aG_9_6_x7px6jdqMHiU4; path=/; expires=Sat, 12-Jul-25 12:25:36 GMT; domain=.supabase.co; HttpOnly; Secure; SameSite=None", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "transfer-encoding": "chunked", "vary": "Accept-Encoding", "x-content-type-options": "nosniff", "x-envoy-attempt-count": "1", "x-envoy-upstream-service-time": "1"}, "body": "W10=", "status": 200, "url": "https://nnxfzhxqzmriggulsudr.supabase.co/rest/v1/scheduled_posts_queue?select=*&status=eq.pending&scheduled_for=lte.2025-07-12T11%3A55%3A33.893Z&order=scheduled_for.asc&limit=10"}, "revalidate": 31536000, "tags": []}