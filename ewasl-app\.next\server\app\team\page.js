(()=>{var e={};e.id=8898,e.ids=[8898],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10052:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=t(65239),a=t(48088),i=t(88170),n=t.n(i),l=t(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);t.d(r,o);let d={children:["",{children:["team",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,41218)),"C:\\Users\\<USER>\\Desktop\\Ewasl\\eWasl.com_Cursor_Digital_Ocean\\ewasl-app\\src\\app\\team\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,45399)),"C:\\Users\\<USER>\\Desktop\\Ewasl\\eWasl.com_Cursor_Digital_Ocean\\ewasl-app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\Ewasl\\eWasl.com_Cursor_Digital_Ocean\\ewasl-app\\src\\app\\team\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/team/page",pathname:"/team",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},12720:(e,r,t)=>{"use strict";t.d(r,{eu:()=>k,q5:()=>A,BK:()=>N});var s=t(60687),a=t(43210),i=t(11273),n=t(13495),l=t(66156),o=t(14163),d=t(57379);function c(){return()=>{}}var u="Avatar",[p,m]=(0,i.A)(u),[f,x]=p(u),h=a.forwardRef((e,r)=>{let{__scopeAvatar:t,...i}=e,[n,l]=a.useState("idle");return(0,s.jsx)(f,{scope:t,imageLoadingStatus:n,onImageLoadingStatusChange:l,children:(0,s.jsx)(o.sG.span,{...i,ref:r})})});h.displayName=u;var g="AvatarImage",y=a.forwardRef((e,r)=>{let{__scopeAvatar:t,src:i,onLoadingStatusChange:u=()=>{},...p}=e,m=x(g,t),f=function(e,{referrerPolicy:r,crossOrigin:t}){let s=(0,d.useSyncExternalStore)(c,()=>!0,()=>!1),i=a.useRef(null),n=s?(i.current||(i.current=new window.Image),i.current):null,[o,u]=a.useState(()=>j(n,e));return(0,l.N)(()=>{u(j(n,e))},[n,e]),(0,l.N)(()=>{let e=e=>()=>{u(e)};if(!n)return;let s=e("loaded"),a=e("error");return n.addEventListener("load",s),n.addEventListener("error",a),r&&(n.referrerPolicy=r),"string"==typeof t&&(n.crossOrigin=t),()=>{n.removeEventListener("load",s),n.removeEventListener("error",a)}},[n,t,r]),o}(i,p),h=(0,n.c)(e=>{u(e),m.onImageLoadingStatusChange(e)});return(0,l.N)(()=>{"idle"!==f&&h(f)},[f,h]),"loaded"===f?(0,s.jsx)(o.sG.img,{...p,ref:r,src:i}):null});y.displayName=g;var v="AvatarFallback",b=a.forwardRef((e,r)=>{let{__scopeAvatar:t,delayMs:i,...n}=e,l=x(v,t),[d,c]=a.useState(void 0===i);return a.useEffect(()=>{if(void 0!==i){let e=window.setTimeout(()=>c(!0),i);return()=>window.clearTimeout(e)}},[i]),d&&"loaded"!==l.imageLoadingStatus?(0,s.jsx)(o.sG.span,{...n,ref:r}):null});function j(e,r){return e?r?(e.src!==r&&(e.src=r),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}b.displayName=v;var w=t(4780);let k=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)(h,{ref:t,className:(0,w.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",e),...r}));k.displayName=h.displayName;let N=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)(y,{ref:t,className:(0,w.cn)("aspect-square h-full w-full",e),...r}));N.displayName=y.displayName;let A=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)(b,{ref:t,className:(0,w.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted",e),...r}));A.displayName=b.displayName},13861:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22918:(e,r,t)=>{Promise.resolve().then(t.bind(t,32252))},23026:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("user-plus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29523:(e,r,t)=>{"use strict";t.d(r,{$:()=>o,r:()=>l});var s=t(60687);t(43210);var a=t(8730),i=t(24224),n=t(4780);let l=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o({className:e,variant:r,size:t,asChild:i=!1,...o}){let d=i?a.DX:"button";return(0,s.jsx)(d,{"data-slot":"button",className:(0,n.cn)(l({variant:r,size:t,className:e})),...o})}},32252:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>v});var s=t(60687),a=t(43210),i=t(52581),n=t(29523),l=t(44493),o=t(96834),d=t(12720),c=t(92363),u=t(99891),p=t(84027),m=t(63143),f=t(13861),x=t(41312),h=t(23026),g=t(88233),y=t(41550);function v(){let[e,r]=(0,a.useState)(!0),[t,v]=(0,a.useState)([]),[b,j]=(0,a.useState)([]),[w,k]=(0,a.useState)(null),[N,A]=(0,a.useState)(""),[E,S]=(0,a.useState)(!1),[R,C]=(0,a.useState)(""),[_,z]=(0,a.useState)("VIEWER"),[I,W]=(0,a.useState)(""),[D,q]=(0,a.useState)(!1),M=async()=>{r(!0);try{let e=await fetch("/api/workspaces"),r=await e.json();if(r.success&&r.workspaces.length>0){let e=r.workspaces[0];k(e);let t=await fetch(`/api/workspaces/${e.id}/members`),s=await t.json();s.success&&(v(s.members),j(s.invitations),A(s.user_role))}}catch(e){console.error("Error loading team data:",e),i.o.error("فشل في تحميل بيانات الفريق")}finally{r(!1)}},B=async()=>{if(w&&R){q(!0);try{let e=await fetch(`/api/workspaces/${w.id}/members`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:R,role:_,message:I})}),r=await e.json();r.success?(i.o.success("تم إرسال الدعوة بنجاح"),S(!1),C(""),W(""),M()):i.o.error(r.error||"فشل في إرسال الدعوة")}catch(e){console.error("Error inviting member:",e),i.o.error("حدث خطأ أثناء إرسال الدعوة")}finally{q(!1)}}},T=async e=>{if(w&&confirm("هل أنت متأكد من إزالة هذا العضو؟"))try{let r=await fetch(`/api/workspaces/${w.id}/members?member_id=${e}`,{method:"DELETE"}),t=await r.json();t.success?(i.o.success("تم إزالة العضو بنجاح"),M()):i.o.error(t.error||"فشل في إزالة العضو")}catch(e){console.error("Error removing member:",e),i.o.error("حدث خطأ أثناء إزالة العضو")}},O=e=>{switch(e){case"OWNER":return(0,s.jsx)(c.A,{className:"h-4 w-4 text-yellow-500"});case"ADMIN":return(0,s.jsx)(u.A,{className:"h-4 w-4 text-red-500"});case"MANAGER":return(0,s.jsx)(p.A,{className:"h-4 w-4 text-blue-500"});case"EDITOR":return(0,s.jsx)(m.A,{className:"h-4 w-4 text-green-500"});case"VIEWER":return(0,s.jsx)(f.A,{className:"h-4 w-4 text-gray-500"});default:return(0,s.jsx)(x.A,{className:"h-4 w-4"})}},P=e=>{switch(e){case"OWNER":return"مالك";case"ADMIN":return"مدير";case"MANAGER":return"مشرف";case"EDITOR":return"محرر";case"VIEWER":return"مشاهد";default:return e}},L=["OWNER","ADMIN"].includes(N),G=["OWNER","ADMIN","MANAGER"].includes(N);return e?(0,s.jsx)("div",{style:{minHeight:"100vh",background:"linear-gradient(135deg, #f8fafc 0%, #e0f2fe 50%, #f3e8ff 100%)",direction:"rtl",fontFamily:"Inter, sans-serif",padding:"2rem",display:"flex",alignItems:"center",justifyContent:"center"},children:(0,s.jsxs)("div",{style:{background:"white",padding:"2rem",borderRadius:"1rem",boxShadow:"0 25px 50px -12px rgba(0, 0, 0, 0.25)",textAlign:"center"},children:[(0,s.jsx)("div",{style:{width:"40px",height:"40px",border:"4px solid #e2e8f0",borderTop:"4px solid #3b82f6",borderRadius:"50%",animation:"spin 1s linear infinite",margin:"0 auto 1rem"}}),(0,s.jsx)("p",{style:{color:"#64748b"},children:"جاري تحميل بيانات الفريق..."})]})}):(0,s.jsx)("div",{style:{minHeight:"100vh",background:"linear-gradient(135deg, #f8fafc 0%, #e0f2fe 50%, #f3e8ff 100%)",direction:"rtl",fontFamily:"Inter, sans-serif",padding:"2rem"},children:(0,s.jsxs)("div",{style:{maxWidth:"1200px",margin:"0 auto"},children:[(0,s.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"2rem"},children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{style:{fontSize:"2rem",fontWeight:"bold",background:"linear-gradient(to right, #2563eb, #9333ea)",WebkitBackgroundClip:"text",WebkitTextFillColor:"transparent",marginBottom:"0.5rem"},children:"\uD83D\uDC65 إدارة الفريق"}),(0,s.jsx)("p",{style:{color:"#64748b"},children:"إدارة أعضاء الفريق والأدوار والصلاحيات"})]}),G&&(0,s.jsxs)(n.$,{onClick:()=>S(!0),style:{background:"linear-gradient(to right, #10b981, #059669)",color:"white",border:"none",padding:"0.75rem 1.5rem",borderRadius:"0.5rem",fontSize:"1rem",fontWeight:"bold",cursor:"pointer",display:"flex",alignItems:"center",gap:"0.5rem"},children:[(0,s.jsx)(h.A,{className:"h-4 w-4"}),"دعوة عضو جديد"]})]}),w&&(0,s.jsx)(l.Zp,{style:{marginBottom:"2rem"},children:(0,s.jsxs)(l.aR,{children:[(0,s.jsxs)(l.ZB,{style:{display:"flex",alignItems:"center",gap:"0.5rem"},children:[(0,s.jsx)(x.A,{className:"h-5 w-5"}),w.name]}),(0,s.jsxs)(l.BT,{children:["الخطة: ",w.plan_type," • الحد الأقصى للأعضاء: ",w.limits.team_members," • الأعضاء الحاليون: ",t.length]})]})}),(0,s.jsxs)(l.Zp,{style:{marginBottom:"2rem"},children:[(0,s.jsxs)(l.aR,{children:[(0,s.jsxs)(l.ZB,{children:["أعضاء الفريق (",t.length,")"]}),(0,s.jsx)(l.BT,{children:"إدارة أعضاء الفريق وأدوارهم"})]}),(0,s.jsx)(l.Wu,{children:(0,s.jsx)("div",{style:{display:"grid",gap:"1rem"},children:t.map(e=>(0,s.jsxs)("div",{style:{display:"flex",alignItems:"center",justifyContent:"space-between",padding:"1rem",background:"#f8fafc",borderRadius:"0.5rem",border:"1px solid #e2e8f0"},children:[(0,s.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"1rem"},children:[(0,s.jsxs)(d.eu,{children:[(0,s.jsx)(d.BK,{src:e.user?.user_metadata?.avatar_url}),(0,s.jsx)(d.q5,{children:e.user?.user_metadata?.full_name?.charAt(0)||e.user?.email?.charAt(0)})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{style:{fontWeight:"bold",marginBottom:"0.25rem"},children:e.user?.user_metadata?.full_name||e.user?.email}),(0,s.jsx)("div",{style:{fontSize:"0.875rem",color:"#64748b",marginBottom:"0.25rem"},children:e.user?.email}),e.title&&(0,s.jsx)("div",{style:{fontSize:"0.75rem",color:"#64748b"},children:e.title})]})]}),(0,s.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"1rem"},children:[(0,s.jsxs)(o.E,{variant:"secondary",style:{display:"flex",alignItems:"center",gap:"0.25rem"},children:[O(e.role),P(e.role)]}),(0,s.jsxs)("div",{style:{fontSize:"0.75rem",color:"#64748b"},children:["انضم في ",new Date(e.joined_at).toLocaleDateString("ar")]}),L&&"OWNER"!==e.role&&(0,s.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>T(e.id),style:{color:"#ef4444",padding:"0.25rem"},children:(0,s.jsx)(g.A,{className:"h-4 w-4"})})]})]},e.id))})})]}),b.length>0&&(0,s.jsxs)(l.Zp,{children:[(0,s.jsxs)(l.aR,{children:[(0,s.jsxs)(l.ZB,{children:["الدعوات المعلقة (",b.length,")"]}),(0,s.jsx)(l.BT,{children:"الدعوات التي لم يتم قبولها بعد"})]}),(0,s.jsx)(l.Wu,{children:(0,s.jsx)("div",{style:{display:"grid",gap:"1rem"},children:b.map(e=>(0,s.jsxs)("div",{style:{display:"flex",alignItems:"center",justifyContent:"space-between",padding:"1rem",background:"#fef3c7",borderRadius:"0.5rem",border:"1px solid #fbbf24"},children:[(0,s.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"1rem"},children:[(0,s.jsx)(y.A,{className:"h-5 w-5 text-amber-600"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{style:{fontWeight:"bold",marginBottom:"0.25rem"},children:e.email}),(0,s.jsxs)("div",{style:{fontSize:"0.875rem",color:"#92400e"},children:["مدعو كـ ",P(e.role)]})]})]}),(0,s.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"1rem"},children:[(0,s.jsx)(o.E,{variant:"outline",style:{color:"#92400e",borderColor:"#fbbf24"},children:"معلق"}),(0,s.jsxs)("div",{style:{fontSize:"0.75rem",color:"#92400e"},children:["ينتهي في ",new Date(e.expires_at).toLocaleDateString("ar")]})]})]},e.id))})})]}),E&&(0,s.jsx)("div",{style:{position:"fixed",top:0,left:0,right:0,bottom:0,background:"rgba(0, 0, 0, 0.5)",display:"flex",alignItems:"center",justifyContent:"center",zIndex:1e3},children:(0,s.jsxs)("div",{style:{background:"white",padding:"2rem",borderRadius:"1rem",maxWidth:"500px",width:"90%",maxHeight:"90vh",overflow:"auto"},children:[(0,s.jsx)("h2",{style:{fontSize:"1.5rem",fontWeight:"bold",marginBottom:"1rem"},children:"دعوة عضو جديد"}),(0,s.jsxs)("div",{style:{marginBottom:"1rem"},children:[(0,s.jsx)("label",{style:{display:"block",fontSize:"0.875rem",fontWeight:"bold",marginBottom:"0.5rem"},children:"البريد الإلكتروني"}),(0,s.jsx)("input",{type:"email",value:R,onChange:e=>C(e.target.value),placeholder:"<EMAIL>",style:{width:"100%",padding:"0.75rem",border:"1px solid #e2e8f0",borderRadius:"0.5rem",fontSize:"1rem"}})]}),(0,s.jsxs)("div",{style:{marginBottom:"1rem"},children:[(0,s.jsx)("label",{style:{display:"block",fontSize:"0.875rem",fontWeight:"bold",marginBottom:"0.5rem"},children:"الدور"}),(0,s.jsxs)("select",{value:_,onChange:e=>z(e.target.value),style:{width:"100%",padding:"0.75rem",border:"1px solid #e2e8f0",borderRadius:"0.5rem",fontSize:"1rem"},children:[(0,s.jsx)("option",{value:"VIEWER",children:"مشاهد"}),(0,s.jsx)("option",{value:"EDITOR",children:"محرر"}),(0,s.jsx)("option",{value:"MANAGER",children:"مشرف"}),"OWNER"===N&&(0,s.jsx)("option",{value:"ADMIN",children:"مدير"})]})]}),(0,s.jsxs)("div",{style:{marginBottom:"2rem"},children:[(0,s.jsx)("label",{style:{display:"block",fontSize:"0.875rem",fontWeight:"bold",marginBottom:"0.5rem"},children:"رسالة (اختيارية)"}),(0,s.jsx)("textarea",{value:I,onChange:e=>W(e.target.value),placeholder:"رسالة ترحيبية للعضو الجديد...",rows:3,style:{width:"100%",padding:"0.75rem",border:"1px solid #e2e8f0",borderRadius:"0.5rem",fontSize:"1rem",resize:"vertical"}})]}),(0,s.jsxs)("div",{style:{display:"flex",gap:"1rem",justifyContent:"flex-end"},children:[(0,s.jsx)(n.$,{variant:"outline",onClick:()=>S(!1),disabled:D,children:"إلغاء"}),(0,s.jsx)(n.$,{onClick:B,disabled:D||!R,style:{background:"linear-gradient(to right, #10b981, #059669)",color:"white",border:"none"},children:D?"جاري الإرسال...":"إرسال الدعوة"})]})]})})]})})}},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},41218:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ewasl\\\\eWasl.com_Cursor_Digital_Ocean\\\\ewasl-app\\\\src\\\\app\\\\team\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Ewasl\\eWasl.com_Cursor_Digital_Ocean\\ewasl-app\\src\\app\\team\\page.tsx","default")},41312:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},41550:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},44493:(e,r,t)=>{"use strict";t.d(r,{BT:()=>d,Wu:()=>c,ZB:()=>o,Zp:()=>n,aR:()=>l,wL:()=>u});var s=t(60687),a=t(43210),i=t(4780);let n=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...r}));n.displayName="Card";let l=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...r}));l.displayName="CardHeader";let o=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",e),...r}));o.displayName="CardTitle";let d=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",e),...r}));d.displayName="CardDescription";let c=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("p-6 pt-0",e),...r}));c.displayName="CardContent";let u=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("flex items-center p-6 pt-0",e),...r}));u.displayName="CardFooter"},53332:(e,r,t)=>{"use strict";var s=t(43210),a="function"==typeof Object.is?Object.is:function(e,r){return e===r&&(0!==e||1/e==1/r)||e!=e&&r!=r},i=s.useState,n=s.useEffect,l=s.useLayoutEffect,o=s.useDebugValue;function d(e){var r=e.getSnapshot;e=e.value;try{var t=r();return!a(e,t)}catch(e){return!0}}var c="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,r){return r()}:function(e,r){var t=r(),s=i({inst:{value:t,getSnapshot:r}}),a=s[0].inst,c=s[1];return l(function(){a.value=t,a.getSnapshot=r,d(a)&&c({inst:a})},[e,t,r]),n(function(){return d(a)&&c({inst:a}),e(function(){d(a)&&c({inst:a})})},[e]),o(t),t};r.useSyncExternalStore=void 0!==s.useSyncExternalStore?s.useSyncExternalStore:c},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57379:(e,r,t)=>{"use strict";e.exports=t(53332)},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63143:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},70440:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var s=t(31658);let a=async e=>[{type:"image/x-icon",sizes:"48x48",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},82750:(e,r,t)=>{Promise.resolve().then(t.bind(t,41218))},84027:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},88233:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},91645:e=>{"use strict";e.exports=require("net")},92363:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("crown",[["path",{d:"M11.562 3.266a.5.5 0 0 1 .876 0L15.39 8.87a1 1 0 0 0 1.516.294L21.183 5.5a.5.5 0 0 1 .798.519l-2.834 10.246a1 1 0 0 1-.956.734H5.81a1 1 0 0 1-.957-.734L2.02 6.02a.5.5 0 0 1 .798-.519l4.276 3.664a1 1 0 0 0 1.516-.294z",key:"1vdc57"}],["path",{d:"M5 21h14",key:"11awu3"}]])},94735:e=>{"use strict";e.exports=require("events")},96834:(e,r,t)=>{"use strict";t.d(r,{E:()=>l});var s=t(60687);t(43210);var a=t(24224),i=t(4780);let n=(0,a.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function l({className:e,variant:r,...t}){return(0,s.jsx)("div",{className:(0,i.cn)(n({variant:r}),e),...t})}},99891:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4243,6167,2215,1658,9038,9908],()=>t(10052));module.exports=s})();