import { createServiceRoleClient } from '@/lib/supabase/server';

export type LogLevel = 'debug' | 'info' | 'warn' | 'error' | 'critical';

export interface LogEntry {
  id?: string;
  level: LogLevel;
  message: string;
  component?: string;
  action?: string;
  userId?: string;
  requestId?: string;
  platform?: string;
  duration?: number;
  metadata?: Record<string, any>;
  error?: {
    name: string;
    message: string;
    stack?: string;
  };
  timestamp: string;
}

export interface LoggerConfig {
  level: LogLevel;
  enableConsole: boolean;
  enableDatabase: boolean;
  enableFile: boolean;
  component?: string;
}

/**
 * Centralized Logging System for eWasl Platform
 * Provides structured logging with multiple outputs and Arabic error support
 */
export class Logger {
  private config: LoggerConfig;
  private supabase: any;
  private requestId: string;

  constructor(config: Partial<LoggerConfig> = {}) {
    this.config = {
      level: (process.env.LOG_LEVEL as LogLevel) || 'info',
      enableConsole: true,
      enableDatabase: process.env.NODE_ENV === 'production',
      enableFile: false,
      ...config
    };

    this.requestId = this.generateRequestId();
    
    // Initialize Supabase client lazily
    this.supabase = null;
  }

  private getSupabaseClient() {
    if (!this.supabase) {
      try {
        this.supabase = createServiceRoleClient();
      } catch (error) {
        console.error('Failed to initialize Supabase client for logging:', error);
      }
    }
    return this.supabase;
  }

  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private shouldLog(level: LogLevel): boolean {
    const levels: LogLevel[] = ['debug', 'info', 'warn', 'error', 'critical'];
    const currentLevelIndex = levels.indexOf(this.config.level);
    const messageLevelIndex = levels.indexOf(level);
    return messageLevelIndex >= currentLevelIndex;
  }

  private formatMessage(entry: LogEntry): string {
    const timestamp = new Date(entry.timestamp).toISOString();
    const level = entry.level.toUpperCase().padEnd(8);
    const component = entry.component ? `[${entry.component}]` : '';
    const action = entry.action ? `{${entry.action}}` : '';
    const duration = entry.duration ? `(${entry.duration}ms)` : '';
    
    return `${timestamp} ${level} ${component}${action} ${entry.message} ${duration}`.trim();
  }

  private async logToConsole(entry: LogEntry): Promise<void> {
    if (!this.config.enableConsole) return;

    const formattedMessage = this.formatMessage(entry);
    
    switch (entry.level) {
      case 'debug':
        console.debug(formattedMessage, entry.metadata);
        break;
      case 'info':
        console.info(formattedMessage, entry.metadata);
        break;
      case 'warn':
        console.warn(formattedMessage, entry.metadata);
        break;
      case 'error':
      case 'critical':
        console.error(formattedMessage, entry.error || entry.metadata);
        break;
    }
  }

  private async logToDatabase(entry: LogEntry): Promise<void> {
    if (!this.config.enableDatabase) return;

    try {
      const supabase = this.getSupabaseClient();
      if (!supabase) return;

      const { error } = await supabase
        .from('system_logs')
        .insert({
          level: entry.level,
          message: entry.message,
          component: entry.component,
          action: entry.action,
          user_id: entry.userId,
          request_id: entry.requestId,
          platform: entry.platform,
          duration: entry.duration,
          metadata: entry.metadata || {},
          error_details: entry.error,
          created_at: entry.timestamp
        });

      if (error) {
        console.error('Failed to log to database:', error);
      }
    } catch (error) {
      console.error('Database logging error:', error);
    }
  }

  private async writeLog(level: LogLevel, message: string, metadata?: Record<string, any>, error?: Error): Promise<void> {
    if (!this.shouldLog(level)) return;

    const entry: LogEntry = {
      level,
      message,
      component: this.config.component,
      requestId: this.requestId,
      timestamp: new Date().toISOString(),
      metadata
    };

    if (error) {
      entry.error = {
        name: error.name,
        message: error.message,
        stack: error.stack
      };
    }

    // Log to all enabled outputs
    await Promise.all([
      this.logToConsole(entry),
      this.logToDatabase(entry)
    ]);
  }

  /**
   * Set request ID for tracking related logs
   */
  setRequestId(requestId: string): void {
    this.requestId = requestId;
  }

  /**
   * Set component name for all subsequent logs
   */
  setComponent(component: string): void {
    this.config.component = component;
  }

  /**
   * Debug level logging
   */
  async debug(message: string, metadata?: Record<string, any>): Promise<void> {
    await this.writeLog('debug', message, metadata);
  }

  /**
   * Info level logging
   */
  async info(message: string, metadata?: Record<string, any>): Promise<void> {
    await this.writeLog('info', message, metadata);
  }

  /**
   * Warning level logging
   */
  async warn(message: string, metadata?: Record<string, any>): Promise<void> {
    await this.writeLog('warn', message, metadata);
  }

  /**
   * Error level logging
   */
  async error(message: string, error?: Error, metadata?: Record<string, any>): Promise<void> {
    await this.writeLog('error', message, metadata, error);
  }

  /**
   * Critical level logging
   */
  async critical(message: string, error?: Error, metadata?: Record<string, any>): Promise<void> {
    await this.writeLog('critical', message, metadata, error);
  }

  /**
   * Log API request/response
   */
  async apiLog(method: string, path: string, statusCode: number, duration: number, metadata?: Record<string, any>): Promise<void> {
    const level: LogLevel = statusCode >= 500 ? 'error' : statusCode >= 400 ? 'warn' : 'info';
    const message = `${method} ${path} ${statusCode}`;
    
    await this.writeLog(level, message, {
      ...metadata,
      method,
      path,
      statusCode,
      duration,
      type: 'api_request'
    });
  }

  /**
   * Log social media publishing activity
   */
  async publishingLog(platform: string, action: string, success: boolean, duration: number, metadata?: Record<string, any>, error?: Error): Promise<void> {
    const level: LogLevel = success ? 'info' : 'error';
    const message = `${platform} ${action} ${success ? 'succeeded' : 'failed'}`;
    
    await this.writeLog(level, message, {
      ...metadata,
      platform,
      action,
      success,
      duration,
      type: 'publishing'
    }, error);
  }

  /**
   * Log OAuth activity
   */
  async oauthLog(platform: string, action: string, success: boolean, userId?: string, metadata?: Record<string, any>, error?: Error): Promise<void> {
    const level: LogLevel = success ? 'info' : 'error';
    const message = `OAuth ${platform} ${action} ${success ? 'succeeded' : 'failed'}`;
    
    await this.writeLog(level, message, {
      ...metadata,
      platform,
      action,
      success,
      userId,
      type: 'oauth'
    }, error);
  }

  /**
   * Log database operations
   */
  async dbLog(operation: string, table: string, success: boolean, duration: number, metadata?: Record<string, any>, error?: Error): Promise<void> {
    const level: LogLevel = success ? 'debug' : 'error';
    const message = `DB ${operation} ${table} ${success ? 'succeeded' : 'failed'}`;
    
    await this.writeLog(level, message, {
      ...metadata,
      operation,
      table,
      success,
      duration,
      type: 'database'
    }, error);
  }

  /**
   * Log user activity
   */
  async userLog(userId: string, action: string, metadata?: Record<string, any>): Promise<void> {
    const message = `User ${action}`;
    
    await this.writeLog('info', message, {
      ...metadata,
      userId,
      action,
      type: 'user_activity'
    });
  }

  /**
   * Log system events
   */
  async systemLog(event: string, metadata?: Record<string, any>): Promise<void> {
    const message = `System ${event}`;
    
    await this.writeLog('info', message, {
      ...metadata,
      event,
      type: 'system'
    });
  }

  /**
   * Create a child logger with additional context
   */
  child(context: Partial<LoggerConfig> & { userId?: string; component?: string }): Logger {
    const childLogger = new Logger({
      ...this.config,
      ...context
    });
    
    childLogger.requestId = this.requestId;
    return childLogger;
  }

  /**
   * Measure and log execution time
   */
  async time<T>(label: string, fn: () => Promise<T>, metadata?: Record<string, any>): Promise<T> {
    const startTime = Date.now();
    
    try {
      const result = await fn();
      const duration = Date.now() - startTime;
      
      await this.info(`${label} completed`, {
        ...metadata,
        duration,
        success: true
      });
      
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      
      await this.error(`${label} failed`, error as Error, {
        ...metadata,
        duration,
        success: false
      });
      
      throw error;
    }
  }
}

// Export singleton instance
export const logger = new Logger();

// Export Arabic error messages helper
export const arabicErrors = {
  authentication: 'فشل في المصادقة',
  authorization: 'غير مصرح لك بالوصول',
  validation: 'بيانات غير صحيحة',
  database: 'خطأ في قاعدة البيانات',
  network: 'خطأ في الشبكة',
  publishing: 'فشل في النشر',
  oauth: 'فشل في ربط الحساب',
  token: 'انتهت صلاحية الرمز المميز',
  server: 'خطأ في الخادم الداخلي',
  notFound: 'العنصر غير موجود',
  conflict: 'تعارض في البيانات',
  rateLimit: 'تم تجاوز الحد المسموح',
  maintenance: 'النظام قيد الصيانة'
};
