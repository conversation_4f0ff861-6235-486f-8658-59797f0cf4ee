(()=>{var e={};e.id=5498,e.ids=[5498],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},82087:(e,t,r)=>{"use strict";r.d(t,{p:()=>i});var a=r(55511),o=r.n(a);let s={CONNECTED:"connected"},n="https://graph.facebook.com/v18.0";class c{constructor(e){this.config=e}generateAppSecretProof(e){return o().createHmac("sha256",this.config.appSecret).update(e).digest("hex")}getAuthorizationUrl(e){let t=new URLSearchParams({client_id:this.config.appId,redirect_uri:this.config.redirectUri,scope:"pages_show_list,pages_read_engagement,pages_manage_posts,pages_read_user_content,instagram_basic,instagram_content_publish,business_management",response_type:"code",state:e||""});return`https://www.facebook.com/v18.0/dialog/oauth?${t.toString()}`}async exchangeCodeForToken(e){let t=new URLSearchParams({client_id:this.config.appId,client_secret:this.config.appSecret,redirect_uri:this.config.redirectUri,code:e}),r=await fetch(`${n}/oauth/access_token`,{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:t.toString()});if(!r.ok){let e=await r.json();throw Error(`Facebook OAuth error: ${e.error?.message||"Unknown error"}`)}return r.json()}async getLongLivedToken(e){let t=new URLSearchParams({grant_type:"fb_exchange_token",client_id:this.config.appId,client_secret:this.config.appSecret,fb_exchange_token:e}),r=await fetch(`${n}/oauth/access_token?${t.toString()}`);if(!r.ok){let e=await r.json();throw Error(`Facebook token exchange error: ${e.error?.message||"Unknown error"}`)}return r.json()}async validateToken(e){let t=this.generateAppSecretProof(e),r=await fetch(`${n}/me?fields=id,name,email,picture&access_token=${e}&appsecret_proof=${t}`);if(!r.ok){let e=await r.json();throw Error(`Facebook API error: ${e.error?.message||"Invalid token"}`)}return r.json()}async getUserAccounts(e){try{let t=this.generateAppSecretProof(e),r=await fetch(`${n}/me/accounts?fields=id,name,access_token,category,tasks,instagram_business_account&access_token=${e}&appsecret_proof=${t}`);if(!r.ok)throw Error("Failed to fetch Facebook pages");let a=await r.json(),o=[];for(let e of a.data||[]){let t={id:`fb_${e.id}`,platform:"FACEBOOK",accountName:e.name,accountHandle:`@${e.name.toLowerCase().replace(/\s+/g,"")}`,connectionStatus:s.CONNECTED,accessToken:e.access_token,refreshToken:null,expiresAt:null,lastValidatedAt:new Date,metadata:{pageId:e.id,category:e.category,tasks:e.tasks||[]},permissions:["read","write"],profileImageUrl:`https://graph.facebook.com/${e.id}/picture?type=large`,createdAt:new Date,updatedAt:new Date};if(o.push(t),e.instagram_business_account){let t={id:`ig_${e.instagram_business_account.id}`,platform:"INSTAGRAM",accountName:e.name,accountHandle:`@${e.name.toLowerCase().replace(/\s+/g,"")}`,connectionStatus:s.CONNECTED,accessToken:e.access_token,refreshToken:null,expiresAt:null,lastValidatedAt:new Date,metadata:{instagramAccountId:e.instagram_business_account.id,connectedFacebookPageId:e.id},permissions:["read","write"],profileImageUrl:`https://graph.facebook.com/${e.instagram_business_account.id}/picture?type=large`,createdAt:new Date,updatedAt:new Date};o.push(t)}}return o}catch(e){throw console.error("Error fetching user accounts:",e),e}}async getAccountMetrics(e,t,r){try{let a=this.generateAppSecretProof(t);if("FACEBOOK"===r){let r=await fetch(`${n}/${e}/insights?metric=page_fans,page_post_engagements&access_token=${t}&appsecret_proof=${a}`);if(r.ok){let e=(await r.json()).data||[],t=e.find(e=>"page_fans"===e.name)?.values?.[0]?.value||0,a=e.find(e=>"page_post_engagements"===e.name)?.values?.[0]?.value||0;return{followerCount:t,engagementRate:t>0?a/t*100:0,postCount:0}}}else if("INSTAGRAM"===r){let r=await fetch(`${n}/${e}?fields=followers_count,media_count&access_token=${t}&appsecret_proof=${a}`);if(r.ok){let e=await r.json();return{followerCount:e.followers_count||0,engagementRate:0,postCount:e.media_count||0}}}return{followerCount:0,engagementRate:0,postCount:0}}catch(e){return console.error("Error fetching account metrics:",e),{followerCount:0,engagementRate:0,postCount:0}}}async refreshAccessToken(e){throw Error("Facebook token refresh not implemented - use long-lived tokens")}async revokeToken(e){try{let t=this.generateAppSecretProof(e);return(await fetch(`${n}/me/permissions?access_token=${e}&appsecret_proof=${t}`,{method:"DELETE"})).ok}catch(e){return console.error("Error revoking Facebook token:",e),!1}}}function i(){let e={appId:process.env.FACEBOOK_APP_ID,appSecret:process.env.FACEBOOK_APP_SECRET,businessId:process.env.FACEBOOK_BUSINESS_ID,redirectUri:`${process.env.OAUTH_REDIRECT_URL}/facebook`};if(!e.appId||!e.appSecret)throw Error("Facebook OAuth configuration is missing");return new c(e)}},85487:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>f,routeModule:()=>u,serverHooks:()=>g,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>d});var a={};r.r(a),r.d(a,{GET:()=>p});var o=r(96559),s=r(48088),n=r(37719),c=r(32190),i=r(82087);async function p(e){try{let{searchParams:t}=new URL(e.url),r=t.get("token");if(!r)return c.NextResponse.json({error:"Access token required"},{status:400});let a=(0,i.p)(),o=await a.getUserAccounts(r);return c.NextResponse.json({success:!0,accountsFound:o.length,accounts:o.map(e=>({id:e.id,accountName:e.accountName,accountHandle:e.accountHandle,platform:e.platform,connectionStatus:e.connectionStatus,hasAccessToken:!!e.accessToken,accessTokenPreview:e.accessToken?e.accessToken.substring(0,20)+"...":null,metadata:e.metadata,permissions:e.permissions}))})}catch(e){return console.error("\uD83D\uDEA8 DEBUG ERROR:",e),c.NextResponse.json({error:"Debug test failed",details:e instanceof Error?e.message:String(e)},{status:500})}}let u=new o.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/debug/facebook/route",pathname:"/api/debug/facebook",filename:"route",bundlePath:"app/api/debug/facebook/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Ewasl\\eWasl.com_Cursor_Digital_Ocean\\ewasl-app\\src\\app\\api\\debug\\facebook\\route.ts",nextConfigOutput:"standalone",userland:a}),{workAsyncStorage:l,workUnitAsyncStorage:d,serverHooks:g}=u;function f(){return(0,n.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:d})}},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4243,580],()=>r(85487));module.exports=a})();