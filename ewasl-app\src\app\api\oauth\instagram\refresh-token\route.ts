import { NextRequest, NextResponse } from 'next/server';
import { createClient, createServiceRoleClient } from '@/lib/supabase/server';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

/**
 * Instagram Token Refresh Endpoint
 * Handles automatic and manual refresh of Instagram access tokens
 * 
 * POST /api/oauth/instagram/refresh-token
 * Body: { accountId?: string, refreshAll?: boolean }
 */
export async function POST(request: NextRequest) {
  try {
    console.log('🔄 Instagram token refresh endpoint called');

    // Get authenticated user
    const supabase = createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { 
          error: 'المصادقة مطلوبة',
          message: 'Authentication required',
          success: false 
        },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { accountId, refreshAll = false } = body;

    console.log('🔄 Token refresh request:', { accountId, refreshAll, userId: user.id });

    // Create service role client for database operations
    const supabaseService = createServiceRoleClient();

    // Get Instagram accounts to refresh
    let accountsQuery = supabaseService
      .from('social_accounts')
      .select('*')
      .eq('user_id', user.id)
      .eq('platform', 'INSTAGRAM');

    if (accountId && !refreshAll) {
      accountsQuery = accountsQuery.eq('id', accountId);
    }

    const { data: accounts, error: accountsError } = await accountsQuery;

    if (accountsError) {
      console.error('❌ Error fetching Instagram accounts:', accountsError);
      return NextResponse.json(
        { 
          error: 'فشل في جلب حسابات انستغرام',
          message: 'Failed to fetch Instagram accounts',
          success: false,
          details: accountsError
        },
        { status: 500 }
      );
    }

    if (!accounts || accounts.length === 0) {
      return NextResponse.json(
        { 
          error: 'لا توجد حسابات انستغرام متصلة',
          message: 'No connected Instagram accounts found',
          success: false
        },
        { status: 404 }
      );
    }

    console.log(`🔄 Found ${accounts.length} Instagram accounts to refresh`);

    const refreshResults = [];

    // Refresh tokens for each account
    for (const account of accounts) {
      try {
        console.log(`🔄 Refreshing token for account: ${account.account_name} (${account.id})`);

        const result = await refreshInstagramToken(account, supabaseService);
        refreshResults.push({
          accountId: account.id,
          accountName: account.account_name,
          ...result
        });

        console.log(`${result.success ? '✅' : '❌'} Token refresh result for ${account.account_name}:`, {
          success: result.success,
          error: result.error
        });

      } catch (error) {
        console.error(`❌ Error refreshing token for ${account.account_name}:`, error);
        refreshResults.push({
          accountId: account.id,
          accountName: account.account_name,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    // Calculate summary
    const successCount = refreshResults.filter(r => r.success).length;
    const totalCount = refreshResults.length;

    console.log(`📊 Token refresh summary: ${successCount}/${totalCount} successful`);

    return NextResponse.json({
      success: successCount > 0,
      message: successCount === totalCount 
        ? 'تم تحديث جميع الرموز بنجاح'
        : successCount > 0 
          ? `تم تحديث ${successCount} من ${totalCount} رموز`
          : 'فشل في تحديث جميع الرموز',
      results: refreshResults,
      summary: {
        total: totalCount,
        successful: successCount,
        failed: totalCount - successCount
      }
    });

  } catch (error) {
    console.error('❌ Critical error in Instagram token refresh:', error);
    return NextResponse.json(
      { 
        error: 'خطأ في الخادم الداخلي',
        message: 'Internal server error',
        success: false,
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * Refresh Instagram token for a specific account
 */
async function refreshInstagramToken(account: any, supabase: any) {
  try {
    console.log(`🔄 Starting token refresh for Instagram account: ${account.account_id}`);

    // Check if token is close to expiry (refresh if expires within 7 days)
    const now = new Date();
    const expiryDate = account.expires_at ? new Date(account.expires_at) : null;
    const daysUntilExpiry = expiryDate ? Math.ceil((expiryDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)) : null;

    console.log(`🔄 Token expiry check:`, {
      expiryDate: expiryDate?.toISOString(),
      daysUntilExpiry,
      shouldRefresh: !expiryDate || daysUntilExpiry <= 7
    });

    // Instagram uses Facebook's Graph API for token refresh
    // Long-lived tokens can be refreshed to extend their lifetime
    const refreshUrl = `https://graph.facebook.com/v18.0/oauth/access_token`;
    const params = new URLSearchParams({
      grant_type: 'fb_exchange_token',
      client_id: process.env.FACEBOOK_APP_ID!,
      client_secret: process.env.FACEBOOK_APP_SECRET!,
      fb_exchange_token: account.access_token
    });

    console.log(`🔄 Making token refresh request to Facebook Graph API...`);

    const response = await fetch(`${refreshUrl}?${params.toString()}`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
      }
    });

    const data = await response.json();

    if (!response.ok || data.error) {
      const errorMessage = data.error?.message || data.error_description || 'Token refresh failed';
      console.error('❌ Instagram token refresh failed:', {
        status: response.status,
        error: data.error,
        message: errorMessage
      });

      // Update account status to indicate token issues
      await supabase
        .from('social_accounts')
        .update({
          connection_status: 'expired',
          last_validated_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', account.id);

      return {
        success: false,
        error: `فشل في تحديث رمز انستغرام: ${errorMessage}`,
        shouldReconnect: true
      };
    }

    console.log('✅ Instagram token refresh successful');

    // Calculate new expiration date (Facebook long-lived tokens last ~60 days)
    const newExpiryDate = new Date();
    newExpiryDate.setDate(newExpiryDate.getDate() + 55); // Refresh 5 days before actual expiration

    // Update database with new token
    const { error: updateError } = await supabase
      .from('social_accounts')
      .update({
        access_token: data.access_token,
        expires_at: newExpiryDate.toISOString(),
        connection_status: 'connected',
        last_validated_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('id', account.id);

    if (updateError) {
      console.error('❌ Failed to update database with new token:', updateError);
      return {
        success: false,
        error: 'فشل في حفظ الرمز الجديد في قاعدة البيانات'
      };
    }

    console.log('✅ Database updated with new Instagram token');

    return {
      success: true,
      message: 'تم تحديث رمز انستغرام بنجاح',
      expiresAt: newExpiryDate.toISOString(),
      daysExtended: 55
    };

  } catch (error) {
    console.error('❌ Critical error in Instagram token refresh:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * GET endpoint for checking token status
 */
export async function GET(request: NextRequest) {
  try {
    const supabase = createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'المصادقة مطلوبة', success: false },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const accountId = searchParams.get('accountId');

    // Get Instagram accounts
    let query = supabase
      .from('social_accounts')
      .select('id, account_name, expires_at, connection_status, last_validated_at')
      .eq('user_id', user.id)
      .eq('platform', 'INSTAGRAM');

    if (accountId) {
      query = query.eq('id', accountId);
    }

    const { data: accounts, error } = await query;

    if (error) {
      return NextResponse.json(
        { error: 'فشل في جلب حسابات انستغرام', success: false },
        { status: 500 }
      );
    }

    // Calculate token status for each account
    const now = new Date();
    const accountsWithStatus = accounts?.map(account => {
      const expiryDate = account.expires_at ? new Date(account.expires_at) : null;
      const daysUntilExpiry = expiryDate ? Math.ceil((expiryDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)) : null;
      
      return {
        ...account,
        daysUntilExpiry,
        needsRefresh: !expiryDate || daysUntilExpiry <= 7,
        isExpired: expiryDate && expiryDate < now
      };
    }) || [];

    return NextResponse.json({
      success: true,
      accounts: accountsWithStatus,
      summary: {
        total: accountsWithStatus.length,
        needsRefresh: accountsWithStatus.filter(a => a.needsRefresh).length,
        expired: accountsWithStatus.filter(a => a.isExpired).length
      }
    });

  } catch (error) {
    console.error('❌ Error checking Instagram token status:', error);
    return NextResponse.json(
      { error: 'خطأ في الخادم الداخلي', success: false },
      { status: 500 }
    );
  }
}
