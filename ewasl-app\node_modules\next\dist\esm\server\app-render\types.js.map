{"version": 3, "sources": ["../../../src/server/app-render/types.ts"], "sourcesContent": ["import type { LoadComponentsReturnType } from '../load-components'\nimport type { ServerRuntime, SizeLimit } from '../../types'\nimport type { NextConfigComplete } from '../../server/config-shared'\nimport type { ClientReferenceManifest } from '../../build/webpack/plugins/flight-manifest-plugin'\nimport type { NextFontManifest } from '../../build/webpack/plugins/next-font-manifest-plugin'\nimport type { ParsedUrlQuery } from 'querystring'\nimport type { AppPageModule } from '../route-modules/app-page/module'\nimport type {\n  HeadData,\n  LoadingModuleData,\n} from '../../shared/lib/app-router-context.shared-runtime'\nimport type { DeepReadonly } from '../../shared/lib/deep-readonly'\nimport type { __ApiPreviewProps } from '../api-utils'\n\nimport s from 'next/dist/compiled/superstruct'\nimport type { RequestLifecycleOpts } from '../base-server'\nimport type { InstrumentationOnRequestError } from '../instrumentation/types'\nimport type { NextRequestHint } from '../web/adapter'\nimport type { BaseNextRequest } from '../base-http'\nimport type { IncomingMessage } from 'http'\nimport type { RenderResumeDataCache } from '../resume-data-cache/resume-data-cache'\n\nexport type DynamicParamTypes =\n  | 'catchall'\n  | 'catchall-intercepted'\n  | 'optional-catchall'\n  | 'dynamic'\n  | 'dynamic-intercepted'\n\nconst dynamicParamTypesSchema = s.enums(['c', 'ci', 'oc', 'd', 'di'])\n\nexport type DynamicParamTypesShort = s.Infer<typeof dynamicParamTypesSchema>\n\nconst segmentSchema = s.union([\n  s.string(),\n  s.tuple([s.string(), s.string(), dynamicParamTypesSchema]),\n])\n\nexport type Segment = s.Infer<typeof segmentSchema>\n\n// unfortunately the tuple is not understood well by Describe so we have to\n// use any here. This does not have any impact on the runtime type since the validation\n// does work correctly.\nexport const flightRouterStateSchema: s.Describe<any> = s.tuple([\n  segmentSchema,\n  s.record(\n    s.string(),\n    s.lazy(() => flightRouterStateSchema)\n  ),\n  s.optional(s.nullable(s.string())),\n  s.optional(\n    s.nullable(\n      s.union([\n        s.literal('refetch'),\n        s.literal('refresh'),\n        s.literal('inside-shared-layout'),\n      ])\n    )\n  ),\n  s.optional(s.boolean()),\n])\n\n/**\n * Router state\n */\nexport type FlightRouterState = [\n  segment: Segment,\n  parallelRoutes: { [parallelRouterKey: string]: FlightRouterState },\n  url?: string | null,\n  /**\n   * \"refresh\" and \"refetch\", despite being similarly named, have different\n   * semantics:\n   * - \"refetch\" is used during a request to inform the server where rendering\n   *   should start from.\n   *\n   * - \"refresh\" is used by the client to mark that a segment should re-fetch the\n   *   data from the server for the current segment. It uses the \"url\" property\n   *   above to determine where to fetch from.\n   *\n   * - \"inside-shared-layout\" is used during a prefetch request to inform the\n   *   server that even if the segment matches, it should be treated as if it's\n   *   within the \"new\" part of a navigation — inside the shared layout. If\n   *   the segment doesn't match, then it has no effect, since it would be\n   *   treated as new regardless. If it does match, though, the server does not\n   *   need to render it, because the client already has it.\n   *\n   *   A bit confusing, but that's because it has only one extremely narrow use\n   *   case — during a non-PPR prefetch, the server uses it to find the first\n   *   loading boundary beneath a shared layout.\n   *\n   *   TODO: We should rethink the protocol for dynamic requests. It might not\n   *   make sense for the client to send a FlightRouterState, since this type is\n   *   overloaded with concerns.\n   */\n  refresh?: 'refetch' | 'refresh' | 'inside-shared-layout' | null,\n  isRootLayout?: boolean,\n]\n\n/**\n * Individual Flight response path\n */\nexport type FlightSegmentPath =\n  // Uses `any` as repeating pattern can't be typed.\n  | any[]\n  // Looks somewhat like this\n  | [\n      segment: Segment,\n      parallelRouterKey: string,\n      segment: Segment,\n      parallelRouterKey: string,\n      segment: Segment,\n      parallelRouterKey: string,\n    ]\n\n/**\n * Represents a tree of segments and the Flight data (i.e. React nodes) that\n * correspond to each one. The tree is isomorphic to the FlightRouterState;\n * however in the future we want to be able to fetch arbitrary partial segments\n * without having to fetch all its children. So this response format will\n * likely change.\n */\nexport type CacheNodeSeedData = [\n  segment: Segment,\n  node: React.ReactNode | null,\n  parallelRoutes: {\n    [parallelRouterKey: string]: CacheNodeSeedData | null\n  },\n  loading: LoadingModuleData | Promise<LoadingModuleData>,\n  isPartial: boolean,\n]\n\nexport type FlightDataSegment = [\n  /* segment of the rendered slice: */ Segment,\n  /* treePatch */ FlightRouterState,\n  /* cacheNodeSeedData */ CacheNodeSeedData | null, // Can be null during prefetch if there's no loading component\n  /* head: viewport */ HeadData,\n  /* isHeadPartial */ boolean,\n]\n\nexport type FlightDataPath =\n  // Uses `any` as repeating pattern can't be typed.\n  | any[]\n  // Looks somewhat like this\n  | [\n      // Holds full path to the segment.\n      ...FlightSegmentPath[],\n      ...FlightDataSegment,\n    ]\n\n/**\n * The Flight response data\n */\nexport type FlightData = Array<FlightDataPath> | string\n\nexport type ActionResult = Promise<any>\n\nexport type ServerOnInstrumentationRequestError = (\n  error: unknown,\n  // The request could be middleware, node server or web server request,\n  // we normalized them into an aligned format to `onRequestError` API later.\n  request: NextRequestHint | BaseNextRequest | IncomingMessage,\n  errorContext: Parameters<InstrumentationOnRequestError>[2]\n) => void | Promise<void>\n\nexport interface RenderOptsPartial {\n  previewProps: __ApiPreviewProps | undefined\n  err?: Error | null\n  dev?: boolean\n  basePath: string\n  trailingSlash: boolean\n  clientReferenceManifest?: DeepReadonly<ClientReferenceManifest>\n  supportsDynamicResponse: boolean\n  runtime?: ServerRuntime\n  serverComponents?: boolean\n  enableTainting?: boolean\n  assetPrefix?: string\n  crossOrigin?: '' | 'anonymous' | 'use-credentials' | undefined\n  nextFontManifest?: DeepReadonly<NextFontManifest>\n  botType?: 'dom' | 'html' | undefined\n  serveStreamingMetadata?: boolean\n  incrementalCache?: import('../lib/incremental-cache').IncrementalCache\n  cacheLifeProfiles?: {\n    [profile: string]: import('../use-cache/cache-life').CacheLife\n  }\n  setIsrStatus?: (key: string, value: boolean | null) => void\n  isRevalidate?: boolean\n  nextExport?: boolean\n  nextConfigOutput?: 'standalone' | 'export'\n  onInstrumentationRequestError?: ServerOnInstrumentationRequestError\n  isDraftMode?: boolean\n  deploymentId?: string\n  onUpdateCookies?: (cookies: string[]) => void\n  loadConfig?: (\n    phase: string,\n    dir: string,\n    customConfig?: object | null,\n    rawConfig?: boolean,\n    silent?: boolean\n  ) => Promise<NextConfigComplete>\n  serverActions?: {\n    bodySizeLimit?: SizeLimit\n    allowedOrigins?: string[]\n  }\n  params?: ParsedUrlQuery\n  isPrefetch?: boolean\n  htmlLimitedBots: string | undefined\n  experimental: {\n    /**\n     * When true, it indicates that the current page supports partial\n     * prerendering.\n     */\n    isRoutePPREnabled?: boolean\n    expireTime: number | undefined\n    clientTraceMetadata: string[] | undefined\n    dynamicIO: boolean\n    clientSegmentCache: boolean | 'client-only'\n    dynamicOnHover: boolean\n    inlineCss: boolean\n    authInterrupts: boolean\n  }\n  postponed?: string\n\n  /**\n   * Should wait for react stream allReady to resolve all suspense boundaries,\n   * in order to perform a full page render.\n   */\n  shouldWaitOnAllReady?: boolean\n\n  /**\n   * The resume data cache that was generated for this partially prerendered\n   * page during dev warmup.\n   */\n  devRenderResumeDataCache?: RenderResumeDataCache\n\n  /**\n   * When true, the page will be rendered using the static rendering to detect\n   * any dynamic API's that would have stopped the page from being fully\n   * statically generated.\n   */\n  isDebugDynamicAccesses?: boolean\n\n  /**\n   * The maximum length of the headers that are emitted by React and added to\n   * the response.\n   */\n  reactMaxHeadersLength: number | undefined\n\n  isStaticGeneration?: boolean\n}\n\nexport type RenderOpts = LoadComponentsReturnType<AppPageModule> &\n  RenderOptsPartial &\n  RequestLifecycleOpts\n\nexport type PreloadCallbacks = (() => void)[]\n\nexport type InitialRSCPayload = {\n  /** buildId */\n  b: string\n  /** assetPrefix */\n  p: string\n  /** initialCanonicalUrlParts */\n  c: string[]\n  /** couldBeIntercepted */\n  i: boolean\n  /** initialFlightData */\n  f: FlightDataPath[]\n  /** missingSlots */\n  m: Set<string> | undefined\n  /** GlobalError */\n  G: [React.ComponentType<any>, React.ReactNode | undefined]\n  /** postponed */\n  s: boolean\n  /** prerendered */\n  S: boolean\n}\n\n// Response from `createFromFetch` for normal rendering\nexport type NavigationFlightResponse = {\n  /** buildId */\n  b: string\n  /** flightData */\n  f: FlightData\n  /** prerendered */\n  S: boolean\n}\n\n// Response from `createFromFetch` for server actions. Action's flight data can be null\nexport type ActionFlightResponse = {\n  /** actionResult */\n  a: ActionResult\n  /** buildId */\n  b: string\n  /** flightData */\n  f: FlightData\n}\n\nexport type RSCPayload =\n  | InitialRSCPayload\n  | NavigationFlightResponse\n  | ActionFlightResponse\n"], "names": ["s", "dynamicParamTypesSchema", "enums", "segmentSchema", "union", "string", "tuple", "flightRouterStateSchema", "record", "lazy", "optional", "nullable", "literal", "boolean"], "mappings": "AAcA,OAAOA,OAAO,iCAAgC;AAe9C,MAAMC,0BAA0BD,EAAEE,KAAK,CAAC;IAAC;IAAK;IAAM;IAAM;IAAK;CAAK;AAIpE,MAAMC,gBAAgBH,EAAEI,KAAK,CAAC;IAC5BJ,EAAEK,MAAM;IACRL,EAAEM,KAAK,CAAC;QAACN,EAAEK,MAAM;QAAIL,EAAEK,MAAM;QAAIJ;KAAwB;CAC1D;AAID,2EAA2E;AAC3E,uFAAuF;AACvF,uBAAuB;AACvB,OAAO,MAAMM,0BAA2CP,EAAEM,KAAK,CAAC;IAC9DH;IACAH,EAAEQ,MAAM,CACNR,EAAEK,MAAM,IACRL,EAAES,IAAI,CAAC,IAAMF;IAEfP,EAAEU,QAAQ,CAACV,EAAEW,QAAQ,CAACX,EAAEK,MAAM;IAC9BL,EAAEU,QAAQ,CACRV,EAAEW,QAAQ,CACRX,EAAEI,KAAK,CAAC;QACNJ,EAAEY,OAAO,CAAC;QACVZ,EAAEY,OAAO,CAAC;QACVZ,EAAEY,OAAO,CAAC;KACX;IAGLZ,EAAEU,QAAQ,CAACV,EAAEa,OAAO;CACrB,EAAC"}