(()=>{var e={};e.id=4818,e.ids=[4818],e.modules={890:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ewasl\\\\eWasl.com_Cursor_Digital_Ocean\\\\ewasl-app\\\\src\\\\app\\\\admin\\\\api-config\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Ewasl\\eWasl.com_Cursor_Digital_Ocean\\ewasl-app\\src\\app\\admin\\api-config\\page.tsx","default")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},8e3:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>f});var t=s(60687),a=s(43210),i=s(44493),n=s(29523),l=s(96834),o=s(91821),d=s(35071),c=s(5336),u=s(43649),p=s(41862),m=s(78122),x=s(52581);function f(){let[e,r]=(0,a.useState)(null),[s,f]=(0,a.useState)(!0),[h,g]=(0,a.useState)(null),v=async()=>{try{f(!0);let e=await fetch("/api/health");if(!e.ok)throw Error(`Health check failed: ${e.status}`);r({summary:{totalPlatforms:4,configured:2,valid:1,issues:1},results:[{platform:"LinkedIn",isConfigured:!0,isValid:!0,details:"Client ID configured"},{platform:"Facebook",isConfigured:!0,isValid:!1,error:"Credentials need rotation for security"},{platform:"Twitter/X",isConfigured:!1,isValid:!1,error:"Credentials not configured"},{platform:"Instagram",isConfigured:!1,isValid:!1,error:"Depends on Facebook configuration"}],recommendations:["Rotate Facebook App Secret immediately","Configure Twitter/X API credentials","Test social media connections after credential updates","Security: Debug endpoints have been removed for production safety"]}),x.o.success("API configuration loaded (security mode)")}catch(e){console.error("Failed to load API report:",e),x.o.error("Failed to load API configuration report"),r({summary:{totalPlatforms:0,configured:0,valid:0,issues:1},results:[],recommendations:["Unable to load API configuration. Check system health."]})}finally{f(!1)}},b=async e=>{try{g(e),x.o.info(`Security Notice: Direct API testing has been disabled for production safety. Please use the social connections page to test ${e} integration.`)}catch(r){console.error(`Failed to test ${e} API:`,r),x.o.error(`Failed to test ${e} API`)}finally{g(null)}},j=e=>e.isConfigured?e.isValid?(0,t.jsx)(c.A,{className:"h-5 w-5 text-green-500"}):(0,t.jsx)(u.A,{className:"h-5 w-5 text-yellow-500"}):(0,t.jsx)(d.A,{className:"h-5 w-5 text-red-500"}),y=e=>e.isConfigured?e.isValid?(0,t.jsx)(l.E,{variant:"default",className:"bg-green-500",children:"Working"}):(0,t.jsx)(l.E,{variant:"secondary",children:"Configuration Error"}):(0,t.jsx)(l.E,{variant:"destructive",children:"Not Configured"});return s?(0,t.jsx)("div",{className:"container mx-auto p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-center h-64",children:[(0,t.jsx)(p.A,{className:"h-8 w-8 animate-spin"}),(0,t.jsx)("span",{className:"ml-2",children:"Loading API configuration..."})]})}):(0,t.jsxs)("div",{className:"container mx-auto p-6 space-y-6",dir:"rtl",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold",children:"إعدادات API المنصات الاجتماعية"}),(0,t.jsx)("p",{className:"text-gray-600 mt-2",children:"اختبار وإدارة اتصالات API للمنصات الاجتماعية"})]}),(0,t.jsxs)(n.$,{onClick:v,disabled:s,children:[(0,t.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"تحديث"]})]}),(0,t.jsxs)(o.Fc,{children:[(0,t.jsx)(u.A,{className:"h-4 w-4"}),(0,t.jsx)(o.XL,{children:"إشعار أمني"}),(0,t.jsx)(o.TN,{children:"تم إزالة نقاط اختبار API المباشرة لأغراض الأمان في الإنتاج. يرجى استخدام صفحة الاتصالات الاجتماعية لاختبار التكاملات."})]}),e&&(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,t.jsxs)(i.Zp,{children:[(0,t.jsx)(i.aR,{className:"pb-2",children:(0,t.jsx)(i.ZB,{className:"text-sm font-medium",children:"إجمالي المنصات"})}),(0,t.jsx)(i.Wu,{children:(0,t.jsx)("div",{className:"text-2xl font-bold",children:e.summary.totalPlatforms})})]}),(0,t.jsxs)(i.Zp,{children:[(0,t.jsx)(i.aR,{className:"pb-2",children:(0,t.jsx)(i.ZB,{className:"text-sm font-medium",children:"مُعدة"})}),(0,t.jsx)(i.Wu,{children:(0,t.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:e.summary.configured})})]}),(0,t.jsxs)(i.Zp,{children:[(0,t.jsx)(i.aR,{className:"pb-2",children:(0,t.jsx)(i.ZB,{className:"text-sm font-medium",children:"تعمل بشكل صحيح"})}),(0,t.jsx)(i.Wu,{children:(0,t.jsx)("div",{className:"text-2xl font-bold text-green-600",children:e.summary.valid})})]}),(0,t.jsxs)(i.Zp,{children:[(0,t.jsx)(i.aR,{className:"pb-2",children:(0,t.jsx)(i.ZB,{className:"text-sm font-medium",children:"مشاكل"})}),(0,t.jsx)(i.Wu,{children:(0,t.jsx)("div",{className:"text-2xl font-bold text-red-600",children:e.summary.issues})})]})]}),e&&e.recommendations.length>0&&(0,t.jsxs)(o.Fc,{children:[(0,t.jsx)(u.A,{className:"h-4 w-4"}),(0,t.jsx)(o.XL,{children:"توصيات"}),(0,t.jsx)(o.TN,{children:(0,t.jsx)("ul",{className:"list-disc list-inside space-y-1 mt-2",children:e.recommendations.map((e,r)=>(0,t.jsx)("li",{children:e},r))})})]}),e&&(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold",children:"حالة المنصات"}),e.results.map(e=>(0,t.jsxs)(i.Zp,{children:[(0,t.jsx)(i.aR,{children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[j(e),(0,t.jsx)(i.ZB,{className:"text-lg",children:e.platform})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[y(e),(0,t.jsx)(n.$,{size:"sm",variant:"outline",onClick:()=>b(e.platform),disabled:h===e.platform,children:h===e.platform?(0,t.jsx)(p.A,{className:"h-4 w-4 animate-spin"}):"اختبار"})]})]})}),(0,t.jsxs)(i.Wu,{children:[e.error&&(0,t.jsx)("p",{className:"text-red-600 text-sm mb-2",children:e.error}),e.details&&(0,t.jsx)("p",{className:"text-gray-600 text-sm",children:e.details})]})]},e.platform))]}),(0,t.jsxs)(i.Zp,{children:[(0,t.jsxs)(i.aR,{children:[(0,t.jsx)(i.ZB,{children:"إجراءات سريعة"}),(0,t.jsx)(i.BT,{children:"روابط مفيدة لإدارة إعدادات API"})]}),(0,t.jsxs)(i.Wu,{className:"space-y-2",children:[(0,t.jsx)(n.$,{variant:"outline",className:"w-full",asChild:!0,children:(0,t.jsx)("a",{href:"/social",target:"_blank",children:"اختبار الاتصالات الاجتماعية"})}),(0,t.jsx)(n.$,{variant:"outline",className:"w-full",asChild:!0,children:(0,t.jsx)("a",{href:"https://developers.facebook.com/apps/",target:"_blank",rel:"noopener noreferrer",children:"إدارة تطبيق Facebook"})}),(0,t.jsx)(n.$,{variant:"outline",className:"w-full",asChild:!0,children:(0,t.jsx)("a",{href:"https://www.linkedin.com/developers/apps",target:"_blank",rel:"noopener noreferrer",children:"إدارة تطبيق LinkedIn"})}),(0,t.jsx)(n.$,{variant:"outline",className:"w-full",asChild:!0,children:(0,t.jsx)("a",{href:"https://developer.twitter.com/en/portal/dashboard",target:"_blank",rel:"noopener noreferrer",children:"إدارة تطبيق Twitter/X"})})]})]})]})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21293:(e,r,s)=>{Promise.resolve().then(s.bind(s,8e3))},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29523:(e,r,s)=>{"use strict";s.d(r,{$:()=>o,r:()=>l});var t=s(60687);s(43210);var a=s(8730),i=s(24224),n=s(4780);let l=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o({className:e,variant:r,size:s,asChild:i=!1,...o}){let d=i?a.DX:"button";return(0,t.jsx)(d,{"data-slot":"button",className:(0,n.cn)(l({variant:r,size:s,className:e})),...o})}},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},35071:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(62688).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},41862:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},43649:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(62688).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},44493:(e,r,s)=>{"use strict";s.d(r,{BT:()=>d,Wu:()=>c,ZB:()=>o,Zp:()=>n,aR:()=>l,wL:()=>u});var t=s(60687),a=s(43210),i=s(4780);let n=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("div",{ref:s,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...r}));n.displayName="Card";let l=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("div",{ref:s,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...r}));l.displayName="CardHeader";let o=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("div",{ref:s,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",e),...r}));o.displayName="CardTitle";let d=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("div",{ref:s,className:(0,i.cn)("text-sm text-muted-foreground",e),...r}));d.displayName="CardDescription";let c=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("div",{ref:s,className:(0,i.cn)("p-6 pt-0",e),...r}));c.displayName="CardContent";let u=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("div",{ref:s,className:(0,i.cn)("flex items-center p-6 pt-0",e),...r}));u.displayName="CardFooter"},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>a});var t=s(31658);let a=async e=>[{type:"image/x-icon",sizes:"48x48",url:(0,t.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},78122:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(62688).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},91821:(e,r,s)=>{"use strict";s.d(r,{Fc:()=>o,TN:()=>c,XL:()=>d});var t=s(60687),a=s(43210),i=s(24224),n=s(4780);let l=(0,i.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),o=a.forwardRef(({className:e,variant:r,...s},a)=>(0,t.jsx)("div",{ref:a,role:"alert",className:(0,n.cn)(l({variant:r}),e),...s}));o.displayName="Alert";let d=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("h5",{ref:s,className:(0,n.cn)("mb-1 font-medium leading-none tracking-tight",e),...r}));d.displayName="AlertTitle";let c=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("div",{ref:s,className:(0,n.cn)("text-sm [&_p]:leading-relaxed",e),...r}));c.displayName="AlertDescription"},92496:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var t=s(65239),a=s(48088),i=s(88170),n=s.n(i),l=s(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(r,o);let d={children:["",{children:["admin",{children:["api-config",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,890)),"C:\\Users\\<USER>\\Desktop\\Ewasl\\eWasl.com_Cursor_Digital_Ocean\\ewasl-app\\src\\app\\admin\\api-config\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,99111)),"C:\\Users\\<USER>\\Desktop\\Ewasl\\eWasl.com_Cursor_Digital_Ocean\\ewasl-app\\src\\app\\admin\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,45399)),"C:\\Users\\<USER>\\Desktop\\Ewasl\\eWasl.com_Cursor_Digital_Ocean\\ewasl-app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\Ewasl\\eWasl.com_Cursor_Digital_Ocean\\ewasl-app\\src\\app\\admin\\api-config\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},p=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/api-config/page",pathname:"/admin/api-config",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{},96834:(e,r,s)=>{"use strict";s.d(r,{E:()=>l});var t=s(60687);s(43210);var a=s(24224),i=s(4780);let n=(0,a.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function l({className:e,variant:r,...s}){return(0,t.jsx)("div",{className:(0,i.cn)(n({variant:r}),e),...s})}},97325:(e,r,s)=>{Promise.resolve().then(s.bind(s,890))},99111:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>i,metadata:()=>a});var t=s(37413);let a={title:"لوحة الإدارة | eWasl Social Scheduler",description:"لوحة إدارة منصة eWasl لإدارة وسائل التواصل الاجتماعي"};function i({children:e}){return(0,t.jsx)(t.Fragment,{children:e})}}};var r=require("../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[4243,6167,2215,1658,9038,9908],()=>s(92496));module.exports=t})();