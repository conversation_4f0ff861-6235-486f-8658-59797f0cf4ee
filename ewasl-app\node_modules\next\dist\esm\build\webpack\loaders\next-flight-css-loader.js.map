{"version": 3, "sources": ["../../../../src/build/webpack/loaders/next-flight-css-loader.ts"], "sourcesContent": ["/**\n * For server-side CSS imports, we need to ignore the actual module content but\n * still trigger the hot-reloading diff mechanism. So here we put the content\n * inside a comment.\n */\n\nimport crypto from 'crypto'\nimport type webpack from 'webpack'\n\ntype NextServerCSSLoaderOptions = {\n  cssModules: boolean\n}\n\nconst NextServerCSSLoader: webpack.LoaderDefinitionFunction<NextServerCSSLoaderOptions> =\n  function (content) {\n    this.cacheable && this.cacheable()\n    const options = this.getOptions()\n    let isCSSModule = options.cssModules\n\n    // Only add the checksum during development.\n    if (process.env.NODE_ENV !== 'production') {\n      // This check is only for backwards compatibility.\n      // TODO: Remove this in the next major version (next 14)\n      if (isCSSModule === undefined) {\n        this.emitWarning(\n          new Error(\n            \"No 'cssModules' option was found for the next-flight-css-loader plugin.\"\n          )\n        )\n        isCSSModule =\n          this.resourcePath.match(/\\.module\\.(css|sass|scss)$/) !== null\n      }\n      const checksum = crypto\n        .createHash('sha1')\n        .update(typeof content === 'string' ? Buffer.from(content) : content)\n        .digest()\n        .toString('hex')\n        .substring(0, 12)\n\n      if (isCSSModule) {\n        return `\\\n${content}\nmodule.exports.__checksum = ${JSON.stringify(checksum)}\n`\n      }\n\n      // Server CSS imports are always available for HMR, so we attach\n      // `module.hot.accept()` to the generated module.\n      const hmrCode = 'if (module.hot) { module.hot.accept() }'\n\n      return `\\\nexport default ${JSON.stringify(checksum)}\n${hmrCode}\n`\n    }\n\n    return content\n  }\n\nexport default NextServerCSSLoader\n"], "names": ["crypto", "NextServerCSSLoader", "content", "cacheable", "options", "getOptions", "isCSSModule", "cssModules", "process", "env", "NODE_ENV", "undefined", "emitWarning", "Error", "resourcePath", "match", "checksum", "createHash", "update", "<PERSON><PERSON><PERSON>", "from", "digest", "toString", "substring", "JSON", "stringify", "hmrCode"], "mappings": "AAAA;;;;CAIC,GAED,OAAOA,YAAY,SAAQ;AAO3B,MAAMC,sBACJ,SAAUC,OAAO;IACf,IAAI,CAACC,SAAS,IAAI,IAAI,CAACA,SAAS;IAChC,MAAMC,UAAU,IAAI,CAACC,UAAU;IAC/B,IAAIC,cAAcF,QAAQG,UAAU;IAEpC,4CAA4C;IAC5C,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;QACzC,kDAAkD;QAClD,wDAAwD;QACxD,IAAIJ,gBAAgBK,WAAW;YAC7B,IAAI,CAACC,WAAW,CACd,qBAEC,CAFD,IAAIC,MACF,4EADF,qBAAA;uBAAA;4BAAA;8BAAA;YAEA;YAEFP,cACE,IAAI,CAACQ,YAAY,CAACC,KAAK,CAAC,kCAAkC;QAC9D;QACA,MAAMC,WAAWhB,OACdiB,UAAU,CAAC,QACXC,MAAM,CAAC,OAAOhB,YAAY,WAAWiB,OAAOC,IAAI,CAAClB,WAAWA,SAC5DmB,MAAM,GACNC,QAAQ,CAAC,OACTC,SAAS,CAAC,GAAG;QAEhB,IAAIjB,aAAa;YACf,OAAO,CAAC;AAChB,EAAEJ,QAAQ;4BACkB,EAAEsB,KAAKC,SAAS,CAACT,UAAU;AACvD,CAAC;QACK;QAEA,gEAAgE;QAChE,iDAAiD;QACjD,MAAMU,UAAU;QAEhB,OAAO,CAAC;eACC,EAAEF,KAAKC,SAAS,CAACT,UAAU;AAC1C,EAAEU,QAAQ;AACV,CAAC;IACG;IAEA,OAAOxB;AACT;AAEF,eAAeD,oBAAmB"}