"use strict";(()=>{var e={};e.id=9006,e.ids=[9006],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{e.exports=require("punycode")},27052:(e,t,o)=>{o.r(t),o.d(t,{patchFetch:()=>v,routeModule:()=>d,serverHooks:()=>x,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>g});var r={};o.r(r),o.d(r,{GET:()=>l,POST:()=>m});var n=o(96559),s=o(48088),a=o(37719),i=o(32190),c=o(32771),u=o(2507),p=o(85535);async function l(e){try{let e=Date.now(),t=await (0,c.bA)(),o=(0,u.createServiceRoleClient)(),{data:r,error:n}=await o.from("posts").select("count").limit(1),s=Date.now()-e,a=await h(),l={status:t.healthy&&!n?"healthy":"unhealthy",timestamp:new Date().toISOString(),responseTime:s,connectionPool:{enabled:(0,c.sJ)().isSupavisorEnabled(),metrics:t.metrics,config:{max:t.config.max,min:t.config.min,connectionTimeout:t.config.connectionTimeoutMillis,idleTimeout:t.config.idleTimeoutMillis}},database:{connected:!n,error:n?.message||null,responseTime:a.responseTime,queryCount:a.queryCount},environment:{nodeEnv:"production",platform:process.env.VERCEL?"vercel":"other",region:process.env.VERCEL_REGION||"unknown"}};return p.v.info("Connection pool health check completed",{component:"connection-pool-health",status:l.status,responseTime:s,poolEnabled:l.connectionPool.enabled}),i.NextResponse.json(l,{status:"healthy"===l.status?200:503,headers:{"Cache-Control":"no-cache, no-store, must-revalidate","Content-Type":"application/json"}})}catch(e){return p.v.error("Connection pool health check failed",{component:"connection-pool-health",error:e instanceof Error?e.message:"Unknown error"}),i.NextResponse.json({status:"error",timestamp:new Date().toISOString(),error:e instanceof Error?e.message:"Health check failed"},{status:500,headers:{"Cache-Control":"no-cache, no-store, must-revalidate","Content-Type":"application/json"}})}}async function h(){let e=Date.now(),t=(0,u.createServiceRoleClient)();try{let o=[t.from("posts").select("count").limit(1),t.from("social_accounts").select("count").limit(1),t.rpc("version")];return await Promise.all(o),{responseTime:Date.now()-e,queryCount:o.length,success:!0}}catch(t){return{responseTime:Date.now()-e,queryCount:0,success:!1,error:t instanceof Error?t.message:"Query test failed"}}}async function m(e){try{let{action:t,config:o}=await e.json();if("update-config"===t)return(0,c.sJ)(),p.v.info("Connection pool configuration update requested",{component:"connection-pool-health",action:t,config:o}),i.NextResponse.json({status:"success",message:"Configuration update logged",timestamp:new Date().toISOString()});if("force-health-check"===t){let e=await (0,c.bA)();return i.NextResponse.json({status:"success",healthCheck:e,timestamp:new Date().toISOString()})}return i.NextResponse.json({status:"error",message:"Unknown action"},{status:400})}catch(e){return p.v.error("Connection pool configuration update failed",{component:"connection-pool-health",error:e instanceof Error?e.message:"Unknown error"}),i.NextResponse.json({status:"error",error:e instanceof Error?e.message:"Configuration update failed"},{status:500})}}let d=new n.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/health/connection-pool/route",pathname:"/api/health/connection-pool",filename:"route",bundlePath:"app/api/health/connection-pool/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Ewasl\\eWasl.com_Cursor_Digital_Ocean\\ewasl-app\\src\\app\\api\\health\\connection-pool\\route.ts",nextConfigOutput:"standalone",userland:r}),{workAsyncStorage:f,workUnitAsyncStorage:g,serverHooks:x}=d;function v(){return(0,a.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:g})}},27910:e=>{e.exports=require("stream")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{e.exports=require("tls")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var o=e=>t(t.s=e),r=t.X(0,[4243,6167,580,4386,1053],()=>o(27052));module.exports=r})();