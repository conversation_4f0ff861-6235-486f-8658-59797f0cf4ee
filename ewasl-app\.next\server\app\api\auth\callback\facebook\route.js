"use strict";(()=>{var e={};e.id=409,e.ids=[409],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{e.exports=require("punycode")},27910:e=>{e.exports=require("stream")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},34631:e=>{e.exports=require("tls")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},74107:(e,t,a)=>{a.r(t),a.d(t,{patchFetch:()=>I,routeModule:()=>G,serverHooks:()=>w,workAsyncStorage:()=>k,workUnitAsyncStorage:()=>E});var o={};a.r(o),a.d(o,{GET:()=>h,POST:()=>_});var r=a(96559),n=a(48088),s=a(37719),c=a(32190),i=a(82087),u=a(2507),l=a(66167),d=a(29021),p=a.n(d),g=a(33873),f=a.n(g);function m(e,t){let a=new Date().toISOString(),o=`[${a}] ${e}${t?"\n"+JSON.stringify(t,null,2):""}

`;try{let e=f().join(process.cwd(),"debug-oauth.log");p().appendFileSync(e,o)}catch(e){console.error("Failed to write to debug log:",e)}}async function h(e){try{let t,a,{searchParams:o}=new URL(e.url),r=o.get("code"),n=o.get("state"),s=o.get("error"),d=o.get("error_description");if(s)return m("Facebook OAuth error:",{error:s,errorDescription:d}),c.NextResponse.redirect(new URL(`/social?error=oauth_error&message=${encodeURIComponent(d||s)}`,e.url));if(!r)return c.NextResponse.redirect(new URL("/social?error=missing_code&message=Authorization code not provided",e.url));let p=(0,i.p)(),g=(0,u.createClient)(),f=(0,l.createClient)("https://nnxfzhxqzmriggulsudr.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY);m("\uD83D\uDD0D DEBUGGING: Exchanging authorization code for access token...");let h=await p.exchangeCodeForToken(r);m("\uD83D\uDD0D DEBUGGING: Token exchange response:",{hasAccessToken:!!h.access_token,tokenType:h.token_type,expiresIn:h.expires_in}),m("\uD83D\uDD0D DEBUGGING: Getting long-lived access token...");let _=await p.getLongLivedToken(h.access_token);m("\uD83D\uDD0D DEBUGGING: Long-lived token response:",{hasAccessToken:!!_.access_token,tokenType:_.token_type,expiresIn:_.expires_in}),m("\uD83D\uDD0D DEBUGGING: Validating token and fetching user info...");let G=await p.validateToken(_.access_token);m("\uD83D\uDD0D DEBUGGING: User info retrieved:",{id:G.id,name:G.name,hasEmail:!!G.email}),m("\uD83D\uDD0D DEBUGGING: Fetching user accounts..."),m("\uD83D\uDD0D DEBUGGING: Using access token:",_.access_token.substring(0,20)+"...");try{t=await p.getUserAccounts(_.access_token),m(`🔍 DEBUGGING: Found ${t.length} accounts:`,t)}catch(t){return m("\uD83D\uDEA8 ERROR: Failed to fetch user accounts:",t),c.NextResponse.redirect(new URL("/social?error=api_error&message=Failed to fetch Facebook accounts",e.url))}if(0===t.length)return m("⚠️ WARNING: No accounts returned from Facebook API"),c.NextResponse.redirect(new URL("/social?error=no_accounts&message=No Facebook accounts found",e.url));if(!n)return c.NextResponse.redirect(new URL("/social?error=missing_state&message=OAuth state parameter missing",e.url));try{let{data:t,error:o}=await g.rpc("validate_oauth_state",{p_state:n,p_platform:"FACEBOOK"});if(o||!t)return m("OAuth state validation failed:",o),c.NextResponse.redirect(new URL("/social?error=invalid_state&message=Invalid or expired OAuth state",e.url));a=t}catch(t){return m("Error validating OAuth state:",t),c.NextResponse.redirect(new URL("/social?error=state_validation_error&message=Failed to validate OAuth state",e.url))}m(`🔍 DEBUGGING: Saving ${t.length} accounts to database...`);let k=0;for(let e=0;e<t.length;e++){let o=t[e];m(`🔍 DEBUGGING: ========== PROCESSING ACCOUNT ${e+1}/${t.length} ==========`),m(`🔍 DEBUGGING: Account details:`,{id:o.id,name:o.accountName,platform:o.platform,hasAccessToken:!!o.accessToken,accessTokenPreview:o.accessToken?o.accessToken.substring(0,20)+"...":"null",metadata:o.metadata,connectionStatus:o.connectionStatus,accountHandle:o.accountHandle});try{let r;m(`🔍 DEBUGGING: Step 1 - Starting account processing for ${o.accountName}...`),m(`🔍 DEBUGGING: Step 2 - Fetching metrics for account ${o.id}...`);let n=o.metadata?.pageId||o.metadata?.instagramAccountId||o.id;m(`🔍 DEBUGGING: Using metrics ID: ${n}`);let s=o.accessToken||_.access_token;m(`🔍 DEBUGGING: Using token: ${s?s.substring(0,20)+"...":"null"}`);try{m(`🔍 DEBUGGING: Calling getAccountMetrics...`),r=await p.getAccountMetrics(n,s,o.platform),m(`🔍 DEBUGGING: ✅ Metrics fetched successfully:`,r)}catch(e){m(`🚨 METRICS ERROR for account ${o.id}:`,e),r={followerCount:0,engagementRate:0,postCount:0},m(`🔍 DEBUGGING: ⚠️ Using default metrics due to error`)}m(`🔍 DEBUGGING: Step 2 completed - Metrics ready`),m(`🔍 DEBUGGING: Step 3 - Preparing account data for database...`);let c={account_id:o.id,user_id:a,platform:o.platform,account_name:o.accountName,access_token:s,account_handle:o.accountHandle||null,connection_status:o.connectionStatus,refresh_token:o.refreshToken||null,expires_at:o.expiresAt?new Date(o.expiresAt).toISOString():null,last_validated_at:o.lastValidatedAt?new Date(o.lastValidatedAt).toISOString():null,metadata:{...o.metadata,metrics:{followerCount:r.followerCount,engagementRate:r.engagementRate,postCount:r.postCount}},profile_image_url:o.profileImageUrl||null,permissions:o.permissions||[]};m(`🔍 DEBUGGING: ✅ Account data prepared successfully`),m(`🔍 DEBUGGING: Account data summary:`,{account_id:c.account_id,user_id:c.user_id,platform:c.platform,account_name:c.account_name,has_access_token:!!c.access_token,connection_status:c.connection_status}),m(`🔍 DEBUGGING: Full account data for database:`,c),m(`🔍 DEBUGGING: Step 4 - Validating required fields...`);let i=["account_id","user_id","platform","account_name","access_token"].filter(e=>!c[e]);if(i.length>0){m(`🚨 VALIDATION ERROR: Missing required fields: ${i.join(", ")}`),m(`🚨 Account data:`,c),m(`🔍 DEBUGGING: ❌ Skipping account ${o.id} due to validation failure`);continue}m(`🔍 DEBUGGING: ✅ All required fields validated successfully`),m(`🔍 DEBUGGING: Step 5 - Attempting to upsert account to database...`),m(`🔍 DEBUGGING: Using service role client for database operation`);let{data:u,error:l}=await f.from("social_accounts").upsert(c,{onConflict:"user_id,platform,account_id"}).select();if(m(`🔍 DEBUGGING: Database operation completed, checking results...`),l)m(`🚨 DATABASE ERROR saving account ${o.id}:`,l),m("\uD83D\uDEA8 Account data that failed:",c),m("\uD83D\uDEA8 Error details:",{code:l.code,message:l.message,details:l.details,hint:l.hint}),m(`🔍 DEBUGGING: ❌ Account ${o.id} failed to save`);else{m(`🔍 DEBUGGING: ✅ Database insertion successful!`),m(`✅ Successfully saved account: ${o.accountName} (${o.platform})`),m(`✅ Upsert result:`,u),k++,m(`🔍 DEBUGGING: ✅ Saved count incremented to: ${k}`),m(`🔍 DEBUGGING: Logging audit entry for account ${o.id}...`);try{let e=await f.from("audit_logs").insert({user_id:a,action:"connect_social_account",resource_type:"social_accounts",resource_ids:[o.id],metadata:{platform:o.platform,account_name:o.accountName,connection_method:"oauth_facebook"}});e.error?m("\uD83D\uDEA8 AUDIT LOG ERROR (non-critical):",e.error):m(`✅ Audit log created for account ${o.id}`)}catch(e){m("\uD83D\uDEA8 AUDIT LOG EXCEPTION (non-critical):",e)}m(`🔍 DEBUGGING: ========== ACCOUNT ${e+1}/${t.length} PROCESSING COMPLETE ==========`),m(`🔍 DEBUGGING: Account ${o.id} result: ${l?"FAILED":"SUCCESS"}`)}}catch(a){m(`🚨 CRITICAL ERROR processing account ${o.accountName} (${o.platform}):`,{error:a,message:a instanceof Error?a.message:String(a),stack:a instanceof Error?a.stack:void 0,accountId:o.id,accountName:o.accountName,platform:o.platform,hasAccessToken:!!o.accessToken}),m(`🔍 DEBUGGING: ========== ACCOUNT ${e+1}/${t.length} PROCESSING FAILED ==========`)}}m(`🔍 DEBUGGING: Final results - Found: ${t.length}, Saved: ${k}`),0===k&&t.length>0&&(m("\uD83D\uDEA8 WARNING: No real Facebook accounts were saved despite being found"),m("\uD83D\uDD0D This suggests an issue in the account processing loop"));let E=k>0?`Successfully connected ${k} account(s)`:`OAuth completed but no accounts were saved (${t.length} found)`;return m(`🔍 DEBUGGING: Redirecting with message: ${E}`),c.NextResponse.redirect(new URL(`/social?success=true&message=${encodeURIComponent(E)}`,e.url))}catch(a){m("Facebook OAuth callback error:",a);let t=a instanceof Error?a.message:"Unknown error occurred";return c.NextResponse.redirect(new URL(`/social?error=callback_error&message=${encodeURIComponent(t)}`,e.url))}}async function _(e){return c.NextResponse.json({error:"Method not allowed"},{status:405})}let G=new r.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/auth/callback/facebook/route",pathname:"/api/auth/callback/facebook",filename:"route",bundlePath:"app/api/auth/callback/facebook/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Ewasl\\eWasl.com_Cursor_Digital_Ocean\\ewasl-app\\src\\app\\api\\auth\\callback\\facebook\\route.ts",nextConfigOutput:"standalone",userland:o}),{workAsyncStorage:k,workUnitAsyncStorage:E,serverHooks:w}=G;function I(){return(0,s.patchFetch)({workAsyncStorage:k,workUnitAsyncStorage:E})}},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},82087:(e,t,a)=>{a.d(t,{p:()=>i});var o=a(55511),r=a.n(o);let n={CONNECTED:"connected"},s="https://graph.facebook.com/v18.0";class c{constructor(e){this.config=e}generateAppSecretProof(e){return r().createHmac("sha256",this.config.appSecret).update(e).digest("hex")}getAuthorizationUrl(e){let t=new URLSearchParams({client_id:this.config.appId,redirect_uri:this.config.redirectUri,scope:"pages_show_list,pages_read_engagement,pages_manage_posts,pages_read_user_content,instagram_basic,instagram_content_publish,business_management",response_type:"code",state:e||""});return`https://www.facebook.com/v18.0/dialog/oauth?${t.toString()}`}async exchangeCodeForToken(e){let t=new URLSearchParams({client_id:this.config.appId,client_secret:this.config.appSecret,redirect_uri:this.config.redirectUri,code:e}),a=await fetch(`${s}/oauth/access_token`,{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:t.toString()});if(!a.ok){let e=await a.json();throw Error(`Facebook OAuth error: ${e.error?.message||"Unknown error"}`)}return a.json()}async getLongLivedToken(e){let t=new URLSearchParams({grant_type:"fb_exchange_token",client_id:this.config.appId,client_secret:this.config.appSecret,fb_exchange_token:e}),a=await fetch(`${s}/oauth/access_token?${t.toString()}`);if(!a.ok){let e=await a.json();throw Error(`Facebook token exchange error: ${e.error?.message||"Unknown error"}`)}return a.json()}async validateToken(e){let t=this.generateAppSecretProof(e),a=await fetch(`${s}/me?fields=id,name,email,picture&access_token=${e}&appsecret_proof=${t}`);if(!a.ok){let e=await a.json();throw Error(`Facebook API error: ${e.error?.message||"Invalid token"}`)}return a.json()}async getUserAccounts(e){try{let t=this.generateAppSecretProof(e),a=await fetch(`${s}/me/accounts?fields=id,name,access_token,category,tasks,instagram_business_account&access_token=${e}&appsecret_proof=${t}`);if(!a.ok)throw Error("Failed to fetch Facebook pages");let o=await a.json(),r=[];for(let e of o.data||[]){let t={id:`fb_${e.id}`,platform:"FACEBOOK",accountName:e.name,accountHandle:`@${e.name.toLowerCase().replace(/\s+/g,"")}`,connectionStatus:n.CONNECTED,accessToken:e.access_token,refreshToken:null,expiresAt:null,lastValidatedAt:new Date,metadata:{pageId:e.id,category:e.category,tasks:e.tasks||[]},permissions:["read","write"],profileImageUrl:`https://graph.facebook.com/${e.id}/picture?type=large`,createdAt:new Date,updatedAt:new Date};if(r.push(t),e.instagram_business_account){let t={id:`ig_${e.instagram_business_account.id}`,platform:"INSTAGRAM",accountName:e.name,accountHandle:`@${e.name.toLowerCase().replace(/\s+/g,"")}`,connectionStatus:n.CONNECTED,accessToken:e.access_token,refreshToken:null,expiresAt:null,lastValidatedAt:new Date,metadata:{instagramAccountId:e.instagram_business_account.id,connectedFacebookPageId:e.id},permissions:["read","write"],profileImageUrl:`https://graph.facebook.com/${e.instagram_business_account.id}/picture?type=large`,createdAt:new Date,updatedAt:new Date};r.push(t)}}return r}catch(e){throw console.error("Error fetching user accounts:",e),e}}async getAccountMetrics(e,t,a){try{let o=this.generateAppSecretProof(t);if("FACEBOOK"===a){let a=await fetch(`${s}/${e}/insights?metric=page_fans,page_post_engagements&access_token=${t}&appsecret_proof=${o}`);if(a.ok){let e=(await a.json()).data||[],t=e.find(e=>"page_fans"===e.name)?.values?.[0]?.value||0,o=e.find(e=>"page_post_engagements"===e.name)?.values?.[0]?.value||0;return{followerCount:t,engagementRate:t>0?o/t*100:0,postCount:0}}}else if("INSTAGRAM"===a){let a=await fetch(`${s}/${e}?fields=followers_count,media_count&access_token=${t}&appsecret_proof=${o}`);if(a.ok){let e=await a.json();return{followerCount:e.followers_count||0,engagementRate:0,postCount:e.media_count||0}}}return{followerCount:0,engagementRate:0,postCount:0}}catch(e){return console.error("Error fetching account metrics:",e),{followerCount:0,engagementRate:0,postCount:0}}}async refreshAccessToken(e){throw Error("Facebook token refresh not implemented - use long-lived tokens")}async revokeToken(e){try{let t=this.generateAppSecretProof(e);return(await fetch(`${s}/me/permissions?access_token=${e}&appsecret_proof=${t}`,{method:"DELETE"})).ok}catch(e){return console.error("Error revoking Facebook token:",e),!1}}}function i(){let e={appId:process.env.FACEBOOK_APP_ID,appSecret:process.env.FACEBOOK_APP_SECRET,businessId:process.env.FACEBOOK_BUSINESS_ID,redirectUri:`${process.env.OAUTH_REDIRECT_URL}/facebook`};if(!e.appId||!e.appSecret)throw Error("Facebook OAuth configuration is missing");return new c(e)}},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")}};var t=require("../../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),o=t.X(0,[4243,6167,580,4386,1053],()=>a(74107));module.exports=o})();