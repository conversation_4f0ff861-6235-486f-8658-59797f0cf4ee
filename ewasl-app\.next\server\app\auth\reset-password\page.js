(()=>{var e={};e.id=4089,e.ids=[4089],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5163:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>P});var t=s(60687),a=s(43210),i=s(27605),l=s(63442),n=s(9275),o=s(16189),d=s(85814),c=s.n(d),u=s(79481),m=s(52581),p=s(29523),x=s(71669),h=s(89667),f=s(44493),g=s(93613),b=s(70334),v=s(5336);let w=(0,s(62688).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]);var y=s(12597),j=s(13861);let N=n.z.object({password:n.z.string().min(8,{message:"كلمة المرور يجب أن تكون 8 أحرف على الأقل"}).regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,{message:"كلمة المرور يجب أن تحتوي على حرف كبير وحرف صغير ورقم"}),confirmPassword:n.z.string()}).refine(e=>e.password===e.confirmPassword,{message:"كلمات المرور غير متطابقة",path:["confirmPassword"]});function k(){let e=(0,o.useRouter)();(0,o.useSearchParams)();let[r,s]=(0,a.useState)(!1),[n,d]=(0,a.useState)(!1),[k,P]=(0,a.useState)(null),[C,_]=(0,a.useState)(!1),[A,R]=(0,a.useState)(!1),[q,z]=(0,a.useState)(null),M=(0,i.mN)({resolver:(0,l.u)(N),defaultValues:{password:"",confirmPassword:""}});async function D(r){s(!0),P(null);try{let s=(0,u.U)(),{error:t}=await s.auth.updateUser({password:r.password});if(t){console.error("Password update error:",t),P("حدث خطأ أثناء تحديث كلمة المرور"),m.o.error("حدث خطأ أثناء تحديث كلمة المرور");return}d(!0),m.o.success("تم تحديث كلمة المرور بنجاح"),setTimeout(()=>{e.push("/auth/signin?message=password-updated")},2e3)}catch(e){console.error("Reset password error:",e),P(e.message||"حدث خطأ أثناء تحديث كلمة المرور"),m.o.error(e.message||"حدث خطأ أثناء تحديث كلمة المرور")}finally{s(!1)}}return null===q?(0,t.jsx)("div",{className:"w-full",dir:"rtl",children:(0,t.jsx)(f.Zp,{children:(0,t.jsx)(f.Wu,{className:"pt-6",children:(0,t.jsxs)("div",{className:"text-center space-y-4",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),(0,t.jsx)("p",{className:"text-gray-600",children:"جاري التحقق من الرابط..."})]})})})}):!1===q?(0,t.jsx)("div",{className:"w-full",dir:"rtl",children:(0,t.jsx)(f.Zp,{className:"border-red-200 bg-red-50",children:(0,t.jsx)(f.Wu,{className:"pt-6",children:(0,t.jsxs)("div",{className:"text-center space-y-4",children:[(0,t.jsx)(g.A,{className:"h-16 w-16 text-red-600 mx-auto"}),(0,t.jsx)("h3",{className:"text-xl font-semibold text-red-800",children:"رابط غير صالح"}),(0,t.jsx)("p",{className:"text-red-700",children:k}),(0,t.jsxs)("div",{className:"pt-4 space-y-2",children:[(0,t.jsx)(c(),{href:"/auth/forgot-password",children:(0,t.jsx)(p.$,{variant:"outline",className:"w-full",children:"طلب رابط جديد"})}),(0,t.jsx)(c(),{href:"/auth/signin",children:(0,t.jsxs)(p.$,{variant:"ghost",className:"w-full",children:[(0,t.jsx)(b.A,{className:"ml-2 h-4 w-4"}),"العودة إلى تسجيل الدخول"]})})]})]})})})}):n?(0,t.jsx)("div",{className:"w-full",dir:"rtl",children:(0,t.jsx)(f.Zp,{className:"border-green-200 bg-green-50",children:(0,t.jsx)(f.Wu,{className:"pt-6",children:(0,t.jsxs)("div",{className:"text-center space-y-4",children:[(0,t.jsx)(v.A,{className:"h-16 w-16 text-green-600 mx-auto"}),(0,t.jsx)("h3",{className:"text-xl font-semibold text-green-800",children:"تم تحديث كلمة المرور!"}),(0,t.jsx)("p",{className:"text-green-700",children:"تم تحديث كلمة المرور بنجاح. سيتم توجيهك إلى صفحة تسجيل الدخول."}),(0,t.jsx)("div",{className:"pt-4",children:(0,t.jsx)(c(),{href:"/auth/signin",children:(0,t.jsxs)(p.$,{className:"w-full",children:[(0,t.jsx)(b.A,{className:"ml-2 h-4 w-4"}),"الانتقال إلى تسجيل الدخول"]})})})]})})})}):(0,t.jsx)("div",{className:"w-full",dir:"rtl",children:(0,t.jsx)(x.lV,{...M,children:(0,t.jsxs)("form",{onSubmit:M.handleSubmit(D),className:"space-y-6",children:[(0,t.jsx)(x.zB,{control:M.control,name:"password",render:({field:e})=>(0,t.jsxs)(x.eI,{children:[(0,t.jsx)(x.lR,{className:"text-gray-700 font-medium",children:"كلمة المرور الجديدة"}),(0,t.jsx)(x.MJ,{children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(h.p,{type:C?"text":"password",placeholder:"أدخل كلمة المرور الجديدة",className:"h-12 border-gray-200 focus:border-blue-500 focus:ring-blue-500 transition-colors pl-10 pr-10",...e}),(0,t.jsx)(w,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5"}),(0,t.jsx)("button",{type:"button",onClick:()=>_(!C),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors",children:C?(0,t.jsx)(y.A,{className:"h-5 w-5"}):(0,t.jsx)(j.A,{className:"h-5 w-5"})})]})}),(0,t.jsx)(x.C5,{})]})}),(0,t.jsx)(x.zB,{control:M.control,name:"confirmPassword",render:({field:e})=>(0,t.jsxs)(x.eI,{children:[(0,t.jsx)(x.lR,{className:"text-gray-700 font-medium",children:"تأكيد كلمة المرور"}),(0,t.jsx)(x.MJ,{children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(h.p,{type:A?"text":"password",placeholder:"أدخل كلمة المرور مرة أخرى",className:"h-12 border-gray-200 focus:border-blue-500 focus:ring-blue-500 transition-colors pl-10 pr-10",...e}),(0,t.jsx)(w,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5"}),(0,t.jsx)("button",{type:"button",onClick:()=>R(!A),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors",children:A?(0,t.jsx)(y.A,{className:"h-5 w-5"}):(0,t.jsx)(j.A,{className:"h-5 w-5"})})]})}),(0,t.jsx)(x.C5,{})]})}),k&&(0,t.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,t.jsx)("p",{className:"text-red-600 text-sm text-center",children:k})}),(0,t.jsx)(p.$,{type:"submit",className:"w-full h-12 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl",disabled:r,children:r?"جاري التحديث...":"تحديث كلمة المرور"}),(0,t.jsx)("div",{className:"text-center",children:(0,t.jsxs)(c(),{href:"/auth/signin",className:"text-blue-600 hover:text-blue-700 font-medium transition-colors duration-200 inline-flex items-center",children:[(0,t.jsx)(b.A,{className:"ml-2 h-4 w-4"}),"العودة إلى تسجيل الدخول"]})})]})})})}function P(){return(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-purple-50 flex items-center justify-center p-4",children:(0,t.jsxs)("div",{className:"w-full max-w-lg",children:[(0,t.jsxs)("div",{className:"text-center mb-10",children:[(0,t.jsx)("div",{className:"w-20 h-20 bg-gradient-to-r from-blue-600 to-purple-600 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-2xl",children:(0,t.jsx)("span",{className:"text-3xl font-bold text-white",children:"eW"})}),(0,t.jsx)("h1",{className:"text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-3",children:"eWasl"}),(0,t.jsx)("p",{className:"text-gray-600 text-lg",children:"منصة إدارة وسائل التواصل الاجتماعي"}),(0,t.jsx)("p",{className:"text-sm text-gray-500 mt-2",children:"إعادة تعيين كلمة المرور"})]}),(0,t.jsxs)("div",{className:"bg-white/80 backdrop-blur-xl rounded-3xl shadow-2xl p-10 border border-white/50",children:[(0,t.jsxs)("div",{className:"text-center mb-8",children:[(0,t.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-3",children:"إعادة تعيين كلمة المرور"}),(0,t.jsx)("p",{className:"text-gray-600 text-lg",children:"أدخل كلمة المرور الجديدة"})]}),(0,t.jsx)(a.Suspense,{fallback:(0,t.jsx)("div",{children:"جاري التحميل..."}),children:(0,t.jsx)(k,{})})]})]})})}},5336:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},12597:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(62688).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},13861:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},16189:(e,r,s)=>{"use strict";var t=s(65773);s.o(t,"useParams")&&s.d(r,{useParams:function(){return t.useParams}}),s.o(t,"usePathname")&&s.d(r,{usePathname:function(){return t.usePathname}}),s.o(t,"useRouter")&&s.d(r,{useRouter:function(){return t.useRouter}}),s.o(t,"useSearchParams")&&s.d(r,{useSearchParams:function(){return t.useSearchParams}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29523:(e,r,s)=>{"use strict";s.d(r,{$:()=>o,r:()=>n});var t=s(60687);s(43210);var a=s(8730),i=s(24224),l=s(4780);let n=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o({className:e,variant:r,size:s,asChild:i=!1,...o}){let d=i?a.DX:"button";return(0,t.jsx)(d,{"data-slot":"button",className:(0,l.cn)(n({variant:r,size:s,className:e})),...o})}},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},44493:(e,r,s)=>{"use strict";s.d(r,{BT:()=>d,Wu:()=>c,ZB:()=>o,Zp:()=>l,aR:()=>n,wL:()=>u});var t=s(60687),a=s(43210),i=s(4780);let l=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("div",{ref:s,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...r}));l.displayName="Card";let n=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("div",{ref:s,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...r}));n.displayName="CardHeader";let o=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("div",{ref:s,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",e),...r}));o.displayName="CardTitle";let d=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("div",{ref:s,className:(0,i.cn)("text-sm text-muted-foreground",e),...r}));d.displayName="CardDescription";let c=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("div",{ref:s,className:(0,i.cn)("p-6 pt-0",e),...r}));c.displayName="CardContent";let u=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("div",{ref:s,className:(0,i.cn)("flex items-center p-6 pt-0",e),...r}));u.displayName="CardFooter"},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},59379:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ewasl\\\\eWasl.com_Cursor_Digital_Ocean\\\\ewasl-app\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Ewasl\\eWasl.com_Cursor_Digital_Ocean\\ewasl-app\\src\\app\\auth\\reset-password\\page.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63460:(e,r,s)=>{Promise.resolve().then(s.bind(s,5163))},70334:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(62688).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},70440:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>a});var t=s(31658);let a=async e=>[{type:"image/x-icon",sizes:"48x48",url:(0,t.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},71669:(e,r,s)=>{"use strict";s.d(r,{C5:()=>b,MJ:()=>f,Rr:()=>g,eI:()=>x,lR:()=>h,lV:()=>d,zB:()=>u});var t=s(60687),a=s(43210),i=s(8730),l=s(27605),n=s(4780),o=s(80013);let d=l.Op,c=a.createContext({}),u=({...e})=>(0,t.jsx)(c.Provider,{value:{name:e.name},children:(0,t.jsx)(l.xI,{...e})}),m=()=>{let e=a.useContext(c),r=a.useContext(p),{getFieldState:s,formState:t}=(0,l.xW)(),i=s(e.name,t);if(!e)throw Error("useFormField should be used within <FormField>");let{id:n}=r;return{id:n,name:e.name,formItemId:`${n}-form-item`,formDescriptionId:`${n}-form-item-description`,formMessageId:`${n}-form-item-message`,...i}},p=a.createContext({}),x=a.forwardRef(({className:e,...r},s)=>{let i=a.useId();return(0,t.jsx)(p.Provider,{value:{id:i},children:(0,t.jsx)("div",{ref:s,className:(0,n.cn)("space-y-2",e),...r})})});x.displayName="FormItem";let h=a.forwardRef(({className:e,...r},s)=>{let{error:a,formItemId:i}=m();return(0,t.jsx)(o.J,{ref:s,className:(0,n.cn)(a&&"text-destructive",e),htmlFor:i,...r})});h.displayName="FormLabel";let f=a.forwardRef(({...e},r)=>{let{error:s,formItemId:a,formDescriptionId:l,formMessageId:n}=m();return(0,t.jsx)(i.DX,{ref:r,id:a,"aria-describedby":s?`${l} ${n}`:`${l}`,"aria-invalid":!!s,...e})});f.displayName="FormControl";let g=a.forwardRef(({className:e,...r},s)=>{let{formDescriptionId:a}=m();return(0,t.jsx)("p",{ref:s,id:a,className:(0,n.cn)("text-sm text-muted-foreground",e),...r})});g.displayName="FormDescription";let b=a.forwardRef(({className:e,children:r,...s},a)=>{let{error:i,formMessageId:l}=m(),o=i?String(i?.message??""):r;return o?(0,t.jsx)("p",{ref:a,id:l,className:(0,n.cn)("text-sm font-medium text-destructive",e),...s,children:o}):null});b.displayName="FormMessage"},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},80013:(e,r,s)=>{"use strict";s.d(r,{J:()=>d});var t=s(60687),a=s(43210),i=s(78148),l=s(24224),n=s(4780);let o=(0,l.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)(i.b,{ref:s,className:(0,n.cn)(o(),e),...r}));d.displayName=i.b.displayName},81630:e=>{"use strict";e.exports=require("http")},82020:(e,r,s)=>{Promise.resolve().then(s.bind(s,59379))},87146:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>l.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var t=s(65239),a=s(48088),i=s(88170),l=s.n(i),n=s(30893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);s.d(r,o);let d={children:["",{children:["auth",{children:["reset-password",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,59379)),"C:\\Users\\<USER>\\Desktop\\Ewasl\\eWasl.com_Cursor_Digital_Ocean\\ewasl-app\\src\\app\\auth\\reset-password\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,45399)),"C:\\Users\\<USER>\\Desktop\\Ewasl\\eWasl.com_Cursor_Digital_Ocean\\ewasl-app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\Ewasl\\eWasl.com_Cursor_Digital_Ocean\\ewasl-app\\src\\app\\auth\\reset-password\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/auth/reset-password/page",pathname:"/auth/reset-password",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},89667:(e,r,s)=>{"use strict";s.d(r,{p:()=>l});var t=s(60687),a=s(43210),i=s(4780);let l=a.forwardRef(({className:e,type:r,...s},a)=>(0,t.jsx)("input",{type:r,className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:a,...s}));l.displayName="Input"},91645:e=>{"use strict";e.exports=require("net")},93613:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(62688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[4243,6167,2215,1658,5814,7825,9038,9908],()=>s(87146));module.exports=t})();