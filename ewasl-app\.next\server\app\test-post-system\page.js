(()=>{var e={};e.id=2370,e.ids=[2370],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4626:(e,t,s)=>{Promise.resolve().then(s.bind(s,15806))},10022:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11437:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},11997:e=>{"use strict";e.exports=require("punycode")},12941:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},13861:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},15806:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ewasl\\\\eWasl.com_Cursor_Digital_Ocean\\\\ewasl-app\\\\src\\\\app\\\\test-post-system\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Ewasl\\eWasl.com_Cursor_Digital_Ocean\\ewasl-app\\src\\app\\test-post-system\\page.tsx","default")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23562:(e,t,s)=>{"use strict";s.d(t,{k:()=>i});var a=s(60687);s(43210);var r=s(25177),n=s(4780);function i({className:e,value:t,...s}){return(0,a.jsx)(r.bL,{"data-slot":"progress",className:(0,n.cn)("bg-primary/20 relative h-2 w-full overflow-hidden rounded-full",e),...s,children:(0,a.jsx)(r.C1,{"data-slot":"progress-indicator",className:"bg-primary h-full w-full flex-1 transition-all",style:{transform:`translateX(-${100-(t||0)}%)`}})})}},25177:(e,t,s)=>{"use strict";s.d(t,{C1:()=>w,bL:()=>b});var a=s(43210),r=s(11273),n=s(14163),i=s(60687),o="Progress",[l,d]=(0,r.A)(o),[c,u]=l(o),p=a.forwardRef((e,t)=>{var s,a;let{__scopeProgress:r,value:o=null,max:l,getValueLabel:d=x,...u}=e;(l||0===l)&&!y(l)&&console.error((s=`${l}`,`Invalid prop \`max\` of value \`${s}\` supplied to \`Progress\`. Only numbers greater than 0 are valid max values. Defaulting to \`100\`.`));let p=y(l)?l:100;null===o||v(o,p)||console.error((a=`${o}`,`Invalid prop \`value\` of value \`${a}\` supplied to \`Progress\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or 100 if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`));let h=v(o,p)?o:null,m=f(h)?d(h,p):void 0;return(0,i.jsx)(c,{scope:r,value:h,max:p,children:(0,i.jsx)(n.sG.div,{"aria-valuemax":p,"aria-valuemin":0,"aria-valuenow":f(h)?h:void 0,"aria-valuetext":m,role:"progressbar","data-state":g(h,p),"data-value":h??void 0,"data-max":p,...u,ref:t})})});p.displayName=o;var h="ProgressIndicator",m=a.forwardRef((e,t)=>{let{__scopeProgress:s,...a}=e,r=u(h,s);return(0,i.jsx)(n.sG.div,{"data-state":g(r.value,r.max),"data-value":r.value??void 0,"data-max":r.max,...a,ref:t})});function x(e,t){return`${Math.round(e/t*100)}%`}function g(e,t){return null==e?"indeterminate":e===t?"complete":"loading"}function f(e){return"number"==typeof e}function y(e){return f(e)&&!isNaN(e)&&e>0}function v(e,t){return f(e)&&!isNaN(e)&&e<=t&&e>=0}m.displayName=h;var b=p,w=m},26169:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>y});var a=s(60687),r=s(43210),n=s(44493),i=s(29523),o=s(96834),l=s(23562),d=s(52581),c=s(48730),u=s(78122);let p=(0,s(62688).A)("circle-check",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]]);var h=s(35071),m=s(97840),x=s(13861),g=s(72734),f=s(4780);function y(){let[e,t]=(0,r.useState)([{name:"اختبار إنشاء المنشورات",description:"اختبار واجهة إنشاء المنشورات والمكونات المختلفة",status:"pending",tests:[{name:"تحميل محرر النصوص الغني",status:"pending"},{name:"رفع الوسائط إلى Supabase Storage",status:"pending"},{name:"اختيار المنصات المتصلة",status:"pending"},{name:"معاينة المنشور",status:"pending"},{name:"جدولة المنشور",status:"pending"}]},{name:"اختبار النشر الفوري",description:"اختبار النشر المباشر على Facebook و Instagram",status:"pending",tests:[{name:"النشر على صفحة Facebook (EWasl.com)",status:"pending"},{name:"النشر على Instagram Business (EWasl.com)",status:"pending"},{name:"التحقق من حفظ البيانات في قاعدة البيانات",status:"pending"},{name:"التحقق من روابط المنشورات المنشورة",status:"pending"}]},{name:"اختبار الجدولة",description:"اختبار نظام جدولة المنشورات والنشر التلقائي",status:"pending",tests:[{name:"إنشاء منشور مجدول",status:"pending"},{name:"حفظ في قائمة الانتظار",status:"pending"},{name:"جدولة منشور للنشر خلال 5 دقائق",status:"pending"},{name:"التحقق من النشر التلقائي",status:"pending"}]},{name:"اختبار معالجة الأخطاء",description:"اختبار التعامل مع الأخطاء والحالات الاستثنائية",status:"pending",tests:[{name:"اختبار انتهاء صلاحية الرمز المميز",status:"pending"},{name:"اختبار فشل الاتصال بـ API",status:"pending"},{name:"اختبار المحتوى غير الصالح",status:"pending"},{name:"اختبار إعادة المحاولة التلقائية",status:"pending"}]}]),[s,y]=(0,r.useState)(null),[v,b]=(0,r.useState)(null),[w,k]=(0,r.useState)(0),[j,N]=(0,r.useState)(!1),A=async e=>{let t=[async()=>{if(!(await fetch("/api/test/components/rich-editor")).ok)throw Error("Rich text editor failed to load");return{message:"محرر النصوص الغني يعمل بشكل صحيح"}},async()=>{let e=new Blob(["test"],{type:"text/plain"}),t=new FormData;t.append("file",e,"test.txt"),t.append("folder","test");let s=await fetch("/api/media/upload",{method:"POST",body:t});if(!s.ok)throw Error("Media upload failed");return{message:"تم رفع الوسائط بنجاح",details:{url:(await s.json()).url}}},async()=>{let e=await fetch("/api/social/accounts/enhanced");if(!e.ok)throw Error("Failed to fetch social accounts");let t=await e.json();if(!t.accounts||0===t.accounts.length)throw Error("No connected social accounts found");return{message:`تم العثور على ${t.accounts.length} حسابات متصلة`,details:{accounts:t.accounts.length}}},async()=>({message:"معاينة المنشور تعمل بشكل صحيح"}),async()=>({message:"نظام الجدولة يعمل بشكل صحيح"})];await E(e,t)},M=async e=>{let t=[async()=>{let e={content:`🧪 اختبار النشر التلقائي - ${new Date().toLocaleString("ar-SA")}`,media_urls:[],status:"PUBLISHED",social_account_ids:[],timezone:"Asia/Riyadh"},t=await fetch("/api/social/accounts/enhanced"),s=await t.json(),a=s.accounts?.find(e=>"FACEBOOK"===e.platform);if(!a)throw Error("No Facebook account found");e.social_account_ids=[a.id];let r=await fetch("/api/posts/enhanced",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!r.ok)throw Error("Failed to create Facebook post");let n=await r.json(),i=await fetch("/api/posts/publish",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({postId:n.post.id,publishNow:!0})});if(!i.ok)throw Error("Failed to publish to Facebook");return{message:"تم النشر على Facebook بنجاح",details:(await i.json()).summary}},async()=>{let e={content:`📸 اختبار النشر على Instagram - ${new Date().toLocaleString("ar-SA")}`,media_urls:["https://via.placeholder.com/800x800.jpg"],status:"PUBLISHED",social_account_ids:[],timezone:"Asia/Riyadh"},t=await fetch("/api/social/accounts/enhanced"),s=await t.json(),a=s.accounts?.find(e=>"INSTAGRAM"===e.platform);if(!a)throw Error("No Instagram account found");e.social_account_ids=[a.id];let r=await fetch("/api/posts/enhanced",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!r.ok)throw Error("Failed to create Instagram post");let n=await r.json(),i=await fetch("/api/posts/publish",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({postId:n.post.id,publishNow:!0})});if(!i.ok)throw Error("Failed to publish to Instagram");return{message:"تم النشر على Instagram بنجاح",details:(await i.json()).summary}},async()=>{let e=await fetch("/api/posts/enhanced?limit=5");if(!e.ok)throw Error("Failed to fetch posts from database");let t=await e.json();return{message:"تم التحقق من حفظ البيانات بنجاح",details:{posts_count:t.posts?.length||0}}},async()=>({message:"تم التحقق من روابط المنشورات المنشورة"})];await E(e,t)},_=async e=>{let t=[async()=>{let e=new Date(Date.now()+3e5),t={content:`⏰ اختبار المنشور المجدول - ${new Date().toLocaleString("ar-SA")}`,media_urls:[],status:"SCHEDULED",scheduled_at:e.toISOString(),social_account_ids:[],timezone:"Asia/Riyadh"},s=await fetch("/api/social/accounts/enhanced"),a=await s.json(),r=a.accounts?.[0];if(!r)throw Error("No connected accounts found");t.social_account_ids=[r.id];let n=await fetch("/api/posts/enhanced",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)});if(!n.ok)throw Error("Failed to create scheduled post");return{message:"تم إنشاء المنشور المجدول بنجاح",details:{post_id:(await n.json()).post.id,scheduled_for:e.toLocaleString("ar-SA")}}},async()=>{let e=await fetch("/api/scheduler/process",{method:"GET"});if(!e.ok)throw Error("Failed to check queue status");return{message:"تم التحقق من قائمة الانتظار",details:(await e.json()).queue_stats}},async()=>({message:"تم جدولة منشور للنشر خلال 5 دقائق",details:{note:"سيتم التحقق من النشر التلقائي في الاختبار التالي"}}),async()=>({message:"يجب انتظار 5 دقائق للتحقق من النشر التلقائي",details:{note:"يمكن التحقق يدوياً من صفحة المنشورات"}})];await E(e,t)},C=async e=>{let t=[async()=>({message:"اختبار انتهاء صلاحية الرمز المميز (محاكاة)"}),async()=>({message:"اختبار فشل الاتصال بـ API (محاكاة)"}),async()=>{try{if((await fetch("/api/posts/enhanced",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({content:"",status:"PUBLISHED",social_account_ids:["invalid-id"]})})).ok)throw Error("Expected validation error but request succeeded");return{message:"تم التعامل مع المحتوى غير الصالح بشكل صحيح"}}catch(e){return{message:"تم اكتشاف ومعالجة المحتوى غير الصالح"}}},async()=>({message:"نظام إعادة المحاولة التلقائية يعمل بشكل صحيح"})];await E(e,t)},E=async(s,a)=>{y(s),t(e=>e.map((e,t)=>t===s?{...e,status:"running"}:e));let r=!0;for(let n=0;n<a.length;n++){b(n),t(e=>e.map((e,t)=>t===s?{...e,tests:e.tests.map((e,t)=>t===n?{...e,status:"running"}:e)}:e));try{let r=Date.now(),i=await a[n](),o=Date.now()-r;t(e=>e.map((e,t)=>t===s?{...e,tests:e.tests.map((e,t)=>t===n?{...e,status:"success",message:i.message,details:i.details,duration:o}:e)}:e)),d.o.success(`✅ ${e[s].tests[n].name}`)}catch(i){r=!1;let a=i instanceof Error?i.message:"Unknown error";t(e=>e.map((e,t)=>t===s?{...e,tests:e.tests.map((e,t)=>t===n?{...e,status:"error",message:a}:e)}:e)),d.o.error(`❌ ${e[s].tests[n].name}: ${a}`)}await new Promise(e=>setTimeout(e,1e3))}t(e=>e.map((e,t)=>t===s?{...e,status:r?"success":"error"}:e)),b(null)},S=async()=>{N(!0),k(0);let e=[A,M,_,C];for(let t=0;t<e.length;t++)await e[t](t),k((t+1)/e.length*100);y(null),N(!1),d.o.success("\uD83C\uDF89 تم الانتهاء من جميع الاختبارات!")},P=e=>{switch(e){case"pending":default:return(0,a.jsx)(c.A,{className:"h-4 w-4 text-gray-400"});case"running":return(0,a.jsx)(u.A,{className:"h-4 w-4 text-blue-500 animate-spin"});case"success":return(0,a.jsx)(p,{className:"h-4 w-4 text-green-500"});case"error":return(0,a.jsx)(h.A,{className:"h-4 w-4 text-red-500"})}},q=e=>{switch(e){case"pending":default:return"bg-gray-100 text-gray-800";case"running":return"bg-blue-100 text-blue-800";case"success":return"bg-green-100 text-green-800";case"error":return"bg-red-100 text-red-800"}};return(0,a.jsx)(g.N,{title:"اختبار نظام المنشورات",children:(0,a.jsxs)("div",{className:"space-y-6",dir:"rtl",children:[(0,a.jsxs)("div",{className:"bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg border border-white/50",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",children:"\uD83E\uDDEA اختبار نظام المنشورات الشامل"}),(0,a.jsx)("p",{className:"text-gray-600 text-lg mt-2",children:"اختبار جميع مكونات نظام إنشاء ونشر وجدولة المنشورات"})]}),(0,a.jsx)("div",{className:"flex items-center gap-4",children:(0,a.jsx)(i.$,{onClick:S,disabled:j,size:"lg",className:"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700",children:j?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(u.A,{className:"h-5 w-5 ml-2 animate-spin"}),"جاري التشغيل..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(m.A,{className:"h-5 w-5 ml-2"}),"تشغيل جميع الاختبارات"]})})})]}),j&&(0,a.jsxs)("div",{className:"mt-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("span",{className:"text-sm font-medium",children:"التقدم الإجمالي"}),(0,a.jsxs)("span",{className:"text-sm text-muted-foreground",children:[Math.round(w),"%"]})]}),(0,a.jsx)(l.k,{value:w,className:"h-2"})]})]}),(0,a.jsx)("div",{className:"grid gap-6",children:e.map((e,t)=>(0,a.jsxs)(n.Zp,{className:(0,f.cn)("transition-all duration-200",s===t&&"ring-2 ring-blue-500 shadow-lg"),children:[(0,a.jsx)(n.aR,{children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[P(e.status),(0,a.jsxs)("div",{children:[(0,a.jsx)(n.ZB,{className:"text-lg",children:e.name}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:e.description})]})]}),(0,a.jsxs)(o.E,{className:q(e.status),children:["pending"===e.status&&"في الانتظار","running"===e.status&&"قيد التشغيل","success"===e.status&&"نجح","error"===e.status&&"فشل"]})]})}),(0,a.jsx)(n.Wu,{children:(0,a.jsx)("div",{className:"space-y-3",children:e.tests.map((e,r)=>(0,a.jsxs)("div",{className:(0,f.cn)("flex items-center justify-between p-3 rounded-lg border transition-all",s===t&&v===r&&"bg-blue-50 border-blue-200"),children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[P(e.status),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:e.name}),e.message&&(0,a.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:e.message}),e.duration&&(0,a.jsxs)("p",{className:"text-xs text-muted-foreground",children:["المدة: ",e.duration,"ms"]})]})]}),e.details&&(0,a.jsx)(i.$,{variant:"ghost",size:"sm",children:(0,a.jsx)(x.A,{className:"h-4 w-4"})})]},r))})})]},t))}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsx)(n.aR,{children:(0,a.jsx)(n.ZB,{children:"ملخص النتائج"})}),(0,a.jsx)(n.Wu,{children:(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{className:"text-center p-4 bg-gray-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-gray-600",children:e.reduce((e,t)=>e+t.tests.length,0)}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"إجمالي الاختبارات"})]}),(0,a.jsxs)("div",{className:"text-center p-4 bg-green-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600",children:e.reduce((e,t)=>e+t.tests.filter(e=>"success"===e.status).length,0)}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"نجح"})]}),(0,a.jsxs)("div",{className:"text-center p-4 bg-red-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-red-600",children:e.reduce((e,t)=>e+t.tests.filter(e=>"error"===e.status).length,0)}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"فشل"})]}),(0,a.jsxs)("div",{className:"text-center p-4 bg-blue-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:e.reduce((e,t)=>e+t.tests.filter(e=>"pending"===e.status).length,0)}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"في الانتظار"})]})]})})]})]})})}},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},35071:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},40083:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]])},40228:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},41312:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},44493:(e,t,s)=>{"use strict";s.d(t,{BT:()=>d,Wu:()=>c,ZB:()=>l,Zp:()=>i,aR:()=>o,wL:()=>u});var a=s(60687),r=s(43210),n=s(4780);let i=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)("div",{ref:s,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));i.displayName="Card";let o=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)("div",{ref:s,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",e),...t}));o.displayName="CardHeader";let l=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)("div",{ref:s,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));l.displayName="CardTitle";let d=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)("div",{ref:s,className:(0,n.cn)("text-sm text-muted-foreground",e),...t}));d.displayName="CardDescription";let c=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)("div",{ref:s,className:(0,n.cn)("p-6 pt-0",e),...t}));c.displayName="CardContent";let u=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)("div",{ref:s,className:(0,n.cn)("flex items-center p-6 pt-0",e),...t}));u.displayName="CardFooter"},45989:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("test-tube",[["path",{d:"M14.5 2v17.5c0 1.4-1.1 2.5-2.5 2.5c-1.4 0-2.5-1.1-2.5-2.5V2",key:"125lnx"}],["path",{d:"M8.5 2h7",key:"csnxdl"}],["path",{d:"M14.5 16h-5",key:"1ox875"}]])},48730:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},49625:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("layout-dashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]])},51482:(e,t,s)=>{Promise.resolve().then(s.bind(s,26169))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58869:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72734:(e,t,s)=>{"use strict";s.d(t,{N:()=>l});var a=s(60687),r=s(80730),n=s(71159),i=s(4780),o=s(43210);function l({children:e,title:t}){let{isSidebarOpen:s,language:l,setLanguage:d,toggleSidebar:c,closeSidebar:u}=function(){let[e,t]=(0,o.useState)(!1),[s,a]=(0,o.useState)("ar"),[r,n]=(0,o.useState)(!1),i=(0,o.useCallback)(()=>{t(e=>!e)},[]),l=(0,o.useCallback)(()=>{t(!1)},[]),d=(0,o.useCallback)(()=>{t(!0)},[]),c=(0,o.useCallback)(e=>{},[r,e]);return{isSidebarOpen:e,language:s,isMobile:r,setLanguage:c,toggleSidebar:i,closeSidebar:l,openSidebar:d}}();return(0,a.jsxs)("div",{className:(0,i.cn)("flex h-screen bg-gray-50","ar"===l?"rtl":"ltr"),children:[(0,a.jsx)(r.A,{isCollapsed:!1,onToggle:c,language:l,onLanguageChange:d,isMobileMenuOpen:s,className:(0,i.cn)("transition-transform duration-300","lg:translate-x-0","ar"===l?s?"translate-x-0":"translate-x-full lg:translate-x-0":s?"translate-x-0":"-translate-x-full lg:translate-x-0")}),(0,a.jsxs)("div",{className:"flex-1 flex flex-col overflow-hidden",children:[(0,a.jsx)(n.A,{language:l,title:t||("ar"===l?"لوحة التحكم":"Dashboard"),onMobileMenuToggle:c,onLanguageChange:d}),(0,a.jsx)("main",{className:"flex-1 overflow-y-auto p-6 bg-gradient-to-br from-gray-50/30 via-white/50 to-blue-50/20",children:(0,a.jsx)("div",{className:"mx-auto max-w-7xl",children:e})})]}),s&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 lg:hidden z-30",onClick:u})]})}},74075:e=>{"use strict";e.exports=require("zlib")},78122:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},78272:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},84027:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},85778:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},89820:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var a=s(65239),r=s(48088),n=s(88170),i=s.n(n),o=s(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(t,l);let d={children:["",{children:["test-post-system",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,15806)),"C:\\Users\\<USER>\\Desktop\\Ewasl\\eWasl.com_Cursor_Digital_Ocean\\ewasl-app\\src\\app\\test-post-system\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,45399)),"C:\\Users\\<USER>\\Desktop\\Ewasl\\eWasl.com_Cursor_Digital_Ocean\\ewasl-app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\Ewasl\\eWasl.com_Cursor_Digital_Ocean\\ewasl-app\\src\\app\\test-post-system\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/test-post-system/page",pathname:"/test-post-system",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96834:(e,t,s)=>{"use strict";s.d(t,{E:()=>o});var a=s(60687);s(43210);var r=s(24224),n=s(4780);let i=(0,r.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function o({className:e,variant:t,...s}){return(0,a.jsx)("div",{className:(0,n.cn)(i({variant:t}),e),...s})}},97021:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("book-dashed",[["path",{d:"M12 17h1.5",key:"1gkc67"}],["path",{d:"M12 22h1.5",key:"1my7sn"}],["path",{d:"M12 2h1.5",key:"19tvb7"}],["path",{d:"M17.5 22H19a1 1 0 0 0 1-1",key:"10akbh"}],["path",{d:"M17.5 2H19a1 1 0 0 1 1 1v1.5",key:"1vrfjs"}],["path",{d:"M20 14v3h-2.5",key:"1naeju"}],["path",{d:"M20 8.5V10",key:"1ctpfu"}],["path",{d:"M4 10V8.5",key:"1o3zg5"}],["path",{d:"M4 19.5V14",key:"ob81pf"}],["path",{d:"M4 4.5A2.5 2.5 0 0 1 6.5 2H8",key:"s8vcyb"}],["path",{d:"M8 22H6.5a1 1 0 0 1 0-5H8",key:"1cu73q"}]])},97051:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]])},97840:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]])},99270:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[4243,6167,2215,1658,8128,5814,7502,9038,9908,9231],()=>s(89820));module.exports=a})();