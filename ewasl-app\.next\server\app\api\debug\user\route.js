(()=>{var e={};e.id=3455,e.ids=[3455],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},24473:(e,t,r)=>{"use strict";r.d(t,{FL:()=>u,WX:()=>c,vD:()=>n});var s=r(34386),o=r(44999),i=r(32190);async function a(){try{let e="https://nnxfzhxqzmriggulsudr.supabase.co",t="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5ueGZ6aHhxem1yaWdndWxzdWRyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIwNjg3NjgsImV4cCI6MjA2NzY0NDc2OH0.1qBlXLOqGtJX_LFRC7XihOpwCtD1U1I2_jdyJ8sq_8w";if(e&&t&&!e.includes("placeholder"))return{url:e,key:t};return console.warn("API Auth: Using fallback Supabase configuration"),{url:"https://nnxfzhxqzmriggulsudr.supabase.co",key:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5ueGZ6aHhxem1yaWdndWxzdWRyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIwNjg3NjgsImV4cCI6MjA2NzY0NDc2OH0.1qBlXLOqGtJX_LFRC7XihOpwCtD1U1I2_jdyJ8sq_8w"}}catch(e){return console.error("Error getting Supabase config in API auth:",e),{url:"https://nnxfzhxqzmriggulsudr.supabase.co",key:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5ueGZ6aHhxem1yaWdndWxzdWRyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIwNjg3NjgsImV4cCI6MjA2NzY0NDc2OH0.1qBlXLOqGtJX_LFRC7XihOpwCtD1U1I2_jdyJ8sq_8w"}}}async function n(e){try{let e=await (0,o.cookies)(),t=await a(),r=(0,s.createServerClient)(t.url,t.key,{cookies:{get:t=>e.get(t)?.value,set(t,r,s){try{e.set(t,r,s)}catch(e){console.warn("Cannot set cookie in API route:",t)}},remove(t,r){try{e.set(t,"",{...r,maxAge:0})}catch(e){console.warn("Cannot remove cookie in API route:",t)}}}}),{data:{user:i},error:n}=await r.auth.getUser();if(n)throw console.error("Authentication error in API route:",n),Error(`Authentication failed: ${n.message}`);if(!i)throw Error("No authenticated user found");return{user:i,supabase:r}}catch(e){throw console.error("❌ API Authentication failed:",e),e}}function u(e,t=200){return i.NextResponse.json({success:!0,data:e,timestamp:new Date().toISOString()},{status:t})}function c(e,t=500,r){return i.NextResponse.json({success:!1,error:e,details:r,timestamp:new Date().toISOString()},{status:t})}},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63341:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>m,routeModule:()=>p,serverHooks:()=>I,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>d});var s={};r.r(s),r.d(s,{GET:()=>c});var o=r(96559),i=r(48088),a=r(37719),n=r(32190),u=r(24473);async function c(e){try{let{user:t,supabase:r}=await (0,u.vD)(e),{data:s,error:o}=await r.from("social_accounts").select("*").eq("user_id",t.id),{data:i,error:a}=await r.from("social_accounts").select("*").eq("user_id",t.id).eq("platform","FACEBOOK"),{data:c,error:p}=await r.from("posts").select("*").limit(1),{data:l,error:d}=await r.from("post_social_accounts").select("*").limit(1);return n.NextResponse.json({user:{id:t.id,email:t.email},socialAccounts:s||[],facebookAccounts:i||[],accountsError:o,fbError:a,testPost:c||[],testPostError:p,testPostSocialAccounts:l||[],testPostSocialAccountsError:d,debug:{totalAccounts:s?.length||0,activeFacebookAccounts:i?.length||0,postsTableTest:p?"ERROR":"OK",postSocialAccountsTableTest:d?"ERROR":"OK"}})}catch(e){return console.error("Debug API error:",e),n.NextResponse.json({error:e instanceof Error?e.message:"Unknown error"},{status:500})}}let p=new o.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/debug/user/route",pathname:"/api/debug/user",filename:"route",bundlePath:"app/api/debug/user/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Ewasl\\eWasl.com_Cursor_Digital_Ocean\\ewasl-app\\src\\app\\api\\debug\\user\\route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:d,serverHooks:I}=p;function m(){return(0,a.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:d})}},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4243,6167,580,4386,4999],()=>r(63341));module.exports=s})();