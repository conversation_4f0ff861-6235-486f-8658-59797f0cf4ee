(()=>{var e={};e.id=9271,e.ids=[9271],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},28046:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>u,tree:()=>l});var r=t(65239),a=t(48088),n=t(88170),i=t.n(n),o=t(30893),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);t.d(s,d);let l={children:["",{children:["debug-post",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,52791)),"C:\\Users\\<USER>\\Desktop\\Ewasl\\eWasl.com_Cursor_Digital_Ocean\\ewasl-app\\src\\app\\debug-post\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,45399)),"C:\\Users\\<USER>\\Desktop\\Ewasl\\eWasl.com_Cursor_Digital_Ocean\\ewasl-app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\Ewasl\\eWasl.com_Cursor_Digital_Ocean\\ewasl-app\\src\\app\\debug-post\\page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/debug-post/page",pathname:"/debug-post",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},52791:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ewasl\\\\eWasl.com_Cursor_Digital_Ocean\\\\ewasl-app\\\\src\\\\app\\\\debug-post\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Ewasl\\eWasl.com_Cursor_Digital_Ocean\\ewasl-app\\src\\app\\debug-post\\page.tsx","default")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67401:(e,s,t)=>{Promise.resolve().then(t.bind(t,94897))},70440:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});var r=t(31658);let a=async e=>[{type:"image/x-icon",sizes:"48x48",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},77129:(e,s,t)=>{Promise.resolve().then(t.bind(t,52791))},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},94897:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var r=t(60687),a=t(43210);function n(){let[e,s]=(0,a.useState)(null),[t,n]=(0,a.useState)(!1),i=async()=>{n(!0),s(null);try{let e=await fetch("/api/debug-post-creation",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({content:"\uD83D\uDD0D DEBUG TEST - Step-by-step post creation analysis\n\nTesting each step individually to isolate the exact issue.\n\nTimestamp: "+new Date().toISOString(),platforms:["facebook"],status:"PUBLISHED"})}),t=await e.json();s({status:e.status,data:t})}catch(e){s({status:"ERROR",data:{error:e.message}})}finally{n(!1)}},o=e=>{switch(e){case"success":return"text-green-600 bg-green-100";case"failed":return"text-red-600 bg-red-100";case"starting":return"text-yellow-600 bg-yellow-100";default:return"text-gray-600 bg-gray-100"}};return(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 p-8",children:(0,r.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold mb-8",children:"\uD83D\uDD0D Post Creation Debug Tool"}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 mb-6",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Step-by-Step Analysis"}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"This tool will test each step of the post creation process individually to identify exactly where the error occurs."}),(0,r.jsx)("button",{onClick:i,disabled:t,className:"bg-blue-500 hover:bg-blue-600 disabled:bg-gray-400 text-white px-6 py-2 rounded-lg font-medium",children:t?"Running Debug Test...":"Run Step-by-Step Debug Test"})]}),e&&(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Debug Results"}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("span",{className:"font-medium",children:"Overall Status: "}),(0,r.jsx)("span",{className:`px-2 py-1 rounded text-sm ${200===e.status?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:e.status}),void 0!==e.data.success&&(0,r.jsx)("span",{className:`ml-2 px-2 py-1 rounded text-sm ${e.data.success?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:e.data.success?"SUCCESS":"FAILED"})]}),e.data.error&&(0,r.jsxs)("div",{className:"mb-4 p-3 bg-red-50 border border-red-200 rounded",children:[(0,r.jsx)("h4",{className:"font-medium text-red-800 mb-2",children:"Error:"}),(0,r.jsx)("p",{className:"text-red-700",children:e.data.error})]}),e.data.steps&&(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("h4",{className:"font-medium mb-3",children:"Step-by-Step Results:"}),(0,r.jsx)("div",{className:"space-y-3",children:e.data.steps.map((e,s)=>(0,r.jsxs)("div",{className:"border rounded p-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,r.jsxs)("span",{className:"font-medium",children:["Step ",e.step,": ",e.name]}),(0,r.jsx)("span",{className:`px-2 py-1 rounded text-xs ${o(e.status)}`,children:e.status.toUpperCase()})]}),e.data&&(0,r.jsx)("div",{className:"mt-2 p-2 bg-gray-50 rounded text-sm",children:(0,r.jsx)("pre",{children:JSON.stringify(e.data,null,2)})}),e.error&&(0,r.jsxs)("div",{className:"mt-2 p-2 bg-red-50 border border-red-200 rounded text-sm",children:[(0,r.jsx)("span",{className:"font-medium text-red-800",children:"Error: "}),(0,r.jsx)("span",{className:"text-red-700",children:e.error})]})]},s))})]}),(0,r.jsxs)("div",{className:"bg-gray-100 rounded p-4 overflow-auto",children:[(0,r.jsx)("h4",{className:"font-medium mb-2",children:"Full Response:"}),(0,r.jsx)("pre",{className:"text-sm",children:JSON.stringify(e.data,null,2)})]})]})]})})}}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[4243,6167,2215,1658,9038,9908],()=>t(28046));module.exports=r})();