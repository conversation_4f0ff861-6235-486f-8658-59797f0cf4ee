"use strict";(()=>{var e={};e.id=3894,e.ids=[3894],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4360:(e,t,a)=>{a.d(t,{A:()=>o});class s{static{this.ERROR_CODES={OAUTH_INVALID_CLIENT:{code:"OAUTH_INVALID_CLIENT",message:"Invalid OAuth client configuration",userMessage:"There's a configuration issue with the social media connection. Please contact support.",recoverable:!1,action:"Contact support for OAuth configuration"},OAUTH_ACCESS_DENIED:{code:"OAUTH_ACCESS_DENIED",message:"User denied OAuth authorization",userMessage:"Authorization was cancelled. You can try connecting again.",recoverable:!0,action:"Try connecting to the platform again"},OAUTH_INVALID_GRANT:{code:"OAUTH_INVALID_GRANT",message:"Invalid authorization grant",userMessage:"The authorization has expired. Please reconnect your account.",recoverable:!0,action:"Reconnect your social media account"},OAUTH_EXPIRED_TOKEN:{code:"OAUTH_EXPIRED_TOKEN",message:"Access token has expired",userMessage:"Your social media connection has expired. Please reconnect your account.",recoverable:!0,action:"Reconnect your social media account"},FACEBOOK_APP_NOT_LIVE:{code:"FACEBOOK_APP_NOT_LIVE",message:"Facebook app is in development mode",userMessage:"Facebook integration is currently in testing mode. Please contact support.",recoverable:!1,action:"Contact support to activate Facebook integration"},FACEBOOK_INVALID_PERMISSIONS:{code:"FACEBOOK_INVALID_PERMISSIONS",message:"Insufficient Facebook permissions",userMessage:"Additional permissions are needed for Facebook posting. Please reconnect with full permissions.",recoverable:!0,action:"Reconnect Facebook with all requested permissions"},LINKEDIN_RATE_LIMIT:{code:"LINKEDIN_RATE_LIMIT",message:"LinkedIn API rate limit exceeded",userMessage:"LinkedIn posting limit reached. Please wait a few minutes before trying again.",recoverable:!0,action:"Wait 15 minutes and try again"},TWITTER_DUPLICATE_POST:{code:"TWITTER_DUPLICATE_POST",message:"Duplicate content detected by Twitter",userMessage:"This content appears to be a duplicate. Please modify your post and try again.",recoverable:!0,action:"Modify your post content to make it unique"},NO_BUSINESS_ACCOUNTS:{code:"NO_BUSINESS_ACCOUNTS",message:"No business accounts found",userMessage:"No business accounts were found for this platform. Make sure you're an admin of business pages or organizations.",recoverable:!0,action:"Connect business accounts or check admin permissions"},BUSINESS_ACCOUNT_INACTIVE:{code:"BUSINESS_ACCOUNT_INACTIVE",message:"Selected business account is inactive",userMessage:"The selected business account is no longer active. Please choose a different account.",recoverable:!0,action:"Select a different business account"},PUBLISH_CONTENT_TOO_LONG:{code:"PUBLISH_CONTENT_TOO_LONG",message:"Content exceeds platform character limit",userMessage:"Your post is too long for this platform. Please shorten it and try again.",recoverable:!0,action:"Reduce post length to fit platform limits"},PUBLISH_MEDIA_INVALID:{code:"PUBLISH_MEDIA_INVALID",message:"Invalid media format or size",userMessage:"The attached media is not supported or too large. Please use a different file.",recoverable:!0,action:"Use supported media formats (JPG, PNG, MP4) under size limits"},PUBLISH_NETWORK_ERROR:{code:"PUBLISH_NETWORK_ERROR",message:"Network error during publishing",userMessage:"There was a connection issue while posting. Please check your internet and try again.",recoverable:!0,action:"Check internet connection and retry"},UNKNOWN_ERROR:{code:"UNKNOWN_ERROR",message:"An unexpected error occurred",userMessage:"Something went wrong. Our team has been notified and will investigate.",recoverable:!1,action:"Contact support if the issue persists"}}}static handleError(e,t,a){let s=new Date().toISOString(),o=this.ERROR_CODES.UNKNOWN_ERROR,r=e?.message||e?.error||"",n=e?.status||e?.statusCode;r.includes("invalid_client")||r.includes("Invalid client")?o=this.ERROR_CODES.OAUTH_INVALID_CLIENT:r.includes("access_denied")?o=this.ERROR_CODES.OAUTH_ACCESS_DENIED:r.includes("invalid_grant")||401===n?o=this.ERROR_CODES.OAUTH_INVALID_GRANT:r.includes("expired")||r.includes("token")?o=this.ERROR_CODES.OAUTH_EXPIRED_TOKEN:"facebook"===a?r.includes("development mode")||r.includes("not live")?o=this.ERROR_CODES.FACEBOOK_APP_NOT_LIVE:(r.includes("permission")||403===n)&&(o=this.ERROR_CODES.FACEBOOK_INVALID_PERMISSIONS):"linkedin"===a?(429===n||r.includes("rate limit"))&&(o=this.ERROR_CODES.LINKEDIN_RATE_LIMIT):"twitter"===a||"x"===a?(r.includes("duplicate")||403===n)&&(o=this.ERROR_CODES.TWITTER_DUPLICATE_POST):"tiktok"===a?429===n||r.includes("rate limit")?o=this.ERROR_CODES.LINKEDIN_RATE_LIMIT:(r.includes("video")||r.includes("media"))&&(o=this.ERROR_CODES.PUBLISH_MEDIA_INVALID):"snapchat"===a?429===n||r.includes("rate limit")?o=this.ERROR_CODES.LINKEDIN_RATE_LIMIT:(r.includes("media")||r.includes("file"))&&(o=this.ERROR_CODES.PUBLISH_MEDIA_INVALID):r.includes("no business accounts")?o=this.ERROR_CODES.NO_BUSINESS_ACCOUNTS:r.includes("inactive account")?o=this.ERROR_CODES.BUSINESS_ACCOUNT_INACTIVE:r.includes("too long")||r.includes("character limit")?o=this.ERROR_CODES.PUBLISH_CONTENT_TOO_LONG:r.includes("media")||r.includes("file")?o=this.ERROR_CODES.PUBLISH_MEDIA_INVALID:(r.includes("network")||r.includes("timeout"))&&(o=this.ERROR_CODES.PUBLISH_NETWORK_ERROR);let i={...o,platform:a,details:{originalError:e,context:t,timestamp:s,stackTrace:e?.stack}};return this.logError(i,t),i}static logError(e,t){console.error("\uD83D\uDEA8 Social Media Error:",{code:e.code,message:e.message,userMessage:e.userMessage,platform:e.platform,recoverable:e.recoverable,action:e.action,context:t,timestamp:new Date().toISOString()}),this.sendToMonitoring(e,t)}static sendToMonitoring(e,t){}static getUserMessage(e){return{message:e.userMessage,action:e.action,recoverable:e.recoverable}}static isRecoverable(e){return e.recoverable}static getPlatformLimits(e){return({twitter:{maxLength:280,maxMedia:4,supportedFormats:["jpg","jpeg","png","gif","mp4"]},x:{maxLength:280,maxMedia:4,supportedFormats:["jpg","jpeg","png","gif","mp4"]},facebook:{maxLength:63206,maxMedia:10,supportedFormats:["jpg","jpeg","png","gif","mp4","mov"]},linkedin:{maxLength:3e3,maxMedia:20,supportedFormats:["jpg","jpeg","png","gif","mp4"]},instagram:{maxLength:2200,maxMedia:10,supportedFormats:["jpg","jpeg","png","mp4"]},tiktok:{maxLength:2200,maxMedia:1,supportedFormats:["mp4","mov","jpg","jpeg","png"]},snapchat:{maxLength:5e3,maxMedia:1,supportedFormats:["jpg","jpeg","png","mp4"]}})[e.toLowerCase()]||{maxLength:280,maxMedia:1,supportedFormats:["jpg","jpeg","png"]}}}let o=s},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{e.exports=require("punycode")},27910:e=>{e.exports=require("stream")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33328:(e,t,a)=>{a.r(t),a.d(t,{patchFetch:()=>f,routeModule:()=>m,serverHooks:()=>E,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>I});var s={};a.r(s),a.d(s,{GET:()=>_,POST:()=>h,dynamic:()=>u});var o=a(96559),r=a(48088),n=a(37719),i=a(32190),c=a(53955),l=a(2507),p=a(4360);let u="force-dynamic";async function d(e){try{let{searchParams:t}=new URL(e.url),a=t.get("state");if(a)try{let e=JSON.parse(Buffer.from(a,"base64url").toString());if(e.userId)return e.userId}catch(e){}return null}catch(e){return console.error("❌ Error getting authenticated user ID:",e),null}}async function _(e){try{let{searchParams:t}=new URL(e.url),a=t.get("code");t.get("state");let s=t.get("error"),o=t.get("error_description");if(s){let e=p.A.handleError({error:s,errorDescription:o},{platform:"snapchat",operation:"oauth_callback",timestamp:new Date().toISOString()},"snapchat");return i.NextResponse.redirect(`https://app.ewasl.com/social?error=${e.code}&message=${encodeURIComponent(e.userMessage)}`)}if(!a)return i.NextResponse.redirect(`https://app.ewasl.com/social?error=missing_code&message=${encodeURIComponent("Authorization code is missing")}`);let r=(0,c.getOAuthConfig)("snapchat");if(!r||!r.enabled)return i.NextResponse.redirect(`https://app.ewasl.com/social?error=oauth_not_configured&message=${encodeURIComponent("Snapchat integration is not configured")}`);let n=Buffer.from(`${r.clientId}:${r.clientSecret}`).toString("base64"),u=await fetch(r.tokenUrl,{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded",Authorization:`Basic ${n}`},body:new URLSearchParams({grant_type:"authorization_code",code:a,redirect_uri:r.redirectUri})});if(!u.ok){let e=await u.text();console.error("Snapchat token exchange failed:",u.status,e);let t=p.A.handleError({status:u.status,message:e},{platform:"snapchat",operation:"token_exchange",timestamp:new Date().toISOString()},"snapchat");return i.NextResponse.redirect(`https://app.ewasl.com/social?error=${t.code}&message=${encodeURIComponent(t.userMessage)}`)}let _=await u.json(),h=await fetch("https://adsapi.snapchat.com/v1/me",{headers:{Authorization:`Bearer ${_.access_token}`,"Content-Type":"application/json"}});if(!h.ok){let e=await h.text();console.error("Snapchat profile fetch failed:",h.status,e);let t=p.A.handleError({status:h.status,message:e},{platform:"snapchat",operation:"profile_fetch",timestamp:new Date().toISOString()},"snapchat");return i.NextResponse.redirect(`https://app.ewasl.com/social?error=${t.code}&message=${encodeURIComponent(t.userMessage)}`)}let m=await h.json(),g=(0,l.createServiceRoleClient)(),I=await d(e);I||(console.warn("⚠️ No authenticated user found, using demo user as fallback"),I="3ddaeb03-2d95-4fff-abad-2a2c7dd25037");let{data:E,error:f}=await g.from("social_accounts").upsert({user_id:I,platform:"SNAPCHAT",account_id:m.me.id,account_name:m.me.display_name||m.me.email,profile_picture:null,access_token:_.access_token,refresh_token:_.refresh_token||null,expires_at:_.expires_in?new Date(Date.now()+1e3*_.expires_in).toISOString():null,updated_at:new Date().toISOString()},{onConflict:"user_id,platform,account_id"});if(f){console.error("Failed to store Snapchat account:",f);let e=p.A.handleError(f,{platform:"snapchat",userId:I,operation:"store_account",timestamp:new Date().toISOString()},"snapchat");return i.NextResponse.redirect(`https://app.ewasl.com/social?error=${e.code}&message=${encodeURIComponent(e.userMessage)}`)}if("true"===t.get("popup"))return new i.NextResponse(`
        <!DOCTYPE html>
        <html>
        <head>
          <title>Snapchat Connected</title>
        </head>
        <body>
          <script>
            if (window.opener) {
              window.opener.postMessage({
                type: 'oauth_success',
                platform: 'snapchat',
                account: '${(m.me.display_name||m.me.email).replace(/'/g,"\\'")}',
                userId: '${I}',
                message: 'Snapchat account connected successfully'
              }, 'https://app.ewasl.com');
            }
            window.close();
          </script>
          <div style="text-align: center; padding: 50px; font-family: Arial, sans-serif;">
            <h2>✅ Snapchat Connected Successfully!</h2>
            <p>Account: ${m.me.display_name||m.me.email}</p>
            <p>This window will close automatically...</p>
          </div>
        </body>
        </html>
      `,{headers:{"Content-Type":"text/html"}});return i.NextResponse.redirect(`https://app.ewasl.com/social?success=snapchat_connected&account=${encodeURIComponent(m.me.display_name||m.me.email)}`)}catch(t){console.error("Snapchat OAuth callback error:",t);let e=p.A.handleError(t,{platform:"snapchat",operation:"oauth_callback",timestamp:new Date().toISOString()},"snapchat");return i.NextResponse.redirect(`https://app.ewasl.com/social?error=${e.code}&message=${encodeURIComponent(e.userMessage)}`)}}async function h(e){return i.NextResponse.json({error:"Method not allowed for OAuth callback"},{status:405})}let m=new o.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/snapchat/callback/route",pathname:"/api/snapchat/callback",filename:"route",bundlePath:"app/api/snapchat/callback/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Ewasl\\eWasl.com_Cursor_Digital_Ocean\\ewasl-app\\src\\app\\api\\snapchat\\callback\\route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:g,workUnitAsyncStorage:I,serverHooks:E}=m;function f(){return(0,n.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:I})}},34631:e=>{e.exports=require("tls")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},53955:(e,t,a)=>{a.r(t),a.d(t,{getAuthorizationUrl:()=>l,getEnabledPlatforms:()=>c,getOAuthConfig:()=>n,getOAuthConfigs:()=>r,getPlatformSummary:()=>u,isPlatformEnabled:()=>i,validateOAuthConfig:()=>p});var s=a(55511),o=a.n(s);function r(){let e="https://app.ewasl.com",t=[];return["X_CLIENT_ID","X_CLIENT_SECRET","FACEBOOK_APP_ID","FACEBOOK_APP_SECRET","LINKEDIN_CLIENT_ID","LINKEDIN_CLIENT_SECRET"].forEach(e=>{process.env[e]||(t.push(e),console.warn(`[OAuth Config] Missing environment variable: ${e}`))}),t.length>0&&console.warn(`[OAuth Config] Missing ${t.length} environment variables: ${t.join(", ")}`),{twitter:{clientId:process.env.X_CLIENT_ID||process.env.TWITTER_CLIENT_ID||"",clientSecret:process.env.X_CLIENT_SECRET||process.env.TWITTER_CLIENT_SECRET||"",redirectUri:`${e}/api/x/callback`,scope:["tweet.read","tweet.write","users.read","media.upload","offline.access"],authUrl:"https://x.com/i/oauth2/authorize",tokenUrl:"https://api.x.com/2/oauth2/token",userInfoUrl:"https://api.x.com/2/users/me",enabled:!!(process.env.X_CLIENT_ID||process.env.TWITTER_CLIENT_ID)},x:{clientId:process.env.X_CLIENT_ID||"",clientSecret:process.env.X_CLIENT_SECRET||"",redirectUri:`${e}/api/x/callback`,scope:["tweet.read","tweet.write","users.read","follows.read","follows.write","offline.access"],authUrl:"https://x.com/i/oauth2/authorize",tokenUrl:"https://api.x.com/2/oauth2/token",userInfoUrl:"https://api.x.com/2/users/me",enabled:!!(process.env.X_CLIENT_ID&&process.env.X_CLIENT_SECRET)},facebook:{clientId:process.env.FACEBOOK_APP_ID||"",clientSecret:process.env.FACEBOOK_APP_SECRET||"",redirectUri:`${e}/api/facebook/callback`,scope:["email","public_profile","pages_show_list","pages_manage_posts","pages_read_engagement","business_management"],authUrl:"https://www.facebook.com/v19.0/dialog/oauth",tokenUrl:"https://graph.facebook.com/v19.0/oauth/access_token",userInfoUrl:"https://graph.facebook.com/v19.0/me",enabled:!!(process.env.FACEBOOK_APP_ID&&process.env.FACEBOOK_APP_SECRET)},instagram:{clientId:process.env.FACEBOOK_APP_ID||"",clientSecret:process.env.FACEBOOK_APP_SECRET||"",redirectUri:`${e}/api/facebook/callback`,scope:["instagram_graph_user_profile","instagram_graph_user_media","instagram_content_publish","pages_show_list","pages_read_engagement","business_management"],authUrl:"https://www.facebook.com/v19.0/dialog/oauth",tokenUrl:"https://graph.facebook.com/v19.0/oauth/access_token",userInfoUrl:"https://graph.facebook.com/v19.0/me",enabled:!!(process.env.FACEBOOK_APP_ID&&process.env.FACEBOOK_APP_SECRET)},linkedin:{clientId:process.env.LINKEDIN_CLIENT_ID||"",clientSecret:process.env.LINKEDIN_CLIENT_SECRET||"",redirectUri:`${e}/api/linkedin/callback`,scope:["openid","profile","w_member_social","email","w_organization_social","r_organization_social"],authUrl:"https://www.linkedin.com/oauth/v2/authorization",tokenUrl:"https://www.linkedin.com/oauth/v2/accessToken",userInfoUrl:"https://api.linkedin.com/v2/userinfo",enabled:!!(process.env.LINKEDIN_CLIENT_ID&&process.env.LINKEDIN_CLIENT_SECRET)},tiktok:{clientId:process.env.TIKTOK_CLIENT_ID||"",clientSecret:process.env.TIKTOK_CLIENT_SECRET||"",redirectUri:`${e}/api/tiktok/callback`,scope:["user.info.basic","video.publish","video.upload"],authUrl:"https://www.tiktok.com/v2/auth/authorize/",tokenUrl:"https://open.tiktokapis.com/v2/oauth/token/",userInfoUrl:"https://open.tiktokapis.com/v2/user/info/",enabled:!!(process.env.TIKTOK_CLIENT_ID&&process.env.TIKTOK_CLIENT_SECRET)},snapchat:{clientId:process.env.SNAPCHAT_CLIENT_ID||"",clientSecret:process.env.SNAPCHAT_CLIENT_SECRET||"",redirectUri:`${e}/api/snapchat/callback`,scope:["snapchat-marketing-api","snapchat-content-api"],authUrl:"https://accounts.snapchat.com/login/oauth2/authorize",tokenUrl:"https://accounts.snapchat.com/login/oauth2/access_token",userInfoUrl:"https://adsapi.snapchat.com/v1/me",enabled:!!(process.env.SNAPCHAT_CLIENT_ID&&process.env.SNAPCHAT_CLIENT_SECRET)}}}function n(e){return r()[e.toLowerCase()]||null}function i(e){let t=n(e);return!!t&&t.enabled}function c(){let e=r();return Object.keys(e).filter(t=>e[t].enabled)}function l(e,t){let a=n(e);if(!a||!a.enabled)return null;let s=new URLSearchParams({client_id:a.clientId,redirect_uri:a.redirectUri,scope:a.scope.join(" "),response_type:"code",...t&&{state:t}});switch(e.toLowerCase()){case"twitter":s.set("code_challenge","challenge"),s.set("code_challenge_method","plain");break;case"x":try{let e=o().randomBytes(32).toString("base64url"),t=o().createHash("sha256").update(e).digest("base64url");s.set("code_challenge",t),s.set("code_challenge_method","S256"),"undefined"!=typeof sessionStorage&&sessionStorage.setItem("x_code_verifier",e)}catch(e){console.error("Failed to generate PKCE challenge for X:",e),s.set("code_challenge","demo_code_challenge_for_testing"),s.set("code_challenge_method","S256")}break;case"facebook":case"instagram":case"linkedin":s.set("response_type","code")}return`${a.authUrl}?${s.toString()}`}function p(e){let t=n(e),a=[],s=[];return t?(t.clientId||a.push(`Missing client ID for ${e}`),t.clientSecret||a.push(`Missing client secret for ${e}`),t.redirectUri||a.push(`Missing redirect URI for ${e}`),t.scope&&0!==t.scope.length||s.push(`No scopes defined for ${e}`),t.enabled||s.push(`Platform ${e} is not enabled`),{valid:0===a.length,errors:a,warnings:s}):(a.push(`Platform ${e} is not supported`),{valid:!1,errors:a,warnings:s})}function u(){return Object.entries(r()).map(([e,t])=>({platform:e,enabled:t.enabled,configured:!!(t.clientId&&t.clientSecret),hasCredentials:!!(t.clientId&&t.clientSecret),scopes:t.scope}))}},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),s=t.X(0,[4243,6167,580,4386,1053],()=>a(33328));module.exports=s})();