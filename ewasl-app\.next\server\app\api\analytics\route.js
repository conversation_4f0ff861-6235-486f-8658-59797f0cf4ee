(()=>{var e={};e.id=3070,e.ids=[3070],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8514:(e,t,a)=>{"use strict";a.r(t),a.d(t,{patchFetch:()=>w,routeModule:()=>h,serverHooks:()=>f,workAsyncStorage:()=>_,workUnitAsyncStorage:()=>g});var s={};a.r(s),a.d(s,{GET:()=>d,POST:()=>m});var r=a(96559),n=a(48088),i=a(37719),o=a(32190),c=a(24473),l=a(53957);class u{async getAllAccountsAnalytics(e){let t=await this.connectionService.getUserConnections(e),a=[];for(let s of t)try{let t=await this.getAccountAnalytics(e,s.platform,s.accountId);t&&a.push(t)}catch(e){console.error(`Failed to get analytics for ${s.platform}:`,e)}return a}async getAccountAnalytics(e,t,a){let s=await this.connectionService.getValidToken(e,t,a);if(!s)throw Error("No valid token available");switch(t.toLowerCase()){case"facebook":return this.getFacebookAnalytics(s,a);case"instagram":return this.getInstagramAnalytics(s,a);case"twitter":return this.getTwitterAnalytics(s,a);case"linkedin":return this.getLinkedInAnalytics(s,a);default:throw Error(`Unsupported platform: ${t}`)}}async getFacebookAnalytics(e,t){let a="https://graph.facebook.com/v19.0",s=await fetch(`${a}/${t}?fields=name,followers_count,fan_count&access_token=${e}`),r=await s.json(),n=await fetch(`${a}/${t}/insights?metric=page_impressions,page_reach,page_engaged_users,page_post_engagements&period=day&since=${this.getDateDaysAgo(30)}&access_token=${e}`),i=await n.json(),o=await fetch(`${a}/${t}/posts?fields=id,message,created_time,likes.summary(true),comments.summary(true),shares&limit=10&access_token=${e}`),c=(await o.json()).data||[],l=c.reduce((e,t)=>e+(t.likes?.summary?.total_count||0),0),u=c.reduce((e,t)=>e+(t.comments?.summary?.total_count||0),0),p=c.reduce((e,t)=>e+(t.shares?.count||0),0),d=this.getInsightValue(i.data,"page_impressions"),m=this.getInsightValue(i.data,"page_reach"),h=this.getInsightValue(i.data,"page_engaged_users");return{platform:"FACEBOOK",accountId:t,accountName:r.name,metrics:{followers:r.fan_count||0,following:0,posts:c.length,engagement:{likes:l,comments:u,shares:p},reach:{impressions:d,reach:m},growth:{followersChange:0,engagementRate:h>0?h/m*100:0,averageLikes:c.length>0?l/c.length:0,averageComments:c.length>0?u/c.length:0}},lastUpdated:new Date}}async getInstagramAnalytics(e,t){let a="https://graph.facebook.com/v19.0",s=await fetch(`${a}/${t}?fields=name,followers_count,follows_count,media_count&access_token=${e}`),r=await s.json(),n=await fetch(`${a}/${t}/insights?metric=impressions,reach,profile_views&period=day&since=${this.getDateDaysAgo(30)}&access_token=${e}`),i=await n.json(),o=await fetch(`${a}/${t}/media?fields=id,caption,timestamp,like_count,comments_count,media_type&limit=10&access_token=${e}`),c=(await o.json()).data||[],l=c.reduce((e,t)=>e+(t.like_count||0),0),u=c.reduce((e,t)=>e+(t.comments_count||0),0),p=this.getInsightValue(i.data,"impressions"),d=this.getInsightValue(i.data,"reach"),m=this.getInsightValue(i.data,"profile_views");return{platform:"INSTAGRAM",accountId:t,accountName:r.name,metrics:{followers:r.followers_count||0,following:r.follows_count||0,posts:r.media_count||0,engagement:{likes:l,comments:u,shares:0,saves:0},reach:{impressions:p,reach:d,profileViews:m},growth:{followersChange:0,engagementRate:d>0?(l+u)/d*100:0,averageLikes:c.length>0?l/c.length:0,averageComments:c.length>0?u/c.length:0}},lastUpdated:new Date}}async getTwitterAnalytics(e,t){let a="https://api.twitter.com/2",s=await fetch(`${a}/users/${t}?user.fields=public_metrics,name,username`,{headers:{Authorization:`Bearer ${e}`}}),r=await s.json(),n=await fetch(`${a}/users/${t}/tweets?tweet.fields=public_metrics,created_at&max_results=10`,{headers:{Authorization:`Bearer ${e}`}}),i=(await n.json()).data||[],o=i.reduce((e,t)=>e+(t.public_metrics?.like_count||0),0),c=i.reduce((e,t)=>e+(t.public_metrics?.retweet_count||0),0),l=i.reduce((e,t)=>e+(t.public_metrics?.reply_count||0),0),u=i.reduce((e,t)=>e+(t.public_metrics?.impression_count||0),0),p=r.data,d=p.public_metrics;return{platform:"TWITTER",accountId:t,accountName:p.name,metrics:{followers:d.followers_count,following:d.following_count,posts:d.tweet_count,engagement:{likes:o,comments:l,shares:c},reach:{impressions:u,reach:u},growth:{followersChange:0,engagementRate:u>0?(o+c+l)/u*100:0,averageLikes:i.length>0?o/i.length:0,averageComments:i.length>0?l/i.length:0}},lastUpdated:new Date}}async getLinkedInAnalytics(e,t){let a=await fetch(`https://api.linkedin.com/v2/people/${t}?projection=(id,firstName,lastName,headline)`,{headers:{Authorization:`Bearer ${e}`}}),s=await a.json();return{platform:"LINKEDIN",accountId:t,accountName:`${s.firstName?.localized?.en_US||""} ${s.lastName?.localized?.en_US||""}`.trim(),metrics:{followers:0,following:0,posts:0,engagement:{likes:0,comments:0,shares:0},reach:{impressions:0,reach:0},growth:{followersChange:0,engagementRate:0,averageLikes:0,averageComments:0}},lastUpdated:new Date}}getInsightValue(e,t){let a=e?.find(e=>e.name===t);return a&&a.values&&0!==a.values.length&&a.values[a.values.length-1].value||0}getDateDaysAgo(e){let t=new Date;return t.setDate(t.getDate()-e),t.toISOString().split("T")[0]}async getPostAnalytics(e,t,a){let s=await this.connectionService.getValidToken(e,t,a.split("_")[0]);if(!s)return null;switch(t.toLowerCase()){case"facebook":return this.getFacebookPostAnalytics(s,a);case"instagram":return this.getInstagramPostAnalytics(s,a);case"twitter":return this.getTwitterPostAnalytics(s,a);default:return null}}async getFacebookPostAnalytics(e,t){let a=await fetch(`https://graph.facebook.com/v19.0/${t}?fields=message,created_time,likes.summary(true),comments.summary(true),shares,insights.metric(post_impressions,post_reach)&access_token=${e}`),s=await a.json(),r=s.likes?.summary?.total_count||0,n=s.comments?.summary?.total_count||0,i=s.shares?.count||0,o=this.getInsightValue(s.insights?.data,"post_impressions"),c=this.getInsightValue(s.insights?.data,"post_reach");return{postId:t,platform:"FACEBOOK",content:s.message||"",publishedAt:new Date(s.created_time),metrics:{likes:r,comments:n,shares:i,impressions:o,reach:c,clicks:0},engagement:{rate:c>0?(r+n+i)/c*100:0,score:r+2*n+3*i}}}async getInstagramPostAnalytics(e,t){let a=await fetch(`https://graph.facebook.com/v19.0/${t}?fields=caption,timestamp,like_count,comments_count,insights.metric(impressions,reach,saves)&access_token=${e}`),s=await a.json(),r=s.like_count||0,n=s.comments_count||0,i=this.getInsightValue(s.insights?.data,"impressions"),o=this.getInsightValue(s.insights?.data,"reach"),c=this.getInsightValue(s.insights?.data,"saves");return{postId:t,platform:"INSTAGRAM",content:s.caption||"",publishedAt:new Date(s.timestamp),metrics:{likes:r,comments:n,shares:0,saves:c,impressions:i,reach:o,clicks:0},engagement:{rate:o>0?(r+n+c)/o*100:0,score:r+2*n+1.5*c}}}async getTwitterPostAnalytics(e,t){let a=await fetch(`https://api.twitter.com/2/tweets/${t}?tweet.fields=public_metrics,created_at,text`,{headers:{Authorization:`Bearer ${e}`}}),s=(await a.json()).data,r=s.public_metrics;return{postId:t,platform:"TWITTER",content:s.text,publishedAt:new Date(s.created_at),metrics:{likes:r.like_count,comments:r.reply_count,shares:r.retweet_count,impressions:r.impression_count,reach:r.impression_count,clicks:0},engagement:{rate:r.impression_count>0?(r.like_count+r.reply_count+r.retweet_count)/r.impression_count*100:0,score:r.like_count+2*r.reply_count+3*r.retweet_count}}}constructor(){this.connectionService=new l.F}}var p=a(63511);async function d(e){try{let{user:t}=await (0,c.vD)(e),{searchParams:a}=new URL(e.url),s=a.get("platform"),r=a.get("accountId"),n="true"===a.get("refresh"),i=new u,l=(0,p.U)();if(s&&r)try{let e=await i.getAccountAnalytics(t.id,s,r);if(e)return await l.from("analytics_cache").upsert({user_id:t.id,platform:e.platform,account_id:e.accountId,metrics:e.metrics,last_updated:e.lastUpdated.toISOString()},{onConflict:"user_id,platform,account_id"}),o.NextResponse.json({success:!0,analytics:e});return o.NextResponse.json({error:"Failed to fetch analytics for this account"},{status:404})}catch(e){if(console.error(`Analytics error for ${s}:`,e),!n){let{data:e}=await l.from("analytics_cache").select("*").eq("user_id",t.id).eq("platform",s.toUpperCase()).eq("account_id",r).single();if(e)return o.NextResponse.json({success:!0,analytics:{platform:e.platform,accountId:e.account_id,accountName:e.account_name||"Unknown",metrics:e.metrics,lastUpdated:new Date(e.last_updated)},cached:!0})}return o.NextResponse.json({error:e.message||"Failed to fetch analytics"},{status:500})}try{let e=await i.getAllAccountsAnalytics(t.id);for(let a of e)await l.from("analytics_cache").upsert({user_id:t.id,platform:a.platform,account_id:a.accountId,account_name:a.accountName,metrics:a.metrics,last_updated:a.lastUpdated.toISOString()},{onConflict:"user_id,platform,account_id"});return o.NextResponse.json({success:!0,analytics:e})}catch(e){if(console.error("All analytics error:",e),!n){let{data:e}=await l.from("analytics_cache").select("*").eq("user_id",t.id).order("last_updated",{ascending:!1});if(e&&e.length>0){let t=e.map(e=>({platform:e.platform,accountId:e.account_id,accountName:e.account_name||"Unknown",metrics:e.metrics,lastUpdated:new Date(e.last_updated)}));return o.NextResponse.json({success:!0,analytics:t,cached:!0})}}return o.NextResponse.json({error:e.message||"Failed to fetch analytics"},{status:500})}}catch(e){return console.error("Analytics API error:",e),o.NextResponse.json({error:e.message||"Internal server error"},{status:500})}}async function m(e){try{let{user:t}=await (0,c.vD)(e),{action:a,platform:s,accountId:r,postId:n}=await e.json(),i=new u;if("refresh"===a)if(s&&r){let e=await i.getAccountAnalytics(t.id,s,r);return o.NextResponse.json({success:!0,analytics:e,refreshed:!0})}else{let e=await i.getAllAccountsAnalytics(t.id);return o.NextResponse.json({success:!0,analytics:e,refreshed:!0})}if("post-analytics"!==a||!n)return o.NextResponse.json({error:"Invalid action"},{status:400});{let e=await i.getPostAnalytics(t.id,s,n);return o.NextResponse.json({success:!0,postAnalytics:e})}}catch(e){return console.error("Analytics POST error:",e),o.NextResponse.json({error:e.message||"Failed to process analytics request"},{status:500})}}let h=new r.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/analytics/route",pathname:"/api/analytics",filename:"route",bundlePath:"app/api/analytics/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Ewasl\\eWasl.com_Cursor_Digital_Ocean\\ewasl-app\\src\\app\\api\\analytics\\route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:_,workUnitAsyncStorage:g,serverHooks:f}=h;function w(){return(0,i.patchFetch)({workAsyncStorage:_,workUnitAsyncStorage:g})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},24473:(e,t,a)=>{"use strict";a.d(t,{FL:()=>c,WX:()=>l,vD:()=>o});var s=a(34386),r=a(44999),n=a(32190);async function i(){try{let e="https://nnxfzhxqzmriggulsudr.supabase.co",t="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5ueGZ6aHhxem1yaWdndWxzdWRyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIwNjg3NjgsImV4cCI6MjA2NzY0NDc2OH0.1qBlXLOqGtJX_LFRC7XihOpwCtD1U1I2_jdyJ8sq_8w";if(e&&t&&!e.includes("placeholder"))return{url:e,key:t};return console.warn("API Auth: Using fallback Supabase configuration"),{url:"https://nnxfzhxqzmriggulsudr.supabase.co",key:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5ueGZ6aHhxem1yaWdndWxzdWRyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIwNjg3NjgsImV4cCI6MjA2NzY0NDc2OH0.1qBlXLOqGtJX_LFRC7XihOpwCtD1U1I2_jdyJ8sq_8w"}}catch(e){return console.error("Error getting Supabase config in API auth:",e),{url:"https://nnxfzhxqzmriggulsudr.supabase.co",key:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5ueGZ6aHhxem1yaWdndWxzdWRyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIwNjg3NjgsImV4cCI6MjA2NzY0NDc2OH0.1qBlXLOqGtJX_LFRC7XihOpwCtD1U1I2_jdyJ8sq_8w"}}}async function o(e){try{let e=await (0,r.cookies)(),t=await i(),a=(0,s.createServerClient)(t.url,t.key,{cookies:{get:t=>e.get(t)?.value,set(t,a,s){try{e.set(t,a,s)}catch(e){console.warn("Cannot set cookie in API route:",t)}},remove(t,a){try{e.set(t,"",{...a,maxAge:0})}catch(e){console.warn("Cannot remove cookie in API route:",t)}}}}),{data:{user:n},error:o}=await a.auth.getUser();if(o)throw console.error("Authentication error in API route:",o),Error(`Authentication failed: ${o.message}`);if(!n)throw Error("No authenticated user found");return{user:n,supabase:a}}catch(e){throw console.error("❌ API Authentication failed:",e),e}}function c(e,t=200){return n.NextResponse.json({success:!0,data:e,timestamp:new Date().toISOString()},{status:t})}function l(e,t=500,a){return n.NextResponse.json({success:!1,error:e,details:a,timestamp:new Date().toISOString()},{status:t})}},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32497:(e,t,a)=>{"use strict";function s(e){return({facebook:{id:"facebook",name:"Facebook",clientId:process.env.FACEBOOK_APP_ID||"",clientSecret:process.env.FACEBOOK_APP_SECRET||"",scope:["email","public_profile","pages_show_list","pages_manage_posts","pages_read_engagement","business_management","instagram_basic","instagram_content_publish"],authUrl:"https://www.facebook.com/v19.0/dialog/oauth",tokenUrl:"https://graph.facebook.com/v19.0/oauth/access_token",userInfoUrl:"https://graph.facebook.com/v19.0/me",enabled:!!(process.env.FACEBOOK_APP_ID&&process.env.FACEBOOK_APP_SECRET)},twitter:{id:"twitter",name:"Twitter",clientId:process.env.TWITTER_CLIENT_ID||"",clientSecret:process.env.TWITTER_CLIENT_SECRET||"",scope:["tweet.read","tweet.write","users.read","follows.read","follows.write","offline.access"],authUrl:"https://twitter.com/i/oauth2/authorize",tokenUrl:"https://api.twitter.com/2/oauth2/token",userInfoUrl:"https://api.twitter.com/2/users/me",enabled:!!(process.env.TWITTER_CLIENT_ID&&process.env.TWITTER_CLIENT_SECRET)},linkedin:{id:"linkedin",name:"LinkedIn",clientId:process.env.LINKEDIN_CLIENT_ID||"",clientSecret:process.env.LINKEDIN_CLIENT_SECRET||"",scope:["r_liteprofile","r_emailaddress","w_member_social"],authUrl:"https://www.linkedin.com/oauth/v2/authorization",tokenUrl:"https://www.linkedin.com/oauth/v2/accessToken",userInfoUrl:"https://api.linkedin.com/v2/me",enabled:!!(process.env.LINKEDIN_CLIENT_ID&&process.env.LINKEDIN_CLIENT_SECRET)},instagram:{id:"instagram",name:"Instagram",clientId:process.env.FACEBOOK_APP_ID||"",clientSecret:process.env.FACEBOOK_APP_SECRET||"",scope:["instagram_graph_user_profile","instagram_graph_user_media","instagram_content_publish","pages_show_list","pages_read_engagement","business_management"],authUrl:"https://www.facebook.com/v19.0/dialog/oauth",tokenUrl:"https://graph.facebook.com/v19.0/oauth/access_token",userInfoUrl:"https://graph.facebook.com/v19.0/me",enabled:!!(process.env.FACEBOOK_APP_ID&&process.env.FACEBOOK_APP_SECRET)}})[e.toLowerCase()]||null}function r(e,t,a,s){let r=new URLSearchParams({client_id:e.clientId,redirect_uri:a,scope:e.scope.join(" "),response_type:"code",state:t});return"twitter"===e.id&&s&&(r.append("code_challenge",s),r.append("code_challenge_method","S256")),`${e.authUrl}?${r.toString()}`}async function n(e,t,a,s){let r=new URLSearchParams({client_id:e.clientId,client_secret:e.clientSecret,code:t,redirect_uri:a,grant_type:"authorization_code"});"twitter"===e.id&&s&&r.append("code_verifier",s);let n=await fetch(e.tokenUrl,{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded",Accept:"application/json"},body:r.toString()});if(!n.ok){let e=await n.text();throw Error(`Token exchange failed: ${e}`)}let i=await n.json();return{accessToken:i.access_token,refreshToken:i.refresh_token,expiresAt:i.expires_in?new Date(Date.now()+1e3*i.expires_in):void 0,scope:i.scope?.split(" "),tokenType:i.token_type||"bearer"}}async function i(e,t){let a=await fetch(e.userInfoUrl,{headers:{Authorization:`Bearer ${t}`,Accept:"application/json"}});if(!a.ok){let e=await a.text();throw Error(`Failed to get user profile: ${e}`)}let s=await a.json();switch(e.id){case"facebook":case"instagram":return{id:s.id,name:s.name,username:s.username,email:s.email,avatar:s.picture?.data?.url,platform:e.id};case"twitter":return{id:s.data.id,name:s.data.name,username:s.data.username,avatar:s.data.profile_image_url,platform:e.id};case"linkedin":return{id:s.id,name:`${s.localizedFirstName} ${s.localizedLastName}`,avatar:s.profilePicture?.["displayImage~"]?.elements?.[0]?.identifiers?.[0]?.identifier,platform:e.id};default:throw Error(`Unsupported platform: ${e.id}`)}}a.d(t,{BW:()=>r,J6:()=>n,VM:()=>i,ZH:()=>s})},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},53957:(e,t,a)=>{"use strict";a.d(t,{F:()=>o});var s=a(63511),r=a(32497),n=a(55511),i=a.n(n);class o{generateState(){return i().randomBytes(32).toString("hex")}generatePKCE(){let e=i().randomBytes(32).toString("base64url"),t=i().createHash("sha256").update(e).digest("base64url");return{codeVerifier:e,codeChallenge:t}}async storeOAuthState(e,t,a,s,r){let{error:n}=await this.supabase.from("oauth_states").insert({user_id:e,platform:t.toLowerCase(),state_token:a,redirect_uri:s,code_verifier:r,expires_at:new Date(Date.now()+6e5).toISOString()});if(n)throw Error(`Failed to store OAuth state: ${n.message}`)}async verifyOAuthState(e,t){let{data:a,error:s}=await this.supabase.from("oauth_states").select("*").eq("state_token",e).eq("platform",t.toLowerCase()).gt("expires_at",new Date().toISOString()).single();return s||!a?null:(await this.supabase.from("oauth_states").delete().eq("state_token",e),{state:a.state_token,platform:a.platform,userId:a.user_id,redirectUri:a.redirect_uri,codeVerifier:a.code_verifier,expiresAt:new Date(a.expires_at)})}async storeConnection(e,t,a,s){let r={user_id:e,platform:t.toUpperCase(),account_id:s.id,account_name:s.name,username:s.username,avatar_url:s.avatar,access_token:a.accessToken,refresh_token:a.refreshToken,expires_at:a.expiresAt?.toISOString(),is_active:!0,updated_at:new Date().toISOString()},{data:n,error:i}=await this.supabase.from("social_accounts").upsert(r,{onConflict:"user_id,platform,account_id"}).select().single();if(i)throw Error(`Failed to store connection: ${i.message}`);return this.mapToSocialConnection(n)}async getUserConnections(e){let{data:t,error:a}=await this.supabase.from("social_accounts").select("*").eq("user_id",e).eq("is_active",!0).order("created_at",{ascending:!1});if(a)throw Error(`Failed to get connections: ${a.message}`);return t.map(this.mapToSocialConnection)}async getConnection(e,t,a){let s=this.supabase.from("social_accounts").select("*").eq("user_id",e).eq("platform",t.toUpperCase()).eq("is_active",!0);a&&(s=s.eq("account_id",a));let{data:r,error:n}=await s.single();return n||!r?null:this.mapToSocialConnection(r)}async disconnectAccount(e,t,a){let{error:s}=await this.supabase.from("social_accounts").update({is_active:!1,updated_at:new Date().toISOString()}).eq("user_id",e).eq("platform",t.toUpperCase()).eq("account_id",a);return!s}async refreshToken(e,t,a){let s=await this.getConnection(e,t,a);if(!s||!s.refreshToken)return!1;let n=(0,r.ZH)(t);if(!n)return!1;try{let r=await fetch(n.tokenUrl,{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded",Accept:"application/json"},body:new URLSearchParams({client_id:n.clientId,client_secret:n.clientSecret,refresh_token:s.refreshToken,grant_type:"refresh_token"}).toString()});if(!r.ok)return!1;let i=await r.json(),{error:o}=await this.supabase.from("social_accounts").update({access_token:i.access_token,refresh_token:i.refresh_token||s.refreshToken,expires_at:i.expires_in?new Date(Date.now()+1e3*i.expires_in).toISOString():s.expiresAt?.toISOString(),updated_at:new Date().toISOString()}).eq("user_id",e).eq("platform",t.toUpperCase()).eq("account_id",a);return!o}catch(e){return console.error("Token refresh failed:",e),!1}}isTokenExpired(e){if(!e.expiresAt)return!1;let t=new Date(Date.now()+3e5);return e.expiresAt<=t}async getValidToken(e,t,a){let s=await this.getConnection(e,t,a);if(!s)return null;if(this.isTokenExpired(s)){if(!await this.refreshToken(e,t,a))return null;let s=await this.getConnection(e,t,a);return s?.accessToken||null}return s.accessToken}mapToSocialConnection(e){return{id:e.id,userId:e.user_id,platform:e.platform,accountId:e.account_id,accountName:e.account_name,username:e.username,avatar:e.avatar_url,accessToken:e.access_token,refreshToken:e.refresh_token,expiresAt:e.expires_at?new Date(e.expires_at):void 0,isActive:e.is_active,createdAt:new Date(e.created_at),updatedAt:new Date(e.updated_at)}}constructor(){this.supabase=(0,s.U)()}}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63511:(e,t,a)=>{"use strict";a.d(t,{U:()=>i,x:()=>o});var s=a(34386);let r=null;async function n(){if(r)return r;try{let e=await fetch("/api/config/supabase");if(!e.ok)throw Error(`Failed to fetch config: ${e.status}`);let t=await e.json();return r=t,t}catch(e){throw console.error("[Supabase Client] Failed to fetch dynamic config:",e),e}}function i(){let e="https://nnxfzhxqzmriggulsudr.supabase.co",t="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5ueGZ6aHhxem1yaWdndWxzdWRyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIwNjg3NjgsImV4cCI6MjA2NzY0NDc2OH0.1qBlXLOqGtJX_LFRC7XihOpwCtD1U1I2_jdyJ8sq_8w";return e&&t&&!e.includes("placeholder")&&!t.includes("placeholder")?(0,s.createBrowserClient)(e,t):(0,s.createBrowserClient)("https://placeholder.supabase.co","placeholder-key")}async function o(){let e="https://nnxfzhxqzmriggulsudr.supabase.co",t="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5ueGZ6aHhxem1yaWdndWxzdWRyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIwNjg3NjgsImV4cCI6MjA2NzY0NDc2OH0.1qBlXLOqGtJX_LFRC7XihOpwCtD1U1I2_jdyJ8sq_8w";if(e&&t&&!e.includes("placeholder")&&!t.includes("placeholder"))return(0,s.createBrowserClient)(e,t);let a=await n();return(0,s.createBrowserClient)(a.url,a.anonKey)}},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),s=t.X(0,[4243,6167,580,4386,4999],()=>a(8514));module.exports=s})();