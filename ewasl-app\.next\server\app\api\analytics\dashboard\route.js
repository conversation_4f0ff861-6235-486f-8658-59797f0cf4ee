(()=>{var e={};e.id=4487,e.ids=[4487],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},24473:(e,t,r)=>{"use strict";r.d(t,{FL:()=>c,WX:()=>u,vD:()=>i});var s=r(34386),a=r(44999),o=r(32190);async function n(){try{let e="https://nnxfzhxqzmriggulsudr.supabase.co",t="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5ueGZ6aHhxem1yaWdndWxzdWRyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIwNjg3NjgsImV4cCI6MjA2NzY0NDc2OH0.1qBlXLOqGtJX_LFRC7XihOpwCtD1U1I2_jdyJ8sq_8w";if(e&&t&&!e.includes("placeholder"))return{url:e,key:t};return console.warn("API Auth: Using fallback Supabase configuration"),{url:"https://nnxfzhxqzmriggulsudr.supabase.co",key:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5ueGZ6aHhxem1yaWdndWxzdWRyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIwNjg3NjgsImV4cCI6MjA2NzY0NDc2OH0.1qBlXLOqGtJX_LFRC7XihOpwCtD1U1I2_jdyJ8sq_8w"}}catch(e){return console.error("Error getting Supabase config in API auth:",e),{url:"https://nnxfzhxqzmriggulsudr.supabase.co",key:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5ueGZ6aHhxem1yaWdndWxzdWRyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIwNjg3NjgsImV4cCI6MjA2NzY0NDc2OH0.1qBlXLOqGtJX_LFRC7XihOpwCtD1U1I2_jdyJ8sq_8w"}}}async function i(e){try{let e=await (0,a.cookies)(),t=await n(),r=(0,s.createServerClient)(t.url,t.key,{cookies:{get:t=>e.get(t)?.value,set(t,r,s){try{e.set(t,r,s)}catch(e){console.warn("Cannot set cookie in API route:",t)}},remove(t,r){try{e.set(t,"",{...r,maxAge:0})}catch(e){console.warn("Cannot remove cookie in API route:",t)}}}}),{data:{user:o},error:i}=await r.auth.getUser();if(i)throw console.error("Authentication error in API route:",i),Error(`Authentication failed: ${i.message}`);if(!o)throw Error("No authenticated user found");return{user:o,supabase:r}}catch(e){throw console.error("❌ API Authentication failed:",e),e}}function c(e,t=200){return o.NextResponse.json({success:!0,data:e,timestamp:new Date().toISOString()},{status:t})}function u(e,t=500,r){return o.NextResponse.json({success:!1,error:e,details:r,timestamp:new Date().toISOString()},{status:t})}},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},60566:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>h,routeModule:()=>p,serverHooks:()=>m,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>l});var s={};r.r(s),r.d(s,{GET:()=>u});var a=r(96559),o=r(48088),n=r(37719),i=r(32190),c=r(24473);async function u(e){return(0,c.vD)(e).then(({user:e,supabase:t})=>i.NextResponse.json({success:!0,user_id:e.id,dashboard_metrics:{overview:{total_posts:156,scheduled_posts:23,published_today:8,engagement_rate:4.2,followers_growth:156,reach:12450},recent_activity:[{type:"post_published",platform:"twitter",content:"New product launch announcement",timestamp:"2024-01-15T10:30:00Z",engagement:{likes:45,shares:12,comments:8}},{type:"post_scheduled",platform:"facebook",content:"Weekly tips for social media success",scheduled_for:"2024-01-16T14:00:00Z"}],performance:{best_performing_post:{content:"Tips for better engagement",platform:"instagram",engagement_rate:8.5,reach:5670},top_platforms:[{name:"Instagram",posts:45,engagement:6.2},{name:"Twitter",posts:38,engagement:4.8},{name:"Facebook",posts:32,engagement:3.9}]},upcoming:{posts_this_week:12,posts_next_week:8,scheduled_campaigns:3}},generated_at:new Date().toISOString()})).catch(e=>i.NextResponse.json({error:"Authentication required for dashboard analytics"},{status:401}))}let p=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/analytics/dashboard/route",pathname:"/api/analytics/dashboard",filename:"route",bundlePath:"app/api/analytics/dashboard/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Ewasl\\eWasl.com_Cursor_Digital_Ocean\\ewasl-app\\src\\app\\api\\analytics\\dashboard\\route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:l,serverHooks:m}=p;function h(){return(0,n.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:l})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4243,6167,580,4386,4999],()=>r(60566));module.exports=s})();