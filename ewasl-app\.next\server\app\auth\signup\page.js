(()=>{var e={};e.id=2778,e.ids=[2778],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},16189:(e,r,t)=>{"use strict";var s=t(65773);t.o(s,"useParams")&&t.d(r,{useParams:function(){return s.useParams}}),t.o(s,"usePathname")&&t.d(r,{usePathname:function(){return s.usePathname}}),t.o(s,"useRouter")&&t.d(r,{useRouter:function(){return s.useRouter}}),t.o(s,"useSearchParams")&&t.d(r,{useSearchParams:function(){return s.useSearchParams}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29523:(e,r,t)=>{"use strict";t.d(r,{$:()=>l,r:()=>n});var s=t(60687);t(43210);var a=t(8730),o=t(24224),i=t(4780);let n=(0,o.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l({className:e,variant:r,size:t,asChild:o=!1,...l}){let d=o?a.DX:"button";return(0,s.jsx)(d,{"data-slot":"button",className:(0,i.cn)(n({variant:r,size:t,className:e})),...l})}},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},35496:(e,r,t)=>{Promise.resolve().then(t.bind(t,57599))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57599:(e,r,t)=>{"use strict";t.d(r,{SignUpForm:()=>w});var s=t(60687),a=t(43210),o=t(27605),i=t(63442),n=t(9275),l=t(16189),d=t(85814),c=t.n(d),u=t(29523),m=t(71669),p=t(89667);let x=["10minutemail.com","tempmail.org","guerrillamail.com","mailinator.com","yopmail.com","temp-mail.org","throwaway.email","getnada.com","maildrop.cc","sharklasers.com"],h={"gmai.com":"gmail.com","gmial.com":"gmail.com","gmail.co":"gmail.com","yahooo.com":"yahoo.com","yaho.com":"yahoo.com","hotmial.com":"hotmail.com","hotmai.com":"hotmail.com","outlok.com":"outlook.com","outloo.com":"outlook.com"};function f(e){if(!e||""===e.trim())return{isValid:!1,error:"البريد الإلكتروني مطلوب"};let r=e.trim().toLowerCase();if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(r))return{isValid:!1,error:"تنسيق البريد الإلكتروني غير صحيح"};let t=r.split("@")[1];return x.includes(t)?{isValid:!1,error:"البريد الإلكتروني المؤقت غير مسموح. يرجى استخدام بريد إلكتروني دائم"}:h[t]?{isValid:!1,error:"يبدو أن هناك خطأ في النطاق",suggestion:r.replace(t,h[t])}:r.split("@")[0].length>64?{isValid:!1,error:"الجزء المحلي من البريد الإلكتروني طويل جداً"}:t.length>253?{isValid:!1,error:"نطاق البريد الإلكتروني طويل جداً"}:r.includes("..")?{isValid:!1,error:"البريد الإلكتروني يحتوي على نقاط متتالية"}:/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(r)?{isValid:!0}:{isValid:!1,error:"البريد الإلكتروني يحتوي على أحرف غير صالحة"}}var g=t(79481),b=t(52581);let v=n.z.object({name:n.z.string().min(2,{message:"الاسم يجب أن يكون حرفين على الأقل"}),email:n.z.string().min(1,{message:"البريد الإلكتروني مطلوب"}).refine(e=>(function(e){let r=f(e);if(!r.isValid)return r;let t=e.trim().toLowerCase();for(let e of(t.split("@")[1],[/test.*@/,/demo.*@/,/fake.*@/,/example.*@/,/@test\./,/@example\./,/@localhost/,/@.*\.test$/,/@.*\.local$/]))if(e.test(t))return{isValid:!1,error:"عناوين البريد الإلكتروني التجريبية غير مسموحة في الإنتاج"};return{isValid:!0}})(e).isValid,{message:"البريد الإلكتروني غير صالح أو غير مسموح"}),password:n.z.string().min(6,{message:"كلمة المرور يجب أن تكون 6 أحرف على الأقل"}),confirmPassword:n.z.string()}).refine(e=>e.password===e.confirmPassword,{message:"كلمات المرور غير متطابقة",path:["confirmPassword"]});function w(){let e=(0,l.useRouter)(),[r,t]=(0,a.useState)(!1),[n,d]=(0,a.useState)(null),[x,h]=(0,a.useState)(null),w=(0,o.mN)({resolver:(0,i.u)(v),defaultValues:{name:"",email:"",password:"",confirmPassword:""}}),j=e=>{let r=function(e){let r=f(e);return e&&""!==e.trim()?r:{isValid:!0}}(e);r.suggestion?h(r.suggestion):h(null)};async function y(r){t(!0),d(null);try{let t=(0,g.U)(),{data:s,error:a}=await t.auth.signUp({email:r.email,password:r.password,options:{data:{name:r.name,role:"USER"}}});if(a)throw console.error("Supabase auth error:",a),Error(a.message||"حدث خطأ أثناء التسجيل");if(!s.user)throw Error("فشل في إنشاء المستخدم");b.o.success("تم إنشاء الحساب بنجاح!"),s.user&&!s.session?(b.o.info("يرجى التحقق من بريدك الإلكتروني لتأكيد الحساب"),e.push("/auth/signin?message=check-email")):e.push("/dashboard")}catch(e){console.error("Registration error:",e),d(e.message||"حدث خطأ أثناء التسجيل"),b.o.error(e.message||"حدث خطأ أثناء التسجيل")}finally{t(!1)}}return(0,s.jsxs)("div",{className:"w-full",dir:"rtl",children:[(0,s.jsx)(m.lV,{...w,children:(0,s.jsxs)("form",{onSubmit:w.handleSubmit(y),className:"space-y-4",children:[(0,s.jsx)(m.zB,{control:w.control,name:"name",render:({field:e})=>(0,s.jsxs)(m.eI,{children:[(0,s.jsx)(m.lR,{className:"text-gray-700 font-medium",children:"الاسم"}),(0,s.jsx)(m.MJ,{children:(0,s.jsx)(p.p,{placeholder:"أدخل اسمك",className:"h-12 border-gray-200 focus:border-blue-500 focus:ring-blue-500 transition-colors",...e})}),(0,s.jsx)(m.C5,{})]})}),(0,s.jsx)(m.zB,{control:w.control,name:"email",render:({field:e})=>(0,s.jsxs)(m.eI,{children:[(0,s.jsx)(m.lR,{className:"text-gray-700 font-medium",children:"البريد الإلكتروني"}),(0,s.jsx)(m.MJ,{children:(0,s.jsx)(p.p,{placeholder:"أدخل بريدك الإلكتروني",className:"h-12 border-gray-200 focus:border-blue-500 focus:ring-blue-500 transition-colors",...e,onChange:r=>{e.onChange(r),j(r.target.value)}})}),x&&(0,s.jsxs)("div",{className:"text-sm text-blue-600",children:["هل تقصد:",(0,s.jsx)("button",{type:"button",className:"underline mr-1",onClick:()=>{w.setValue("email",x),h(null)},children:x}),"؟"]}),(0,s.jsx)(m.C5,{})]})}),(0,s.jsx)(m.zB,{control:w.control,name:"password",render:({field:e})=>(0,s.jsxs)(m.eI,{children:[(0,s.jsx)(m.lR,{className:"text-gray-700 font-medium",children:"كلمة المرور"}),(0,s.jsx)(m.MJ,{children:(0,s.jsx)(p.p,{type:"password",placeholder:"أدخل كلمة المرور",className:"h-12 border-gray-200 focus:border-blue-500 focus:ring-blue-500 transition-colors",...e})}),(0,s.jsx)(m.C5,{})]})}),(0,s.jsx)(m.zB,{control:w.control,name:"confirmPassword",render:({field:e})=>(0,s.jsxs)(m.eI,{children:[(0,s.jsx)(m.lR,{className:"text-gray-700 font-medium",children:"تأكيد كلمة المرور"}),(0,s.jsx)(m.MJ,{children:(0,s.jsx)(p.p,{type:"password",placeholder:"أدخل كلمة المرور مرة أخرى",className:"h-12 border-gray-200 focus:border-blue-500 focus:ring-blue-500 transition-colors",...e})}),(0,s.jsx)(m.C5,{})]})}),n&&(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,s.jsx)("p",{className:"text-red-600 text-sm text-center",children:n})}),(0,s.jsx)(u.$,{type:"submit",className:"w-full h-12 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl",disabled:r,children:r?(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}),"جاري التسجيل..."]}):"إنشاء حساب"})]})}),(0,s.jsx)("div",{className:"text-center mt-6",children:(0,s.jsxs)("p",{className:"text-gray-600",children:["لديك حساب بالفعل؟"," ",(0,s.jsx)(c(),{href:"/auth/signin",className:"text-blue-600 hover:text-blue-700 font-medium transition-colors duration-200",children:"تسجيل الدخول"})]})})]})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65e3:(e,r,t)=>{Promise.resolve().then(t.bind(t,98662))},70440:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var s=t(31658);let a=async e=>[{type:"image/x-icon",sizes:"48x48",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},71669:(e,r,t)=>{"use strict";t.d(r,{C5:()=>b,MJ:()=>f,Rr:()=>g,eI:()=>x,lR:()=>h,lV:()=>d,zB:()=>u});var s=t(60687),a=t(43210),o=t(8730),i=t(27605),n=t(4780),l=t(80013);let d=i.Op,c=a.createContext({}),u=({...e})=>(0,s.jsx)(c.Provider,{value:{name:e.name},children:(0,s.jsx)(i.xI,{...e})}),m=()=>{let e=a.useContext(c),r=a.useContext(p),{getFieldState:t,formState:s}=(0,i.xW)(),o=t(e.name,s);if(!e)throw Error("useFormField should be used within <FormField>");let{id:n}=r;return{id:n,name:e.name,formItemId:`${n}-form-item`,formDescriptionId:`${n}-form-item-description`,formMessageId:`${n}-form-item-message`,...o}},p=a.createContext({}),x=a.forwardRef(({className:e,...r},t)=>{let o=a.useId();return(0,s.jsx)(p.Provider,{value:{id:o},children:(0,s.jsx)("div",{ref:t,className:(0,n.cn)("space-y-2",e),...r})})});x.displayName="FormItem";let h=a.forwardRef(({className:e,...r},t)=>{let{error:a,formItemId:o}=m();return(0,s.jsx)(l.J,{ref:t,className:(0,n.cn)(a&&"text-destructive",e),htmlFor:o,...r})});h.displayName="FormLabel";let f=a.forwardRef(({...e},r)=>{let{error:t,formItemId:a,formDescriptionId:i,formMessageId:n}=m();return(0,s.jsx)(o.DX,{ref:r,id:a,"aria-describedby":t?`${i} ${n}`:`${i}`,"aria-invalid":!!t,...e})});f.displayName="FormControl";let g=a.forwardRef(({className:e,...r},t)=>{let{formDescriptionId:a}=m();return(0,s.jsx)("p",{ref:t,id:a,className:(0,n.cn)("text-sm text-muted-foreground",e),...r})});g.displayName="FormDescription";let b=a.forwardRef(({className:e,children:r,...t},a)=>{let{error:o,formMessageId:i}=m(),l=o?String(o?.message??""):r;return l?(0,s.jsx)("p",{ref:a,id:i,className:(0,n.cn)("text-sm font-medium text-destructive",e),...t,children:l}):null});b.displayName="FormMessage"},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},80013:(e,r,t)=>{"use strict";t.d(r,{J:()=>d});var s=t(60687),a=t(43210),o=t(78148),i=t(24224),n=t(4780);let l=(0,i.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)(o.b,{ref:t,className:(0,n.cn)(l(),e),...r}));d.displayName=o.b.displayName},81630:e=>{"use strict";e.exports=require("http")},89667:(e,r,t)=>{"use strict";t.d(r,{p:()=>i});var s=t(60687),a=t(43210),o=t(4780);let i=a.forwardRef(({className:e,type:r,...t},a)=>(0,s.jsx)("input",{type:r,className:(0,o.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:a,...t}));i.displayName="Input"},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},94796:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>i,metadata:()=>o});var s=t(37413),a=t(98662);let o={title:"إنشاء حساب | eWasl Social Scheduler",description:"إنشاء حساب جديد في منصة eWasl لإدارة وسائل التواصل الاجتماعي"};function i(){return(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-purple-50 flex items-center justify-center p-4",children:(0,s.jsxs)("div",{className:"w-full max-w-lg",children:[(0,s.jsxs)("div",{className:"text-center mb-10",children:[(0,s.jsx)("div",{className:"w-20 h-20 bg-gradient-to-r from-blue-600 to-purple-600 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-2xl",children:(0,s.jsx)("span",{className:"text-3xl font-bold text-white",children:"eW"})}),(0,s.jsx)("h1",{className:"text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-3",children:"eWasl"}),(0,s.jsx)("p",{className:"text-gray-600 text-lg",children:"منصة إدارة وسائل التواصل الاجتماعي"}),(0,s.jsx)("p",{className:"text-sm text-gray-500 mt-2",children:"إنشاء حساب جديد"})]}),(0,s.jsxs)("div",{className:"bg-white/80 backdrop-blur-xl rounded-3xl shadow-2xl p-10 border border-white/50",children:[(0,s.jsxs)("div",{className:"text-center mb-8",children:[(0,s.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-3",children:"انضم إلينا"}),(0,s.jsx)("p",{className:"text-gray-600 text-lg",children:"أنشئ حسابك الجديد وابدأ رحلتك"})]}),(0,s.jsx)(a.SignUpForm,{})]})]})})}},98036:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var s=t(65239),a=t(48088),o=t(88170),i=t.n(o),n=t(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);t.d(r,l);let d={children:["",{children:["auth",{children:["signup",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,94796)),"C:\\Users\\<USER>\\Desktop\\Ewasl\\eWasl.com_Cursor_Digital_Ocean\\ewasl-app\\src\\app\\auth\\signup\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,45399)),"C:\\Users\\<USER>\\Desktop\\Ewasl\\eWasl.com_Cursor_Digital_Ocean\\ewasl-app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\Ewasl\\eWasl.com_Cursor_Digital_Ocean\\ewasl-app\\src\\app\\auth\\signup\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/auth/signup/page",pathname:"/auth/signup",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},98662:(e,r,t)=>{"use strict";t.d(r,{SignUpForm:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call SignUpForm() from the server but SignUpForm is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Ewasl\\eWasl.com_Cursor_Digital_Ocean\\ewasl-app\\src\\components\\auth\\signup-form.tsx","SignUpForm")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4243,6167,2215,1658,5814,7825,9038,9908],()=>t(98036));module.exports=s})();