"use strict";(()=>{var e={};e.id=6782,e.ids=[6782],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7393:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>P,routeModule:()=>h,serverHooks:()=>E,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>g});var s={};r.r(s),r.d(s,{GET:()=>d,OPTIONS:()=>_});var a=r(96559),o=r(48088),n=r(37719),i=r(32190),l=r(2507),u=r(80726),c=r(85211);class p{async getCurrentUsage(e){let t=this.getCurrentMonthYear(),{data:r,error:s}=await this.supabase.from("usage_tracking").select("*").eq("user_id",e).eq("month_year",t).single();if(s&&"PGRST116"!==s.code)throw console.error("Failed to get usage data:",s),s;return{postsCreated:r?.posts_created||0,postsScheduled:r?.posts_scheduled||0,apiCalls:r?.api_calls||0,storageUsedMb:r?.storage_used_mb||0,aiGenerationsUsed:r?.ai_generations_used||0,reportsGenerated:r?.reports_generated||0}}async getUserPlanLimits(e){let{data:t,error:r}=await this.supabase.from("users").select(`
        current_plan_id,
        subscription_plans!inner(
          plan_type,
          max_social_accounts,
          max_users,
          max_posts_per_month,
          features
        )
      `).eq("id",e).single();if(r)return console.error("Failed to get user plan:",r),c.b9.FREE;let s=t.subscription_plans?.[0]?.plan_type?.toUpperCase()||"FREE";return c.b9[s]||c.b9.FREE}async canPerformAction(e,t){try{let[r,s]=await Promise.all([this.getCurrentUsage(e),this.getUserPlanLimits(e)]);switch(t){case"create_post":if(null===s.postsPerMonth)return{allowed:!0};let a=r.postsCreated+r.postsScheduled;if(a>=s.postsPerMonth)return{allowed:!1,reason:"Monthly post limit reached",currentUsage:a,limit:s.postsPerMonth};return{allowed:!0,currentUsage:a,limit:s.postsPerMonth};case"schedule_post":if(null===s.postsPerMonth)return{allowed:!0};let o=r.postsCreated+r.postsScheduled;if(o>=s.postsPerMonth)return{allowed:!1,reason:"Monthly post limit reached",currentUsage:o,limit:s.postsPerMonth};return{allowed:!0,currentUsage:o,limit:s.postsPerMonth};case"generate_ai":if(null===s.aiGenerations)return{allowed:!0};if(r.aiGenerationsUsed>=s.aiGenerations)return{allowed:!1,reason:"Monthly AI generation limit reached",currentUsage:r.aiGenerationsUsed,limit:s.aiGenerations};return{allowed:!0,currentUsage:r.aiGenerationsUsed,limit:s.aiGenerations};case"generate_report":if(null===s.reportsPerMonth)return{allowed:!0};if(r.reportsGenerated>=s.reportsPerMonth)return{allowed:!1,reason:"Monthly report generation limit reached",currentUsage:r.reportsGenerated,limit:s.reportsPerMonth};return{allowed:!0,currentUsage:r.reportsGenerated,limit:s.reportsPerMonth};case"add_social_account":if(null===s.socialAccounts)return{allowed:!0};let{count:n}=await this.supabase.from("social_accounts").select("*",{count:"exact",head:!0}).eq("user_id",e);if((n||0)>=s.socialAccounts)return{allowed:!1,reason:"Social account limit reached",currentUsage:n||0,limit:s.socialAccounts};return{allowed:!0,currentUsage:n||0,limit:s.socialAccounts};case"add_user":if(null===s.users)return{allowed:!0};let{count:i}=await this.supabase.from("team_members").select("*",{count:"exact",head:!0}).eq("user_id",e).eq("status","active"),l=(i||0)+1;if(l>=s.users)return{allowed:!1,reason:"User limit reached",currentUsage:l,limit:s.users};return{allowed:!0,currentUsage:l,limit:s.users};default:return{allowed:!0}}}catch(e){return console.error("Failed to check action permission:",e),{allowed:!1,reason:"Failed to check permissions"}}}async trackUsage(e,t,r=1){let s=this.getCurrentMonthYear();try{let{data:a}=await this.supabase.from("usage_tracking").select("*").eq("user_id",e).eq("month_year",s).single(),o={};switch(t){case"create_post":o.posts_created=(a?.posts_created||0)+r;break;case"schedule_post":o.posts_scheduled=(a?.posts_scheduled||0)+r;break;case"api_call":o.api_calls=(a?.api_calls||0)+r;break;case"generate_ai":o.ai_generations_used=(a?.ai_generations_used||0)+r;break;case"generate_report":o.reports_generated=(a?.reports_generated||0)+r}if(a){let{error:t}=await this.supabase.from("usage_tracking").update(o).eq("user_id",e).eq("month_year",s);if(t)throw console.error("Failed to update usage:",t),t}else{let{error:t}=await this.supabase.from("usage_tracking").insert({user_id:e,month_year:s,...o});if(t)throw console.error("Failed to create usage record:",t),t}}catch(e){throw console.error("Failed to track usage:",e),e}}async getUsageHistory(e,t=6){let r=this.getLastNMonths(t),{data:s,error:a}=await this.supabase.from("usage_tracking").select("*").eq("user_id",e).in("month_year",r).order("month_year",{ascending:!1});if(a)throw console.error("Failed to get usage history:",a),a;return(s||[]).map(e=>({postsCreated:e.posts_created||0,postsScheduled:e.posts_scheduled||0,apiCalls:e.api_calls||0,storageUsedMb:e.storage_used_mb||0,aiGenerationsUsed:e.ai_generations_used||0,reportsGenerated:e.reports_generated||0}))}async resetUsageForNewPeriod(e){let t=this.getCurrentMonthYear(),{error:r}=await this.supabase.from("usage_tracking").upsert({user_id:e,month_year:t,posts_created:0,posts_scheduled:0,api_calls:0,storage_used_mb:0,ai_generations_used:0,reports_generated:0},{onConflict:"user_id,month_year"});if(r)throw console.error("Failed to reset usage:",r),r}getCurrentMonthYear(){let e=new Date,t=e.getFullYear(),r=(e.getMonth()+1).toString().padStart(2,"0");return`${t}-${r}`}getLastNMonths(e){let t=[],r=new Date;for(let s=0;s<e;s++){let e=new Date(r.getFullYear(),r.getMonth()-s,1),a=e.getFullYear(),o=(e.getMonth()+1).toString().padStart(2,"0");t.push(`${a}-${o}`)}return t}async checkLimitExceeded(e){let[t,r]=await Promise.all([this.getCurrentUsage(e),this.getUserPlanLimits(e)]),s=[],a=!1;if(null!==r.postsPerMonth){let e=t.postsCreated+t.postsScheduled,o=e/r.postsPerMonth*100;s.push({type:"posts",current:e,limit:r.postsPerMonth,percentage:o}),o>=100&&(a=!0)}if(null!==r.aiGenerations){let e=t.aiGenerationsUsed/r.aiGenerations*100;s.push({type:"ai_generations",current:t.aiGenerationsUsed,limit:r.aiGenerations,percentage:e}),e>=100&&(a=!0)}if(null!==r.reportsPerMonth){let e=t.reportsGenerated/r.reportsPerMonth*100;s.push({type:"reports",current:t.reportsGenerated,limit:r.reportsPerMonth,percentage:e}),e>=100&&(a=!0)}return{exceeded:a,limits:s}}constructor(){this.supabase=(0,u.HK)()}}async function d(e){let t=(0,l.createClient)();try{let{data:{user:e},error:r}=await t.auth.getUser();if(r||!e)return i.NextResponse.json({error:"Unauthorized"},{status:401});let{data:s,error:a}=await t.from("users").select(`
        id,
        email,
        name,
        subscription_status,
        trial_ends_at,
        current_plan_id,
        subscription_plans!inner(
          id,
          name,
          name_ar,
          description,
          description_ar,
          price_monthly,
          price_yearly,
          max_social_accounts,
          max_users,
          max_posts_per_month,
          features,
          plan_type
        )
      `).eq("id",e.id).single();if(a)return console.error("Failed to get user data:",a),i.NextResponse.json({error:"Failed to get user data"},{status:500});let{data:o,error:n}=await t.from("user_subscriptions").select("*").eq("user_id",e.id).single(),l=new p,[u,c,d]=await Promise.all([l.getCurrentUsage(e.id),l.getUserPlanLimits(e.id),l.checkLimitExceeded(e.id)]),{count:_}=await t.from("social_accounts").select("*",{count:"exact",head:!0}).eq("user_id",e.id),{count:h}=await t.from("team_members").select("*",{count:"exact",head:!0}).eq("user_id",e.id).eq("status","active");return i.NextResponse.json({success:!0,user:{id:s.id,email:s.email,name:s.name,subscriptionStatus:s.subscription_status,trialEndsAt:s.trial_ends_at},plan:s.subscription_plans,subscription:o||null,usage:{current:u,limits:c,limitStatus:d,socialAccounts:_||0,teamMembers:(h||0)+1},features:s.subscription_plans?.features||{}})}catch(e){return console.error("Subscription status error:",e),i.NextResponse.json({error:"Failed to get subscription status",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}async function _(){return new i.NextResponse(null,{status:200,headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"GET, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization"}})}let h=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/stripe/subscription-status/route",pathname:"/api/stripe/subscription-status",filename:"route",bundlePath:"app/api/stripe/subscription-status/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Ewasl\\eWasl.com_Cursor_Digital_Ocean\\ewasl-app\\src\\app\\api\\stripe\\subscription-status\\route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:m,workUnitAsyncStorage:g,serverHooks:E}=h;function P(){return(0,n.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:g})}},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{e.exports=require("punycode")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{e.exports=require("tls")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},80726:(e,t,r)=>{r.d(t,{HK:()=>a,sY:()=>o});var s=r(66167);function a(){let e="https://nnxfzhxqzmriggulsudr.supabase.co",t=process.env.SUPABASE_SERVICE_ROLE_KEY||"placeholder-key";if(!e)throw Error("Missing NEXT_PUBLIC_SUPABASE_URL environment variable");return process.env.SUPABASE_SERVICE_ROLE_KEY||console.warn("SUPABASE_SERVICE_ROLE_KEY environment variable not set - using anon key as fallback"),(0,s.createClient)(e,t,{auth:{autoRefreshToken:!1,persistSession:!1}})}let o=a(),n=process.env.SUPABASE_SERVICE_ROLE_KEY||"placeholder-key";(0,s.createClient)("https://nnxfzhxqzmriggulsudr.supabase.co",n,{auth:{autoRefreshToken:!1,persistSession:!1}})},81630:e=>{e.exports=require("http")},85211:(e,t,r)=>{r.d(t,{CV:()=>l,J0:()=>o,ND:()=>n,b9:()=>u,sF:()=>i});var s=r(97877);r(9365);let a=process.env.STRIPE_SECRET_KEY?new s.A(process.env.STRIPE_SECRET_KEY,{apiVersion:"2025-05-28.basil",typescript:!0}):null,o=()=>{if(!a)throw Error("Stripe not configured: STRIPE_SECRET_KEY environment variable is not set");return a},n={FREE:{id:"free",name:"Free",nameAr:"مجاني",description:"Basic social media management for individuals",descriptionAr:"إدارة أساسية لوسائل التواصل الاجتماعي للأفراد",priceMonthly:0,priceYearly:0,maxSocialAccounts:2,maxUsers:1,maxPostsPerMonth:10,features:{basicScheduling:!0,basicAnalytics:!0,arabicSupport:!0,rtlSupport:!0,imageUpload:!0,basicNotifications:!0},stripePriceIds:{monthly:null,yearly:null}},PRO:{id:"pro",name:"Pro",nameAr:"المحترف",description:"Ideal for individuals and startups",descriptionAr:"مثالي للأفراد والشركات الناشئة",priceMonthly:9,priceYearly:90,maxSocialAccounts:5,maxUsers:2,maxPostsPerMonth:null,popular:!0,features:{socialAccounts5:!0,unlimitedPosts:!0,users2:!0,basicAnalytics:!0,arabicSupport:!0,postScheduling:!0,basicAiContent:!0,calendarView:!0,imageVideoUpload:!0,rtlSupport:!0,commentReplies:!0,basicNotifications:!0},featuresAr:["إدارة 5 حسابات اجتماعية","عدد غير محدود من المنشورات","مستخدمان فقط","تقارير وتحليلات أساسية","دعم النصوص باللغة العربية","إنشاء المنشورات وجدولتها","توليد منشورات AI (أساسي)","عرض تقويم بسيط وجدولة ذكية","تحميل الصور ومقاطع الفيديو","دعم RTL وخطوط عربية","الرد على تعليقات ومتابعة النشاط","إشعارات بسيطة ومركز إشعار مصغر"],stripePriceIds:{monthly:"price_1RUYNWEpEYvJL85Mqc51HFuS",yearly:"price_1RUYNWEpEYvJL85MjMachouR"},stripeProductId:"prod_SPN7y03W0tVZHq",lookupKeys:{monthly:"ewasl_pro_monthly",yearly:"ewasl_pro_yearly"}},BUSINESS:{id:"business",name:"Business",nameAr:"الأعمال",description:"Perfect for growing teams and agencies",descriptionAr:"مناسب للشركات المتنامية والوكالات الصغيرة",priceMonthly:25,priceYearly:250,maxSocialAccounts:10,maxUsers:5,maxPostsPerMonth:null,features:{allProFeatures:!0,socialAccounts10:!0,users5:!0,dragDropScheduling:!0,advancedAnalytics:!0,contentLibrary:!0,advancedAiContent:!0,realtimeNotifications:!0,dataExport:!0,prioritySupport:!0,automatedReports:!0},featuresAr:["كل ميزات خطة المحترف، بالإضافة إلى:","إدارة 10 حسابات اجتماعية","حتى 5 مستخدمين","جدولة متقدمة بالسحب والإفلات","تحليل أداء متقدم (المنشورات، المتابعين، المقارنة بين المنصات)","مكتبة محتوى وقوالب جاهزة للنشر","دعم توليد محتوى AI متقدم","إشعارات فورية وتحليل النشاط في الوقت الحقيقي","تصدير البيانات بصيغ CSV / PDF / Excel","دعم مخصص بأولوية عالية","تقارير أسبوعية تلقائية"],stripePriceIds:{monthly:process.env.STRIPE_PRICE_ID_BUSINESS_MONTHLY,yearly:process.env.STRIPE_PRICE_ID_BUSINESS_YEARLY}},ENTERPRISE:{id:"enterprise",name:"Enterprise",nameAr:"المؤسسات",description:"For large organizations and marketing agencies",descriptionAr:"للشركات الكبرى والوكالات",priceMonthly:null,priceYearly:null,maxSocialAccounts:-1,maxUsers:-1,maxPostsPerMonth:null,customPricing:!0,contactText:"راسلنا للحصول على عرض مخصص",features:{unlimitedAccounts:!0,unlimitedUsers:!0,customAnalytics:!0,integrations:!0,adminDashboard:!0,directTraining:!0,uiCustomization:!0,advancedSecurity:!0},featuresAr:["عدد غير محدود من الحسابات","عدد غير محدود من المستخدمين","تحليلات مخصصة وتقارير شاملة","دعم تكاملات إضافية (CRM، Zapier، WhatsApp API)","لوحة تحكم إدارية موسعة","تدريب ودعم مباشر","تخصيص كامل للواجهة والتقارير","طبقة أمان متقدمة (SSO، إدارة أذونات دقيقة)"],stripePriceIds:{monthly:null,yearly:null}}};function i(e){return Object.values(n).find(t=>t.stripePriceIds.monthly===e||t.stripePriceIds.yearly===e)}let l={CUSTOMER_SUBSCRIPTION_CREATED:"customer.subscription.created",CUSTOMER_SUBSCRIPTION_UPDATED:"customer.subscription.updated",CUSTOMER_SUBSCRIPTION_DELETED:"customer.subscription.deleted",INVOICE_PAYMENT_SUCCEEDED:"invoice.payment_succeeded",INVOICE_PAYMENT_FAILED:"invoice.payment_failed",CUSTOMER_CREATED:"customer.created",CUSTOMER_UPDATED:"customer.updated"},u={FREE:{postsPerMonth:10,socialAccounts:2,users:1,aiGenerations:5,reportsPerMonth:1},PRO:{postsPerMonth:null,socialAccounts:5,users:2,aiGenerations:50,reportsPerMonth:10},BUSINESS:{postsPerMonth:null,socialAccounts:10,users:5,aiGenerations:200,reportsPerMonth:null},ENTERPRISE:{postsPerMonth:null,socialAccounts:null,users:null,aiGenerations:null,reportsPerMonth:null}}},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4243,6167,580,4386,7449,1053],()=>r(7393));module.exports=s})();