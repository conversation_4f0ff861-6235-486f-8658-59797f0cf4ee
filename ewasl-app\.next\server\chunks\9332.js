"use strict";exports.id=9332,exports.ids=[9332],exports.modules={14137:(t,e,n)=>{n.d(e,{k:()=>r});function r(t){return (e={})=>{let n=e.width?String(e.width):t.defaultWidth;return t.formats[n]||t.formats[t.defaultWidth]}}},21509:(t,e,n)=>{n.d(e,{K:()=>r});function r(t){return(e,n={})=>{let r=e.match(t.matchPattern);if(!r)return null;let a=r[0],i=e.match(t.parsePattern);if(!i)return null;let o=t.valueCallback?t.valueCallback(i[0]):i[0];return{value:o=n.valueCallback?n.valueCallback(o):o,rest:e.slice(a.length)}}}},23711:(t,e,n)=>{n.d(e,{a:()=>a});var r=n(87981);function a(t,e){return(0,r.w)(e||t,t)}},29175:(t,e,n)=>{n.d(e,{c:()=>l});let r={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}};var a=n(14137);let i={date:(0,a.k)({formats:{full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},defaultWidth:"full"}),time:(0,a.k)({formats:{full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},defaultWidth:"full"}),dateTime:(0,a.k)({formats:{full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})},o={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"};var u=n(54755);let s={ordinalNumber:(t,e)=>{let n=Number(t),r=n%100;if(r>20||r<10)switch(r%10){case 1:return n+"st";case 2:return n+"nd";case 3:return n+"rd"}return n+"th"},era:(0,u.o)({values:{narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},defaultWidth:"wide"}),quarter:(0,u.o)({values:{narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},defaultWidth:"wide",argumentCallback:t=>t-1}),month:(0,u.o)({values:{narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},defaultWidth:"wide"}),day:(0,u.o)({values:{narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},defaultWidth:"wide"}),dayPeriod:(0,u.o)({values:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},defaultWidth:"wide",formattingValues:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},defaultFormattingWidth:"wide"})};var d=n(86787);let l={code:"en-US",formatDistance:(t,e,n)=>{let a,i=r[t];if(a="string"==typeof i?i:1===e?i.one:i.other.replace("{{count}}",e.toString()),n?.addSuffix)if(n.comparison&&n.comparison>0)return"in "+a;else return a+" ago";return a},formatLong:i,formatRelative:(t,e,n,r)=>o[t],localize:s,match:{ordinalNumber:(0,n(21509).K)({matchPattern:/^(\d+)(th|st|nd|rd)?/i,parsePattern:/\d+/i,valueCallback:t=>parseInt(t,10)}),era:(0,d.A)({matchPatterns:{narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^b/i,/^(a|c)/i]},defaultParseWidth:"any"}),quarter:(0,d.A)({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:t=>t+1}),month:(0,d.A)({matchPatterns:{narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},defaultParseWidth:"any"}),day:(0,d.A)({matchPatterns:{narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},defaultParseWidth:"any"}),dayPeriod:(0,d.A)({matchPatterns:{narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},defaultParseWidth:"any"})},options:{weekStartsOn:0,firstWeekContainsDate:1}}},29789:(t,e,n)=>{n.d(e,{x:()=>a});var r=n(87981);function a(t,...e){let n=r.w.bind(null,t||e.find(t=>"object"==typeof t));return e.map(n)}},30319:(t,e,n)=>{n.d(e,{o:()=>a});var r=n(23711);function a(t,e){let n=(0,r.a)(t,e?.in);return n.setHours(0,0,0,0),n}},31504:(t,e,n)=>{n.d(e,{G:()=>a});var r=n(23711);function a(t){let e=(0,r.a)(t),n=new Date(Date.UTC(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours(),e.getMinutes(),e.getSeconds(),e.getMilliseconds()));return n.setUTCFullYear(e.getFullYear()),t-n}},54755:(t,e,n)=>{n.d(e,{o:()=>r});function r(t){return(e,n)=>{let r;if("formatting"===(n?.context?String(n.context):"standalone")&&t.formattingValues){let e=t.defaultFormattingWidth||t.defaultWidth,a=n?.width?String(n.width):e;r=t.formattingValues[a]||t.formattingValues[e]}else{let e=t.defaultWidth,a=n?.width?String(n.width):t.defaultWidth;r=t.values[a]||t.values[e]}return r[t.argumentCallback?t.argumentCallback(e):e]}}},58505:(t,e,n)=>{n.d(e,{Cg:()=>i,F6:()=>s,Nw:()=>u,_P:()=>d,my:()=>r,s0:()=>o,w4:()=>a});let r=6048e5,a=864e5,i=6e4,o=36e5,u=43200,s=1440,d=Symbol.for("constructDateFrom")},78872:(t,e,n)=>{n.d(e,{q:()=>a});let r={};function a(){return r}},86787:(t,e,n)=>{function r(t){return(e,n={})=>{let r,a=n.width,i=a&&t.matchPatterns[a]||t.matchPatterns[t.defaultMatchWidth],o=e.match(i);if(!o)return null;let u=o[0],s=a&&t.parsePatterns[a]||t.parsePatterns[t.defaultParseWidth],d=Array.isArray(s)?function(t,e){for(let n=0;n<t.length;n++)if(e(t[n]))return n}(s,t=>t.test(u)):function(t,e){for(let n in t)if(Object.prototype.hasOwnProperty.call(t,n)&&e(t[n]))return n}(s,t=>t.test(u));return r=t.valueCallback?t.valueCallback(d):d,{value:r=n.valueCallback?n.valueCallback(r):r,rest:e.slice(u.length)}}}n.d(e,{A:()=>r})},87981:(t,e,n)=>{n.d(e,{w:()=>a});var r=n(58505);function a(t,e){return"function"==typeof t?t(e):t&&"object"==typeof t&&r._P in t?t[r._P](e):t instanceof Date?new t.constructor(e):new Date(e)}},89332:(t,e,n)=>{n.d(e,{GP:()=>F});var r=n(29175),a=n(78872),i=n(31504),o=n(29789),u=n(58505),s=n(30319),d=n(23711);function l(t,e){let n=(0,a.q)(),r=e?.weekStartsOn??e?.locale?.options?.weekStartsOn??n.weekStartsOn??n.locale?.options?.weekStartsOn??0,i=(0,d.a)(t,e?.in),o=i.getDay();return i.setDate(i.getDate()-(7*(o<r)+o-r)),i.setHours(0,0,0,0),i}function c(t,e){return l(t,{...e,weekStartsOn:1})}var h=n(87981);function m(t,e){let n=(0,d.a)(t,e?.in),r=n.getFullYear(),a=(0,h.w)(n,0);a.setFullYear(r+1,0,4),a.setHours(0,0,0,0);let i=c(a),o=(0,h.w)(n,0);o.setFullYear(r,0,4),o.setHours(0,0,0,0);let u=c(o);return n.getTime()>=i.getTime()?r+1:n.getTime()>=u.getTime()?r:r-1}function f(t,e){let n=(0,d.a)(t,e?.in),r=n.getFullYear(),i=(0,a.q)(),o=e?.firstWeekContainsDate??e?.locale?.options?.firstWeekContainsDate??i.firstWeekContainsDate??i.locale?.options?.firstWeekContainsDate??1,u=(0,h.w)(e?.in||t,0);u.setFullYear(r+1,0,o),u.setHours(0,0,0,0);let s=l(u,e),c=(0,h.w)(e?.in||t,0);c.setFullYear(r,0,o),c.setHours(0,0,0,0);let m=l(c,e);return+n>=+s?r+1:+n>=+m?r:r-1}function g(t,e){let n=Math.abs(t).toString().padStart(e,"0");return(t<0?"-":"")+n}let w={y(t,e){let n=t.getFullYear(),r=n>0?n:1-n;return g("yy"===e?r%100:r,e.length)},M(t,e){let n=t.getMonth();return"M"===e?String(n+1):g(n+1,2)},d:(t,e)=>g(t.getDate(),e.length),a(t,e){let n=t.getHours()/12>=1?"pm":"am";switch(e){case"a":case"aa":return n.toUpperCase();case"aaa":return n;case"aaaaa":return n[0];default:return"am"===n?"a.m.":"p.m."}},h:(t,e)=>g(t.getHours()%12||12,e.length),H:(t,e)=>g(t.getHours(),e.length),m:(t,e)=>g(t.getMinutes(),e.length),s:(t,e)=>g(t.getSeconds(),e.length),S(t,e){let n=e.length;return g(Math.trunc(t.getMilliseconds()*Math.pow(10,n-3)),e.length)}},b={midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},y={G:function(t,e,n){let r=+(t.getFullYear()>0);switch(e){case"G":case"GG":case"GGG":return n.era(r,{width:"abbreviated"});case"GGGGG":return n.era(r,{width:"narrow"});default:return n.era(r,{width:"wide"})}},y:function(t,e,n){if("yo"===e){let e=t.getFullYear();return n.ordinalNumber(e>0?e:1-e,{unit:"year"})}return w.y(t,e)},Y:function(t,e,n,r){let a=f(t,r),i=a>0?a:1-a;return"YY"===e?g(i%100,2):"Yo"===e?n.ordinalNumber(i,{unit:"year"}):g(i,e.length)},R:function(t,e){return g(m(t),e.length)},u:function(t,e){return g(t.getFullYear(),e.length)},Q:function(t,e,n){let r=Math.ceil((t.getMonth()+1)/3);switch(e){case"Q":return String(r);case"QQ":return g(r,2);case"Qo":return n.ordinalNumber(r,{unit:"quarter"});case"QQQ":return n.quarter(r,{width:"abbreviated",context:"formatting"});case"QQQQQ":return n.quarter(r,{width:"narrow",context:"formatting"});default:return n.quarter(r,{width:"wide",context:"formatting"})}},q:function(t,e,n){let r=Math.ceil((t.getMonth()+1)/3);switch(e){case"q":return String(r);case"qq":return g(r,2);case"qo":return n.ordinalNumber(r,{unit:"quarter"});case"qqq":return n.quarter(r,{width:"abbreviated",context:"standalone"});case"qqqqq":return n.quarter(r,{width:"narrow",context:"standalone"});default:return n.quarter(r,{width:"wide",context:"standalone"})}},M:function(t,e,n){let r=t.getMonth();switch(e){case"M":case"MM":return w.M(t,e);case"Mo":return n.ordinalNumber(r+1,{unit:"month"});case"MMM":return n.month(r,{width:"abbreviated",context:"formatting"});case"MMMMM":return n.month(r,{width:"narrow",context:"formatting"});default:return n.month(r,{width:"wide",context:"formatting"})}},L:function(t,e,n){let r=t.getMonth();switch(e){case"L":return String(r+1);case"LL":return g(r+1,2);case"Lo":return n.ordinalNumber(r+1,{unit:"month"});case"LLL":return n.month(r,{width:"abbreviated",context:"standalone"});case"LLLLL":return n.month(r,{width:"narrow",context:"standalone"});default:return n.month(r,{width:"wide",context:"standalone"})}},w:function(t,e,n,r){let i=function(t,e){let n=(0,d.a)(t,e?.in);return Math.round((l(n,e)-function(t,e){let n=(0,a.q)(),r=e?.firstWeekContainsDate??e?.locale?.options?.firstWeekContainsDate??n.firstWeekContainsDate??n.locale?.options?.firstWeekContainsDate??1,i=f(t,e),o=(0,h.w)(e?.in||t,0);return o.setFullYear(i,0,r),o.setHours(0,0,0,0),l(o,e)}(n,e))/u.my)+1}(t,r);return"wo"===e?n.ordinalNumber(i,{unit:"week"}):g(i,e.length)},I:function(t,e,n){let r=function(t,e){let n=(0,d.a)(t,void 0);return Math.round((c(n)-function(t,e){let n=m(t,void 0),r=(0,h.w)(t,0);return r.setFullYear(n,0,4),r.setHours(0,0,0,0),c(r)}(n))/u.my)+1}(t);return"Io"===e?n.ordinalNumber(r,{unit:"week"}):g(r,e.length)},d:function(t,e,n){return"do"===e?n.ordinalNumber(t.getDate(),{unit:"date"}):w.d(t,e)},D:function(t,e,n){let r=function(t,e){let n=(0,d.a)(t,void 0);return function(t,e,n){let[r,a]=(0,o.x)(void 0,t,e),d=(0,s.o)(r),l=(0,s.o)(a);return Math.round((d-(0,i.G)(d)-(l-(0,i.G)(l)))/u.w4)}(n,function(t,e){let n=(0,d.a)(t,void 0);return n.setFullYear(n.getFullYear(),0,1),n.setHours(0,0,0,0),n}(n))+1}(t);return"Do"===e?n.ordinalNumber(r,{unit:"dayOfYear"}):g(r,e.length)},E:function(t,e,n){let r=t.getDay();switch(e){case"E":case"EE":case"EEE":return n.day(r,{width:"abbreviated",context:"formatting"});case"EEEEE":return n.day(r,{width:"narrow",context:"formatting"});case"EEEEEE":return n.day(r,{width:"short",context:"formatting"});default:return n.day(r,{width:"wide",context:"formatting"})}},e:function(t,e,n,r){let a=t.getDay(),i=(a-r.weekStartsOn+8)%7||7;switch(e){case"e":return String(i);case"ee":return g(i,2);case"eo":return n.ordinalNumber(i,{unit:"day"});case"eee":return n.day(a,{width:"abbreviated",context:"formatting"});case"eeeee":return n.day(a,{width:"narrow",context:"formatting"});case"eeeeee":return n.day(a,{width:"short",context:"formatting"});default:return n.day(a,{width:"wide",context:"formatting"})}},c:function(t,e,n,r){let a=t.getDay(),i=(a-r.weekStartsOn+8)%7||7;switch(e){case"c":return String(i);case"cc":return g(i,e.length);case"co":return n.ordinalNumber(i,{unit:"day"});case"ccc":return n.day(a,{width:"abbreviated",context:"standalone"});case"ccccc":return n.day(a,{width:"narrow",context:"standalone"});case"cccccc":return n.day(a,{width:"short",context:"standalone"});default:return n.day(a,{width:"wide",context:"standalone"})}},i:function(t,e,n){let r=t.getDay(),a=0===r?7:r;switch(e){case"i":return String(a);case"ii":return g(a,e.length);case"io":return n.ordinalNumber(a,{unit:"day"});case"iii":return n.day(r,{width:"abbreviated",context:"formatting"});case"iiiii":return n.day(r,{width:"narrow",context:"formatting"});case"iiiiii":return n.day(r,{width:"short",context:"formatting"});default:return n.day(r,{width:"wide",context:"formatting"})}},a:function(t,e,n){let r=t.getHours()/12>=1?"pm":"am";switch(e){case"a":case"aa":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"});case"aaa":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return n.dayPeriod(r,{width:"narrow",context:"formatting"});default:return n.dayPeriod(r,{width:"wide",context:"formatting"})}},b:function(t,e,n){let r,a=t.getHours();switch(r=12===a?b.noon:0===a?b.midnight:a/12>=1?"pm":"am",e){case"b":case"bb":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"});case"bbb":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return n.dayPeriod(r,{width:"narrow",context:"formatting"});default:return n.dayPeriod(r,{width:"wide",context:"formatting"})}},B:function(t,e,n){let r,a=t.getHours();switch(r=a>=17?b.evening:a>=12?b.afternoon:a>=4?b.morning:b.night,e){case"B":case"BB":case"BBB":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"});case"BBBBB":return n.dayPeriod(r,{width:"narrow",context:"formatting"});default:return n.dayPeriod(r,{width:"wide",context:"formatting"})}},h:function(t,e,n){if("ho"===e){let e=t.getHours()%12;return 0===e&&(e=12),n.ordinalNumber(e,{unit:"hour"})}return w.h(t,e)},H:function(t,e,n){return"Ho"===e?n.ordinalNumber(t.getHours(),{unit:"hour"}):w.H(t,e)},K:function(t,e,n){let r=t.getHours()%12;return"Ko"===e?n.ordinalNumber(r,{unit:"hour"}):g(r,e.length)},k:function(t,e,n){let r=t.getHours();return(0===r&&(r=24),"ko"===e)?n.ordinalNumber(r,{unit:"hour"}):g(r,e.length)},m:function(t,e,n){return"mo"===e?n.ordinalNumber(t.getMinutes(),{unit:"minute"}):w.m(t,e)},s:function(t,e,n){return"so"===e?n.ordinalNumber(t.getSeconds(),{unit:"second"}):w.s(t,e)},S:function(t,e){return w.S(t,e)},X:function(t,e,n){let r=t.getTimezoneOffset();if(0===r)return"Z";switch(e){case"X":return v(r);case"XXXX":case"XX":return M(r);default:return M(r,":")}},x:function(t,e,n){let r=t.getTimezoneOffset();switch(e){case"x":return v(r);case"xxxx":case"xx":return M(r);default:return M(r,":")}},O:function(t,e,n){let r=t.getTimezoneOffset();switch(e){case"O":case"OO":case"OOO":return"GMT"+p(r,":");default:return"GMT"+M(r,":")}},z:function(t,e,n){let r=t.getTimezoneOffset();switch(e){case"z":case"zz":case"zzz":return"GMT"+p(r,":");default:return"GMT"+M(r,":")}},t:function(t,e,n){return g(Math.trunc(t/1e3),e.length)},T:function(t,e,n){return g(+t,e.length)}};function p(t,e=""){let n=t>0?"-":"+",r=Math.abs(t),a=Math.trunc(r/60),i=r%60;return 0===i?n+String(a):n+String(a)+e+g(i,2)}function v(t,e){return t%60==0?(t>0?"-":"+")+g(Math.abs(t)/60,2):M(t,e)}function M(t,e=""){let n=Math.abs(t);return(t>0?"-":"+")+g(Math.trunc(n/60),2)+e+g(n%60,2)}let k=(t,e)=>{switch(t){case"P":return e.date({width:"short"});case"PP":return e.date({width:"medium"});case"PPP":return e.date({width:"long"});default:return e.date({width:"full"})}},x=(t,e)=>{switch(t){case"p":return e.time({width:"short"});case"pp":return e.time({width:"medium"});case"ppp":return e.time({width:"long"});default:return e.time({width:"full"})}},P={p:x,P:(t,e)=>{let n,r=t.match(/(P+)(p+)?/)||[],a=r[1],i=r[2];if(!i)return k(t,e);switch(a){case"P":n=e.dateTime({width:"short"});break;case"PP":n=e.dateTime({width:"medium"});break;case"PPP":n=e.dateTime({width:"long"});break;default:n=e.dateTime({width:"full"})}return n.replace("{{date}}",k(a,e)).replace("{{time}}",x(i,e))}},S=/^D+$/,W=/^Y+$/,D=["D","DD","YY","YYYY"],T=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,Y=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,q=/^'([^]*?)'?$/,C=/''/g,O=/[a-zA-Z]/;function F(t,e,n){let i=(0,a.q)(),o=n?.locale??i.locale??r.c,u=n?.firstWeekContainsDate??n?.locale?.options?.firstWeekContainsDate??i.firstWeekContainsDate??i.locale?.options?.firstWeekContainsDate??1,s=n?.weekStartsOn??n?.locale?.options?.weekStartsOn??i.weekStartsOn??i.locale?.options?.weekStartsOn??0,l=(0,d.a)(t,n?.in);if(!(l instanceof Date||"object"==typeof l&&"[object Date]"===Object.prototype.toString.call(l))&&"number"!=typeof l||isNaN(+(0,d.a)(l)))throw RangeError("Invalid time value");let c=e.match(Y).map(t=>{let e=t[0];return"p"===e||"P"===e?(0,P[e])(t,o.formatLong):t}).join("").match(T).map(t=>{if("''"===t)return{isToken:!1,value:"'"};let e=t[0];if("'"===e)return{isToken:!1,value:function(t){let e=t.match(q);return e?e[1].replace(C,"'"):t}(t)};if(y[e])return{isToken:!0,value:t};if(e.match(O))throw RangeError("Format string contains an unescaped latin alphabet character `"+e+"`");return{isToken:!1,value:t}});o.localize.preprocessor&&(c=o.localize.preprocessor(l,c));let h={firstWeekContainsDate:u,weekStartsOn:s,locale:o};return c.map(r=>{if(!r.isToken)return r.value;let a=r.value;return(!n?.useAdditionalWeekYearTokens&&W.test(a)||!n?.useAdditionalDayOfYearTokens&&S.test(a))&&function(t,e,n){let r=function(t,e,n){let r="Y"===t[0]?"years":"days of the month";return`Use \`${t.toLowerCase()}\` instead of \`${t}\` (in \`${e}\`) for formatting ${r} to the input \`${n}\`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md`}(t,e,n);if(console.warn(r),D.includes(t))throw RangeError(r)}(a,e,String(t)),(0,y[a[0]])(l,a,o.localize,h)}).join("")}}};