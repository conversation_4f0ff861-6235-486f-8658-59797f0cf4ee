"use strict";(()=>{var e={};e.id=8876,e.ids=[8876],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{e.exports=require("punycode")},18244:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>x,routeModule:()=>f,serverHooks:()=>S,workAsyncStorage:()=>w,workUnitAsyncStorage:()=>y});var a={};r.r(a),r.d(a,{GET:()=>u});var n=r(96559),o=r(48088),s=r(37719),c=r(32190),i=r(2507),d=r(85535);async function u(e){try{let{searchParams:t}=new URL(e.url),r=t.get("timeRange")||"7d",a=(0,i.createClient)(),{data:{user:n},error:o}=await a.auth.getUser();if(o||!n)return c.NextResponse.json({error:"Unauthorized"},{status:401});let s=new Date,u="7d"===r?7:"30d"===r?30:90,f=new Date(s);f.setDate(f.getDate()-u);let w=await l(a,n.id,f),y=await p(a,n.id,f),S=await g(a,n.id,f),x=await h(a,n.id,f,u),q=await _(a,n.id,f),v=await m(a,n.id),D={overview:w,trends:y,platformBreakdown:S,engagementTrends:x,topPerformingPosts:q,realtimeActivity:v};return d.v.info("Enhanced dashboard metrics fetched",{component:"enhanced-dashboard-api",userId:n.id,timeRange:r,metricsCount:Object.keys(D).length}),c.NextResponse.json({success:!0,metrics:D,timestamp:new Date().toISOString(),timeRange:r})}catch(e){return d.v.error("Enhanced dashboard API error",{component:"enhanced-dashboard-api",error:e instanceof Error?e.message:"Unknown error"}),c.NextResponse.json({error:"Failed to fetch dashboard metrics",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}async function l(e,t,r){try{let{data:a,error:n}=await e.from("posts").select("id, status, created_at").eq("user_id",t);if(n)throw n;let{data:o,error:s}=await e.from("social_accounts").select("id, platform, status").eq("user_id",t).eq("status","connected");if(s)throw s;let{data:c,error:i}=await e.from("post_analytics").select("engagement_count, reach_count, impressions_count").eq("user_id",t).gte("created_at",r.toISOString()),d=a?.length||0,u=a?.filter(e=>"scheduled"===e.status).length||0,l=a?.filter(e=>{let t=new Date(e.created_at),r=new Date;return t.toDateString()===r.toDateString()&&"published"===e.status}).length||0,p=c?.reduce((e,t)=>e+(t.engagement_count||0),0)||0,g=c?.reduce((e,t)=>e+(t.reach_count||0),0)||0,h=c?.reduce((e,t)=>e+(t.impressions_count||0),0)||0;return{totalPosts:d,totalEngagement:p,totalReach:g,totalImpressions:h,engagementRate:h>0?p/h*100:0,connectedAccounts:o?.length||0,scheduledPosts:u,publishedToday:l}}catch(e){return d.v.error("Failed to fetch overview metrics",{error:e}),{totalPosts:0,totalEngagement:0,totalReach:0,totalImpressions:0,engagementRate:0,connectedAccounts:0,scheduledPosts:0,publishedToday:0}}}async function p(e,t,r){try{let a=Math.floor((Date.now()-r.getTime())/864e5),n=new Date(r);n.setDate(n.getDate()-a);let{data:o}=await e.from("post_analytics").select("engagement_count, reach_count, impressions_count").eq("user_id",t).gte("created_at",r.toISOString()),{data:s}=await e.from("post_analytics").select("engagement_count, reach_count, impressions_count").eq("user_id",t).gte("created_at",n.toISOString()).lt("created_at",r.toISOString()),c=o?.reduce((e,t)=>e+(t.engagement_count||0),0)||0,i=s?.reduce((e,t)=>e+(t.engagement_count||0),0)||0,d=o?.reduce((e,t)=>e+(t.reach_count||0),0)||0,u=s?.reduce((e,t)=>e+(t.reach_count||0),0)||0,l=o?.reduce((e,t)=>e+(t.impressions_count||0),0)||0,p=s?.reduce((e,t)=>e+(t.impressions_count||0),0)||0,{data:g}=await e.from("posts").select("id").eq("user_id",t).gte("created_at",r.toISOString()),{data:h}=await e.from("posts").select("id").eq("user_id",t).gte("created_at",n.toISOString()).lt("created_at",r.toISOString());return{postsGrowth:(h?.length||0)>0?((g?.length||0)-(h?.length||0))/(h?.length||0)*100:0,engagementGrowth:i>0?(c-i)/i*100:0,reachGrowth:u>0?(d-u)/u*100:0,impressionsGrowth:p>0?(l-p)/p*100:0}}catch(e){return d.v.error("Failed to fetch trends",{error:e}),{postsGrowth:0,engagementGrowth:0,reachGrowth:0,impressionsGrowth:0}}}async function g(e,t,r){try{let{data:a,error:n}=await e.from("post_social_accounts").select(`
        social_account_id,
        social_accounts!inner(platform),
        posts!inner(user_id),
        post_analytics(engagement_count, reach_count)
      `).eq("posts.user_id",t).gte("posts.created_at",r.toISOString());if(n)throw n;let o=new Map;return a?.forEach(e=>{let t=e.social_accounts.platform;o.has(t)||o.set(t,{platform:t,posts:0,engagement:0,reach:0,color:{instagram:"#E4405F",facebook:"#1877F2",twitter:"#1DA1F2",linkedin:"#0A66C2",tiktok:"#000000",youtube:"#FF0000"}[t.toLowerCase()]||"#6B7280"});let r=o.get(t);r.posts+=1,r.engagement+=e.post_analytics?.engagement_count||0,r.reach+=e.post_analytics?.reach_count||0}),Array.from(o.values())}catch(e){return d.v.error("Failed to fetch platform breakdown",{error:e}),[]}}async function h(e,t,r,a){try{let n=[];for(let o=0;o<a;o++){let a=new Date(r);a.setDate(a.getDate()+o);let s=new Date(a);s.setDate(s.getDate()+1);let{data:c}=await e.from("post_analytics").select("engagement_count, reach_count, impressions_count").eq("user_id",t).gte("created_at",a.toISOString()).lt("created_at",s.toISOString()),{data:i}=await e.from("posts").select("id").eq("user_id",t).gte("created_at",a.toISOString()).lt("created_at",s.toISOString());n.push({date:a.toISOString().split("T")[0],engagement:c?.reduce((e,t)=>e+(t.engagement_count||0),0)||0,reach:c?.reduce((e,t)=>e+(t.reach_count||0),0)||0,impressions:c?.reduce((e,t)=>e+(t.impressions_count||0),0)||0,posts:i?.length||0})}return n}catch(e){return d.v.error("Failed to fetch engagement trends",{error:e}),[]}}async function _(e,t,r){try{let{data:a,error:n}=await e.from("posts").select(`
        id,
        content,
        created_at,
        post_social_accounts!inner(
          social_accounts!inner(platform)
        ),
        post_analytics(engagement_count, reach_count)
      `).eq("user_id",t).gte("created_at",r.toISOString()).order("created_at",{ascending:!1}).limit(5);if(n)throw n;return a?.map(e=>({id:e.id,content:e.content?.substring(0,100)+"...",platform:e.post_social_accounts[0]?.social_accounts?.platform||"Unknown",engagement:e.post_analytics?.engagement_count||0,reach:e.post_analytics?.reach_count||0,publishedAt:e.created_at}))||[]}catch(e){return d.v.error("Failed to fetch top performing posts",{error:e}),[]}}async function m(e,t){try{let{data:r,error:a}=await e.from("activities").select("id, type, description, created_at, metadata").eq("user_id",t).order("created_at",{ascending:!1}).limit(10);if(a)throw a;return r?.map(e=>({id:e.id,type:e.type,message:e.description,timestamp:e.created_at,platform:e.metadata?.platform}))||[]}catch(e){return d.v.error("Failed to fetch real-time activity",{error:e}),[]}}let f=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/analytics/enhanced-dashboard/route",pathname:"/api/analytics/enhanced-dashboard",filename:"route",bundlePath:"app/api/analytics/enhanced-dashboard/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Ewasl\\eWasl.com_Cursor_Digital_Ocean\\ewasl-app\\src\\app\\api\\analytics\\enhanced-dashboard\\route.ts",nextConfigOutput:"standalone",userland:a}),{workAsyncStorage:w,workUnitAsyncStorage:y,serverHooks:S}=f;function x(){return(0,s.patchFetch)({workAsyncStorage:w,workUnitAsyncStorage:y})}},27910:e=>{e.exports=require("stream")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{e.exports=require("tls")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4243,6167,580,4386,1053],()=>r(18244));module.exports=a})();