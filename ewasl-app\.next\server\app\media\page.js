(()=>{var e={};e.id=1049,e.ids=[1049],e.modules={2943:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(62688).A)("video",[["path",{d:"m16 13 5.223 3.482a.5.5 0 0 0 .777-.416V7.87a.5.5 0 0 0-.752-.432L16 10.5",key:"ftymec"}],["rect",{x:"2",y:"6",width:"14",height:"12",rx:"2",key:"158x01"}]])},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6943:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(62688).A)("grid-3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]])},9005:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(62688).A)("image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},13861:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},16023:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(62688).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},16189:(e,t,r)=>{"use strict";var i=r(65773);r.o(i,"useParams")&&r.d(t,{useParams:function(){return i.useParams}}),r.o(i,"usePathname")&&r.d(t,{usePathname:function(){return i.usePathname}}),r.o(i,"useRouter")&&r.d(t,{useRouter:function(){return i.useRouter}}),r.o(i,"useSearchParams")&&r.d(t,{useSearchParams:function(){return i.useSearchParams}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},25366:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(62688).A)("list",[["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M3 18h.01",key:"1tta3j"}],["path",{d:"M3 6h.01",key:"1rqtza"}],["path",{d:"M8 12h13",key:"1za7za"}],["path",{d:"M8 18h13",key:"1lx6n3"}],["path",{d:"M8 6h13",key:"ik3vkj"}]])},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},37177:(e,t,r)=>{Promise.resolve().then(r.bind(r,58703))},41862:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},50329:(e,t,r)=>{Promise.resolve().then(r.bind(r,85755))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58703:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});let i=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ewasl\\\\eWasl.com_Cursor_Digital_Ocean\\\\ewasl-app\\\\src\\\\app\\\\media\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Ewasl\\eWasl.com_Cursor_Digital_Ocean\\ewasl-app\\src\\app\\media\\page.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var i=r(31658);let a=async e=>[{type:"image/x-icon",sizes:"48x48",url:(0,i.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},85755:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>C});var i=r(60687),a=r(43210),s=r(96474),o=r(99270),l=r(6943),n=r(25366),d=r(13861),c=r(88233),p=r(41862),u=r(16023),m=r(9005),h=r(2943),g=r(52581),x=r(79481),f=r(55511);let y={randomUUID:f.randomUUID},b=new Uint8Array(256),v=b.length,w=[];for(let e=0;e<256;++e)w.push((e+256).toString(16).slice(1));let j=function(e,t,r){if(y.randomUUID&&!t&&!e)return y.randomUUID();let i=(e=e||{}).random??e.rng?.()??(v>b.length-16&&((0,f.randomFillSync)(b),v=0),b.slice(v,v+=16));if(i.length<16)throw Error("Random bytes length must be >= 16");if(i[6]=15&i[6]|64,i[8]=63&i[8]|128,t){if((r=r||0)<0||r+16>t.length)throw RangeError(`UUID byte range ${r}:${r+15} is out of buffer bounds`);for(let e=0;e<16;++e)t[r+e]=i[e];return t}return function(e,t=0){return(w[e[t+0]]+w[e[t+1]]+w[e[t+2]]+w[e[t+3]]+"-"+w[e[t+4]]+w[e[t+5]]+"-"+w[e[t+6]]+w[e[t+7]]+"-"+w[e[t+8]]+w[e[t+9]]+"-"+w[e[t+10]]+w[e[t+11]]+w[e[t+12]]+w[e[t+13]]+w[e[t+14]]+w[e[t+15]]).toLowerCase()}(i)};class k{async initializeBucket(){let{data:e}=await this.supabase.storage.listBuckets();if(!e?.some(e=>e.name===this.bucketName)){let{error:e}=await this.supabase.storage.createBucket(this.bucketName,{public:!0,allowedMimeTypes:["image/jpeg","image/png","image/gif","image/webp","video/mp4","video/mov","video/avi"],fileSizeLimit:0x3200000});if(e)throw console.error("Failed to create storage bucket:",e),Error("Failed to initialize media storage")}}validateFile(e){return e.size>0x3200000?{isValid:!1,error:`حجم الملف يتجاوز الحد الأقصى ${Math.round(50)}MB`}:["image/jpeg","image/jpg","image/png","image/gif","image/webp","video/mp4","video/mov","video/avi"].includes(e.type)?{isValid:!0}:{isValid:!1,error:"نوع الملف غير مدعوم. يُسمح بالصور والفيديوهات فقط"}}async compressImage(e,t={}){let{maxWidth:r=1920,maxHeight:i=1080,quality:a=.8}=t;return new Promise((t,s)=>{let o=document.createElement("canvas"),l=o.getContext("2d"),n=new Image;n.onload=()=>{let{width:d,height:c}=n;if(d>r||c>i){let e=Math.min(r/d,i/c);d*=e,c*=e}o.width=d,o.height=c,l?.drawImage(n,0,0,d,c),o.toBlob(r=>{r?t(new File([r],e.name,{type:e.type,lastModified:Date.now()})):s(Error("Image compression failed"))},e.type,a)},n.onerror=()=>s(Error("Failed to load image")),n.src=URL.createObjectURL(e)})}async uploadFile(e,t,r={},i){let a=this.validateFile(e);if(!a.isValid)throw Error(a.error);await this.initializeBucket();let s=e,o=e.size;if(r.compress&&e.type.startsWith("image/"))try{s=await this.compressImage(e,{maxWidth:r.maxWidth,maxHeight:r.maxHeight,quality:r.quality})}catch(e){console.warn("Image compression failed, using original file:",e)}let l=e.name.split(".").pop(),n=`${j()}.${l}`,d=r.folder||"uploads",c=`${t}/${d}/${n}`,{data:p,error:u}=await this.supabase.storage.from(this.bucketName).upload(c,s,{cacheControl:"3600",upsert:!1});if(u)throw Error(`Upload failed: ${u.message}`);let{data:m}=this.supabase.storage.from(this.bucketName).getPublicUrl(c),h={compressed:r.compress&&e.type.startsWith("image/"),originalSize:o};if(e.type.startsWith("image/"))try{let e=await this.getImageDimensions(s);h.width=e.width,h.height=e.height}catch(e){console.warn("Failed to get image dimensions:",e)}let g={id:j(),fileName:n,originalName:e.name,fileType:e.type,fileSize:s.size,publicUrl:m.publicUrl,cdnUrl:m.publicUrl,userId:t,folder:d,metadata:h,createdAt:new Date,updatedAt:new Date},{error:x}=await this.supabase.from("media_files").insert({id:g.id,user_id:t,file_name:n,original_name:e.name,file_type:e.type,file_size:s.size,public_url:m.publicUrl,folder:d,metadata:h,created_at:new Date().toISOString(),updated_at:new Date().toISOString()});if(x)throw await this.supabase.storage.from(this.bucketName).remove([c]),Error(`Failed to save media record: ${x.message}`);return g}getImageDimensions(e){return new Promise((t,r)=>{let i=new Image;i.onload=()=>{t({width:i.naturalWidth,height:i.naturalHeight})},i.onerror=r,i.src=URL.createObjectURL(e)})}async getUserMedia(e,t={}){let r=this.supabase.from("media_files").select("*",{count:"exact"}).eq("user_id",e).order("created_at",{ascending:!1});t.folder&&(r=r.eq("folder",t.folder)),t.type&&(r=r.like("file_type",`${t.type}/%`)),t.limit&&(r=r.limit(t.limit)),t.offset&&(r=r.range(t.offset,t.offset+(t.limit||10)-1));let{data:i,error:a,count:s}=await r;if(a)throw Error(`Failed to get media files: ${a.message}`);return{files:i?.map(this.mapToMediaFile)||[],total:s||0}}async deleteFile(e,t){let{data:r,error:i}=await this.supabase.from("media_files").select("*").eq("id",t).eq("user_id",e).single();if(i||!r)return!1;let a=`${e}/${r.folder}/${r.file_name}`,{error:s}=await this.supabase.storage.from(this.bucketName).remove([a]);s&&console.error("Failed to delete from storage:",s);let{error:o}=await this.supabase.from("media_files").delete().eq("id",t).eq("user_id",e);return!o}mapToMediaFile(e){return{id:e.id,fileName:e.file_name,originalName:e.original_name,fileType:e.file_type,fileSize:e.file_size,publicUrl:e.public_url,cdnUrl:e.public_url,userId:e.user_id,folder:e.folder,metadata:e.metadata,createdAt:new Date(e.created_at),updatedAt:new Date(e.updated_at)}}constructor(){this.supabase=(0,x.U)(),this.bucketName="media-files"}}function _({onUploadComplete:e,onUploadStart:t,onUploadProgress:r,acceptedTypes:s=["image/*","video/*"],maxSize:o=0x3200000,folder:l="uploads",enableCompression:n=!0,className:d="",userId:c}){let[x,f]=(0,a.useState)(!1),[y,b]=(0,a.useState)(!1),[v,w]=(0,a.useState)(0),[j,_]=(0,a.useState)(!1),[S,C]=(0,a.useState)(n),[A,D]=(0,a.useState)([.8]),[M,N]=(0,a.useState)([1920]),[z,P]=(0,a.useState)([1080]),U=(0,a.useRef)(null),q=new k,R=async i=>{if(!c)return void g.o.error("يجب تسجيل الدخول لرفع الملفات");f(!0),w(0),t?.();try{let t=q.validateFile(i);if(!t.isValid)return void g.o.error(t.error);let a=await q.uploadFile(i,c,{folder:l,compress:S&&i.type.startsWith("image/"),maxWidth:M[0],maxHeight:z[0],quality:A[0]},e=>{w(e.percentage),r?.(e.percentage)});g.o.success("تم رفع الملف بنجاح!"),e?.(a)}catch(e){console.error("Upload error:",e),g.o.error(e.message||"حدث خطأ أثناء رفع الملف")}finally{f(!1)}},F=e=>{e.preventDefault(),e.stopPropagation(),"dragenter"===e.type||"dragover"===e.type?b(!0):"dragleave"===e.type&&b(!1)},I=()=>{U.current?.click()};return(0,i.jsxs)("div",{className:`relative ${d}`,children:[(0,i.jsx)("input",{ref:U,type:"file",accept:s.join(","),onChange:e=>{e.target.files&&e.target.files[0]&&R(e.target.files[0])},className:"hidden",disabled:x}),(0,i.jsx)("div",{className:`
          border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-all
          ${y?"border-blue-500 bg-blue-50":"border-gray-300 hover:border-gray-400"}
          ${x?"opacity-50 cursor-not-allowed":""}
        `,onDragEnter:F,onDragLeave:F,onDragOver:F,onDrop:e=>{e.preventDefault(),e.stopPropagation(),b(!1),e.dataTransfer.files&&e.dataTransfer.files[0]&&R(e.dataTransfer.files[0])},onClick:x?void 0:I,children:x?(0,i.jsxs)("div",{className:"flex flex-col items-center gap-3",children:[(0,i.jsx)(p.A,{className:"h-8 w-8 animate-spin text-blue-500"}),(0,i.jsx)("p",{className:"text-sm text-gray-600",children:"جاري رفع الملف..."})]}):(0,i.jsxs)("div",{className:"flex flex-col items-center gap-3",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(u.A,{className:"h-8 w-8 text-gray-400"}),(0,i.jsx)(m.A,{className:"h-6 w-6 text-gray-400"}),(0,i.jsx)(h.A,{className:"h-6 w-6 text-gray-400"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-sm font-medium text-gray-700 mb-1",children:"اسحب وأفلت الملفات هنا أو انقر للاختيار"}),(0,i.jsx)("p",{className:"text-xs text-gray-500",children:"الصور: JPEG, PNG, GIF, WebP • الفيديو: MP4, WebM, MOV"}),(0,i.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:["الحد الأقصى: ",(e=>{if(0===e)return"0 Bytes";let t=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,t)).toFixed(2))+" "+["Bytes","KB","MB","GB"][t]})(o)]})]}),(0,i.jsxs)("div",{className:"flex gap-2",children:[(0,i.jsx)("button",{type:"button",className:"px-4 py-2 bg-blue-500 text-white text-sm rounded-md hover:bg-blue-600 transition-colors",onClick:e=>{e.stopPropagation(),I()},children:"\uD83D\uDCF7 اختيار صورة"}),(0,i.jsx)("button",{type:"button",className:"px-4 py-2 bg-purple-500 text-white text-sm rounded-md hover:bg-purple-600 transition-colors",onClick:e=>{e.stopPropagation(),I()},children:"\uD83C\uDFA5 اختيار فيديو"})]})]})}),y&&(0,i.jsx)("div",{className:"absolute inset-0 bg-blue-500 bg-opacity-10 border-2 border-blue-500 border-dashed rounded-lg flex items-center justify-center",children:(0,i.jsx)("p",{className:"text-blue-600 font-medium",children:"أفلت الملف هنا"})})]})}var S=r(16189);function C(){let[e,t]=(0,a.useState)([]),[r,p]=(0,a.useState)(!0),[u,m]=(0,a.useState)(""),[h,x]=(0,a.useState)("all"),[f,y]=(0,a.useState)("grid"),[b,v]=(0,a.useState)(!1),[w,j]=(0,a.useState)(null);(0,S.useRouter)();let k=async r=>{try{let i=await fetch("/api/media",{method:"DELETE",headers:{"Content-Type":"application/json"},body:JSON.stringify({mediaId:r})}),a=await i.json();if(!i.ok)throw Error(a.error||"Failed to delete media");t(e.filter(e=>e.id!==r)),g.o.success("تم حذف الملف بنجاح")}catch(e){console.error("Error deleting media:",e),g.o.error("فشل في حذف الملف")}},C=e=>{if(0===e)return"0 Bytes";let t=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,t)).toFixed(2))+" "+["Bytes","KB","MB","GB"][t]},A=e=>new Date(e).toLocaleDateString("ar-SA"),D=e.filter(e=>e.fileName.toLowerCase().includes(u.toLowerCase()));return(0,i.jsxs)("div",{style:{minHeight:"100vh",background:"linear-gradient(135deg, #f8fafc 0%, #e0f2fe 50%, #f3e8ff 100%)",direction:"rtl",fontFamily:"Inter, sans-serif"},children:[(0,i.jsx)("div",{style:{background:"white",borderBottom:"1px solid #e5e7eb",padding:"1rem 2rem"},children:(0,i.jsxs)("div",{style:{maxWidth:"1200px",margin:"0 auto",display:"flex",justifyContent:"space-between",alignItems:"center"},children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h1",{style:{fontSize:"1.875rem",fontWeight:"bold",background:"linear-gradient(to right, #2563eb, #9333ea)",WebkitBackgroundClip:"text",WebkitTextFillColor:"transparent",margin:0},children:"\uD83D\uDCC1 مكتبة الوسائط"}),(0,i.jsx)("p",{style:{color:"#6b7280",margin:"0.25rem 0 0 0"},children:"إدارة الصور والفيديوهات الخاصة بك"})]}),(0,i.jsxs)("button",{onClick:()=>v(!0),style:{background:"linear-gradient(to right, #2563eb, #9333ea)",color:"white",padding:"0.75rem 1.5rem",borderRadius:"0.5rem",border:"none",fontSize:"0.875rem",fontWeight:"500",cursor:"pointer",display:"flex",alignItems:"center",gap:"0.5rem"},children:[(0,i.jsx)(s.A,{className:"h-4 w-4"}),"رفع ملف جديد"]})]})}),(0,i.jsxs)("div",{style:{maxWidth:"1200px",margin:"0 auto",padding:"2rem"},children:[(0,i.jsx)("div",{style:{background:"white",borderRadius:"0.75rem",padding:"1.5rem",marginBottom:"2rem",boxShadow:"0 4px 6px -1px rgba(0, 0, 0, 0.1)"},children:(0,i.jsxs)("div",{style:{display:"flex",gap:"1rem",alignItems:"center",flexWrap:"wrap"},children:[(0,i.jsxs)("div",{style:{position:"relative",flex:"1",minWidth:"200px"},children:[(0,i.jsx)(o.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,i.jsx)("input",{type:"text",placeholder:"البحث في الملفات...",value:u,onChange:e=>m(e.target.value),style:{width:"100%",padding:"0.75rem 2.5rem 0.75rem 1rem",border:"1px solid #d1d5db",borderRadius:"0.5rem",fontSize:"0.875rem"}})]}),(0,i.jsx)("div",{style:{display:"flex",gap:"0.5rem"},children:[{key:"all",label:"الكل",icon:"\uD83D\uDCC1"},{key:"image",label:"الصور",icon:"\uD83D\uDDBC️"},{key:"video",label:"الفيديو",icon:"\uD83C\uDFA5"}].map(e=>(0,i.jsxs)("button",{onClick:()=>x(e.key),style:{padding:"0.5rem 1rem",borderRadius:"0.375rem",border:"1px solid #d1d5db",background:h===e.key?"#2563eb":"white",color:h===e.key?"white":"#374151",fontSize:"0.875rem",cursor:"pointer"},children:[e.icon," ",e.label]},e.key))}),(0,i.jsxs)("div",{style:{display:"flex",gap:"0.25rem"},children:[(0,i.jsx)("button",{onClick:()=>y("grid"),style:{padding:"0.5rem",borderRadius:"0.375rem",border:"1px solid #d1d5db",background:"grid"===f?"#2563eb":"white",color:"grid"===f?"white":"#374151",cursor:"pointer"},children:(0,i.jsx)(l.A,{className:"h-4 w-4"})}),(0,i.jsx)("button",{onClick:()=>y("list"),style:{padding:"0.5rem",borderRadius:"0.375rem",border:"1px solid #d1d5db",background:"list"===f?"#2563eb":"white",color:"list"===f?"white":"#374151",cursor:"pointer"},children:(0,i.jsx)(n.A,{className:"h-4 w-4"})})]})]})}),(0,i.jsx)("div",{style:{background:"white",borderRadius:"0.75rem",padding:"1.5rem",boxShadow:"0 4px 6px -1px rgba(0, 0, 0, 0.1)"},children:r?(0,i.jsx)("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"200px"},children:(0,i.jsxs)("div",{style:{textAlign:"center"},children:[(0,i.jsx)("div",{style:{fontSize:"2rem",marginBottom:"1rem"},children:"\uD83D\uDD04"}),(0,i.jsx)("p",{style:{color:"#6b7280",margin:0},children:"جاري التحميل..."})]})}):0===D.length?(0,i.jsxs)("div",{style:{textAlign:"center",padding:"3rem",color:"#6b7280"},children:[(0,i.jsx)("div",{style:{fontSize:"4rem",marginBottom:"1rem"},children:"\uD83D\uDCC1"}),(0,i.jsx)("h3",{style:{fontSize:"1.125rem",fontWeight:"500",margin:"0 0 0.5rem 0"},children:"لا توجد ملفات"}),(0,i.jsx)("p",{style:{margin:0},children:"ابدأ برفع الصور والفيديوهات"})]}):(0,i.jsx)("div",{style:{display:"grid",gridTemplateColumns:"grid"===f?"repeat(auto-fill, minmax(200px, 1fr))":"1fr",gap:"1rem"},className:"responsive grid-layout",children:D.map(e=>{let t=e.fileType.startsWith("image"),r=e.fileType.startsWith("video");return(0,i.jsx)("div",{style:{border:"1px solid #e5e7eb",borderRadius:"0.5rem",overflow:"hidden",background:"white",transition:"all 0.2s",cursor:"pointer"},onMouseEnter:e=>{e.currentTarget.style.boxShadow="0 4px 6px -1px rgba(0, 0, 0, 0.1)"},onMouseLeave:e=>{e.currentTarget.style.boxShadow="none"},children:"grid"===f?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("div",{style:{aspectRatio:"1",background:"#f3f4f6",display:"flex",alignItems:"center",justifyContent:"center",position:"relative"},children:[t?(0,i.jsx)("img",{src:e.publicUrl,alt:e.fileName,style:{width:"100%",height:"100%",objectFit:"cover"}}):(0,i.jsxs)("div",{style:{textAlign:"center",color:"#6b7280"},children:[r?"\uD83C\uDFA5":"\uD83D\uDCCE",(0,i.jsx)("div",{style:{fontSize:"0.75rem",marginTop:"0.5rem"},children:r?"فيديو":"ملف"})]}),(0,i.jsxs)("div",{style:{position:"absolute",top:"0.5rem",left:"0.5rem",display:"flex",gap:"0.25rem"},children:[(0,i.jsx)("button",{onClick:t=>{t.stopPropagation(),window.open(e.publicUrl,"_blank")},style:{padding:"0.25rem",background:"rgba(255, 255, 255, 0.9)",borderRadius:"0.25rem",border:"none",cursor:"pointer"},children:(0,i.jsx)(d.A,{className:"h-3 w-3"})}),(0,i.jsx)("button",{onClick:t=>{t.stopPropagation(),k(e.id)},style:{padding:"0.25rem",background:"rgba(255, 255, 255, 0.9)",borderRadius:"0.25rem",border:"none",cursor:"pointer",color:"#ef4444"},children:(0,i.jsx)(c.A,{className:"h-3 w-3"})})]})]}),(0,i.jsxs)("div",{style:{padding:"0.75rem"},children:[(0,i.jsx)("p",{style:{fontSize:"0.875rem",fontWeight:"500",margin:"0 0 0.25rem 0",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},children:e.fileName}),(0,i.jsxs)("p",{style:{fontSize:"0.75rem",color:"#6b7280",margin:0},children:[C(e.fileSize)," • ",A(e.createdAt)]})]})]}):(0,i.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"1rem",padding:"1rem"},children:[(0,i.jsx)("div",{style:{width:"3rem",height:"3rem",background:"#f3f4f6",borderRadius:"0.375rem",display:"flex",alignItems:"center",justifyContent:"center",flexShrink:0},children:t?(0,i.jsx)("img",{src:e.publicUrl,alt:e.fileName,style:{width:"100%",height:"100%",objectFit:"cover",borderRadius:"0.375rem"}}):(0,i.jsx)("span",{style:{fontSize:"1.5rem"},children:r?"\uD83C\uDFA5":"\uD83D\uDCCE"})}),(0,i.jsxs)("div",{style:{flex:1},children:[(0,i.jsx)("p",{style:{fontSize:"0.875rem",fontWeight:"500",margin:"0 0 0.25rem 0"},children:e.fileName}),(0,i.jsxs)("p",{style:{fontSize:"0.75rem",color:"#6b7280",margin:0},children:[C(e.fileSize)," • ",A(e.createdAt)]})]}),(0,i.jsxs)("div",{style:{display:"flex",gap:"0.5rem"},children:[(0,i.jsx)("button",{onClick:()=>window.open(e.publicUrl,"_blank"),style:{padding:"0.5rem",background:"#f3f4f6",borderRadius:"0.375rem",border:"none",cursor:"pointer"},children:(0,i.jsx)(d.A,{className:"h-4 w-4"})}),(0,i.jsx)("button",{onClick:()=>k(e.id),style:{padding:"0.5rem",background:"#fef2f2",borderRadius:"0.375rem",border:"none",cursor:"pointer",color:"#ef4444"},children:(0,i.jsx)(c.A,{className:"h-4 w-4"})})]})]})},e.id)})})})]}),b&&(0,i.jsx)("div",{style:{position:"fixed",inset:0,background:"rgba(0, 0, 0, 0.5)",display:"flex",alignItems:"center",justifyContent:"center",zIndex:50},children:(0,i.jsxs)("div",{style:{background:"white",borderRadius:"0.75rem",padding:"2rem",maxWidth:"500px",width:"90%"},children:[(0,i.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"1.5rem"},children:[(0,i.jsx)("h2",{style:{fontSize:"1.25rem",fontWeight:"bold",margin:0},children:"رفع ملف جديد"}),(0,i.jsx)("button",{onClick:()=>v(!1),style:{padding:"0.5rem",background:"none",border:"none",cursor:"pointer",borderRadius:"0.375rem"},children:"✕"})]}),(0,i.jsx)(_,{onUploadComplete:r=>{t([r,...e]),v(!1),g.o.success("تم رفع الملف بنجاح!")}})]})})]})}},88233:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(62688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},91645:e=>{"use strict";e.exports=require("net")},92862:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>u,tree:()=>d});var i=r(65239),a=r(48088),s=r(88170),o=r.n(s),l=r(30893),n={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>l[e]);r.d(t,n);let d={children:["",{children:["media",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,58703)),"C:\\Users\\<USER>\\Desktop\\Ewasl\\eWasl.com_Cursor_Digital_Ocean\\ewasl-app\\src\\app\\media\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,45399)),"C:\\Users\\<USER>\\Desktop\\Ewasl\\eWasl.com_Cursor_Digital_Ocean\\ewasl-app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\Ewasl\\eWasl.com_Cursor_Digital_Ocean\\ewasl-app\\src\\app\\media\\page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},u=new i.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/media/page",pathname:"/media",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},94735:e=>{"use strict";e.exports=require("events")},96474:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},99270:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[4243,6167,2215,1658,9038,9908],()=>r(92862));module.exports=i})();