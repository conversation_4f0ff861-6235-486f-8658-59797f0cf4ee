{"version": 3, "file": "index.umd.js", "sources": ["../src/code-block.ts"], "sourcesContent": ["import { mergeAttributes, Node, textblockTypeInputRule } from '@tiptap/core'\nimport {\n  Plugin,\n  Plugin<PERSON>ey,\n  Selection,\n  TextSelection,\n} from '@tiptap/pm/state'\n\nexport interface CodeBlockOptions {\n  /**\n   * Adds a prefix to language classes that are applied to code tags.\n   * @default 'language-'\n   */\n  languageClassPrefix: string\n  /**\n   * Define whether the node should be exited on triple enter.\n   * @default true\n   */\n  exitOnTripleEnter: boolean\n  /**\n   * Define whether the node should be exited on arrow down if there is no node after it.\n   * @default true\n   */\n  exitOnArrowDown: boolean\n  /**\n   * The default language.\n   * @default null\n   * @example 'js'\n   */\n  defaultLanguage: string | null | undefined\n  /**\n   * Custom HTML attributes that should be added to the rendered HTML tag.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    codeBlock: {\n      /**\n       * Set a code block\n       * @param attributes Code block attributes\n       * @example editor.commands.setCodeBlock({ language: 'javascript' })\n       */\n      setCodeBlock: (attributes?: { language: string }) => ReturnType\n      /**\n       * Toggle a code block\n       * @param attributes Code block attributes\n       * @example editor.commands.toggleCodeBlock({ language: 'javascript' })\n       */\n      toggleCodeBlock: (attributes?: { language: string }) => ReturnType\n    }\n  }\n}\n\n/**\n * Matches a code block with backticks.\n */\nexport const backtickInputRegex = /^```([a-z]+)?[\\s\\n]$/\n\n/**\n * Matches a code block with tildes.\n */\nexport const tildeInputRegex = /^~~~([a-z]+)?[\\s\\n]$/\n\n/**\n * This extension allows you to create code blocks.\n * @see https://tiptap.dev/api/nodes/code-block\n */\nexport const CodeBlock = Node.create<CodeBlockOptions>({\n  name: 'codeBlock',\n\n  addOptions() {\n    return {\n      languageClassPrefix: 'language-',\n      exitOnTripleEnter: true,\n      exitOnArrowDown: true,\n      defaultLanguage: null,\n      HTMLAttributes: {},\n    }\n  },\n\n  content: 'text*',\n\n  marks: '',\n\n  group: 'block',\n\n  code: true,\n\n  defining: true,\n\n  addAttributes() {\n    return {\n      language: {\n        default: this.options.defaultLanguage,\n        parseHTML: element => {\n          const { languageClassPrefix } = this.options\n          const classNames = [...(element.firstElementChild?.classList || [])]\n          const languages = classNames\n            .filter(className => className.startsWith(languageClassPrefix))\n            .map(className => className.replace(languageClassPrefix, ''))\n          const language = languages[0]\n\n          if (!language) {\n            return null\n          }\n\n          return language\n        },\n        rendered: false,\n      },\n    }\n  },\n\n  parseHTML() {\n    return [\n      {\n        tag: 'pre',\n        preserveWhitespace: 'full',\n      },\n    ]\n  },\n\n  renderHTML({ node, HTMLAttributes }) {\n    return [\n      'pre',\n      mergeAttributes(this.options.HTMLAttributes, HTMLAttributes),\n      [\n        'code',\n        {\n          class: node.attrs.language\n            ? this.options.languageClassPrefix + node.attrs.language\n            : null,\n        },\n        0,\n      ],\n    ]\n  },\n\n  addCommands() {\n    return {\n      setCodeBlock:\n        attributes => ({ commands }) => {\n          return commands.setNode(this.name, attributes)\n        },\n      toggleCodeBlock:\n        attributes => ({ commands }) => {\n          return commands.toggleNode(this.name, 'paragraph', attributes)\n        },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      'Mod-Alt-c': () => this.editor.commands.toggleCodeBlock(),\n\n      // remove code block when at start of document or code block is empty\n      Backspace: () => {\n        const { empty, $anchor } = this.editor.state.selection\n        const isAtStart = $anchor.pos === 1\n\n        if (!empty || $anchor.parent.type.name !== this.name) {\n          return false\n        }\n\n        if (isAtStart || !$anchor.parent.textContent.length) {\n          return this.editor.commands.clearNodes()\n        }\n\n        return false\n      },\n\n      // exit node on triple enter\n      Enter: ({ editor }) => {\n        if (!this.options.exitOnTripleEnter) {\n          return false\n        }\n\n        const { state } = editor\n        const { selection } = state\n        const { $from, empty } = selection\n\n        if (!empty || $from.parent.type !== this.type) {\n          return false\n        }\n\n        const isAtEnd = $from.parentOffset === $from.parent.nodeSize - 2\n        const endsWithDoubleNewline = $from.parent.textContent.endsWith('\\n\\n')\n\n        if (!isAtEnd || !endsWithDoubleNewline) {\n          return false\n        }\n\n        return editor\n          .chain()\n          .command(({ tr }) => {\n            tr.delete($from.pos - 2, $from.pos)\n\n            return true\n          })\n          .exitCode()\n          .run()\n      },\n\n      // exit node on arrow down\n      ArrowDown: ({ editor }) => {\n        if (!this.options.exitOnArrowDown) {\n          return false\n        }\n\n        const { state } = editor\n        const { selection, doc } = state\n        const { $from, empty } = selection\n\n        if (!empty || $from.parent.type !== this.type) {\n          return false\n        }\n\n        const isAtEnd = $from.parentOffset === $from.parent.nodeSize - 2\n\n        if (!isAtEnd) {\n          return false\n        }\n\n        const after = $from.after()\n\n        if (after === undefined) {\n          return false\n        }\n\n        const nodeAfter = doc.nodeAt(after)\n\n        if (nodeAfter) {\n          return editor.commands.command(({ tr }) => {\n            tr.setSelection(Selection.near(doc.resolve(after)))\n            return true\n          })\n        }\n\n        return editor.commands.exitCode()\n      },\n    }\n  },\n\n  addInputRules() {\n    return [\n      textblockTypeInputRule({\n        find: backtickInputRegex,\n        type: this.type,\n        getAttributes: match => ({\n          language: match[1],\n        }),\n      }),\n      textblockTypeInputRule({\n        find: tildeInputRegex,\n        type: this.type,\n        getAttributes: match => ({\n          language: match[1],\n        }),\n      }),\n    ]\n  },\n\n  addProseMirrorPlugins() {\n    return [\n      // this plugin creates a code block for pasted content from VS Code\n      // we can also detect the copied code language\n      new Plugin({\n        key: new PluginKey('codeBlockVSCodeHandler'),\n        props: {\n          handlePaste: (view, event) => {\n            if (!event.clipboardData) {\n              return false\n            }\n\n            // don’t create a new code block within code blocks\n            if (this.editor.isActive(this.type.name)) {\n              return false\n            }\n\n            const text = event.clipboardData.getData('text/plain')\n            const vscode = event.clipboardData.getData('vscode-editor-data')\n            const vscodeData = vscode ? JSON.parse(vscode) : undefined\n            const language = vscodeData?.mode\n\n            if (!text || !language) {\n              return false\n            }\n\n            const { tr, schema } = view.state\n\n            // prepare a text node\n            // strip carriage return chars from text pasted as code\n            // see: https://github.com/ProseMirror/prosemirror-view/commit/a50a6bcceb4ce52ac8fcc6162488d8875613aacd\n            const textNode = schema.text(text.replace(/\\r\\n?/g, '\\n'))\n\n            // create a code block with the text node\n            // replace selection with the code block\n            tr.replaceSelectionWith(this.type.create({ language }, textNode))\n\n            if (tr.selection.$from.parent.type !== this.type) {\n              // put cursor inside the newly created code block\n              tr.setSelection(TextSelection.near(tr.doc.resolve(Math.max(0, tr.selection.from - 2))))\n            }\n\n            // store meta information\n            // this is useful for other plugins that depends on the paste event\n            // like the paste rule plugin\n            tr.setMeta('paste', true)\n\n            view.dispatch(tr)\n\n            return true\n          },\n        },\n      }),\n    ]\n  },\n})\n"], "names": ["Node", "mergeAttributes", "state", "Selection", "textblockTypeInputRule", "Plugin", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TextSelection"], "mappings": ";;;;;;EAyDA;;EAEG;AACI,QAAM,kBAAkB,GAAG;EAElC;;EAEG;AACI,QAAM,eAAe,GAAG;EAE/B;;;EAGG;AACU,QAAA,SAAS,GAAGA,SAAI,CAAC,MAAM,CAAmB;EACrD,IAAA,IAAI,EAAE,WAAW;MAEjB,UAAU,GAAA;UACR,OAAO;EACL,YAAA,mBAAmB,EAAE,WAAW;EAChC,YAAA,iBAAiB,EAAE,IAAI;EACvB,YAAA,eAAe,EAAE,IAAI;EACrB,YAAA,eAAe,EAAE,IAAI;EACrB,YAAA,cAAc,EAAE,EAAE;WACnB;OACF;EAED,IAAA,OAAO,EAAE,OAAO;EAEhB,IAAA,KAAK,EAAE,EAAE;EAET,IAAA,KAAK,EAAE,OAAO;EAEd,IAAA,IAAI,EAAE,IAAI;EAEV,IAAA,QAAQ,EAAE,IAAI;MAEd,aAAa,GAAA;UACX,OAAO;EACL,YAAA,QAAQ,EAAE;EACR,gBAAA,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,eAAe;kBACrC,SAAS,EAAE,OAAO,IAAG;;EACnB,oBAAA,MAAM,EAAE,mBAAmB,EAAE,GAAG,IAAI,CAAC,OAAO;EAC5C,oBAAA,MAAM,UAAU,GAAG,CAAC,IAAI,CAAA,CAAA,EAAA,GAAA,OAAO,CAAC,iBAAiB,0CAAE,SAAS,KAAI,EAAE,CAAC,CAAC;sBACpE,MAAM,SAAS,GAAG;2BACf,MAAM,CAAC,SAAS,IAAI,SAAS,CAAC,UAAU,CAAC,mBAAmB,CAAC;EAC7D,yBAAA,GAAG,CAAC,SAAS,IAAI,SAAS,CAAC,OAAO,CAAC,mBAAmB,EAAE,EAAE,CAAC,CAAC;EAC/D,oBAAA,MAAM,QAAQ,GAAG,SAAS,CAAC,CAAC,CAAC;sBAE7B,IAAI,CAAC,QAAQ,EAAE;EACb,wBAAA,OAAO,IAAI;;EAGb,oBAAA,OAAO,QAAQ;mBAChB;EACD,gBAAA,QAAQ,EAAE,KAAK;EAChB,aAAA;WACF;OACF;MAED,SAAS,GAAA;UACP,OAAO;EACL,YAAA;EACE,gBAAA,GAAG,EAAE,KAAK;EACV,gBAAA,kBAAkB,EAAE,MAAM;EAC3B,aAAA;WACF;OACF;EAED,IAAA,UAAU,CAAC,EAAE,IAAI,EAAE,cAAc,EAAE,EAAA;UACjC,OAAO;cACL,KAAK;cACLC,oBAAe,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,cAAc,CAAC;EAC5D,YAAA;kBACE,MAAM;EACN,gBAAA;EACE,oBAAA,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC;4BACd,IAAI,CAAC,OAAO,CAAC,mBAAmB,GAAG,IAAI,CAAC,KAAK,CAAC;EAChD,0BAAE,IAAI;EACT,iBAAA;kBACD,CAAC;EACF,aAAA;WACF;OACF;MAED,WAAW,GAAA;UACT,OAAO;cACL,YAAY,EACV,UAAU,IAAI,CAAC,EAAE,QAAQ,EAAE,KAAI;kBAC7B,OAAO,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,UAAU,CAAC;eAC/C;cACH,eAAe,EACb,UAAU,IAAI,CAAC,EAAE,QAAQ,EAAE,KAAI;EAC7B,gBAAA,OAAO,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,WAAW,EAAE,UAAU,CAAC;eAC/D;WACJ;OACF;MAED,oBAAoB,GAAA;UAClB,OAAO;cACL,WAAW,EAAE,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,eAAe,EAAE;;cAGzD,SAAS,EAAE,MAAK;EACd,gBAAA,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS;EACtD,gBAAA,MAAM,SAAS,GAAG,OAAO,CAAC,GAAG,KAAK,CAAC;EAEnC,gBAAA,IAAI,CAAC,KAAK,IAAI,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,EAAE;EACpD,oBAAA,OAAO,KAAK;;kBAGd,IAAI,SAAS,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,EAAE;sBACnD,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,EAAE;;EAG1C,gBAAA,OAAO,KAAK;eACb;;EAGD,YAAA,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,KAAI;EACpB,gBAAA,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE;EACnC,oBAAA,OAAO,KAAK;;EAGd,gBAAA,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM;EACxB,gBAAA,MAAM,EAAE,SAAS,EAAE,GAAG,KAAK;EAC3B,gBAAA,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,SAAS;EAElC,gBAAA,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,MAAM,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,EAAE;EAC7C,oBAAA,OAAO,KAAK;;EAGd,gBAAA,MAAM,OAAO,GAAG,KAAK,CAAC,YAAY,KAAK,KAAK,CAAC,MAAM,CAAC,QAAQ,GAAG,CAAC;EAChE,gBAAA,MAAM,qBAAqB,GAAG,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,MAAM,CAAC;EAEvE,gBAAA,IAAI,CAAC,OAAO,IAAI,CAAC,qBAAqB,EAAE;EACtC,oBAAA,OAAO,KAAK;;EAGd,gBAAA,OAAO;EACJ,qBAAA,KAAK;EACL,qBAAA,OAAO,CAAC,CAAC,EAAE,EAAE,EAAE,KAAI;EAClB,oBAAA,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,EAAE,KAAK,CAAC,GAAG,CAAC;EAEnC,oBAAA,OAAO,IAAI;EACb,iBAAC;EACA,qBAAA,QAAQ;EACR,qBAAA,GAAG,EAAE;eACT;;EAGD,YAAA,SAAS,EAAE,CAAC,EAAE,MAAM,EAAE,KAAI;EACxB,gBAAA,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE;EACjC,oBAAA,OAAO,KAAK;;EAGd,gBAAA,MAAM,SAAEC,OAAK,EAAE,GAAG,MAAM;EACxB,gBAAA,MAAM,EAAE,SAAS,EAAE,GAAG,EAAE,GAAGA,OAAK;EAChC,gBAAA,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,SAAS;EAElC,gBAAA,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,MAAM,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,EAAE;EAC7C,oBAAA,OAAO,KAAK;;EAGd,gBAAA,MAAM,OAAO,GAAG,KAAK,CAAC,YAAY,KAAK,KAAK,CAAC,MAAM,CAAC,QAAQ,GAAG,CAAC;kBAEhE,IAAI,CAAC,OAAO,EAAE;EACZ,oBAAA,OAAO,KAAK;;EAGd,gBAAA,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE;EAE3B,gBAAA,IAAI,KAAK,KAAK,SAAS,EAAE;EACvB,oBAAA,OAAO,KAAK;;kBAGd,MAAM,SAAS,GAAG,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC;kBAEnC,IAAI,SAAS,EAAE;sBACb,OAAO,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,EAAE,KAAI;EACxC,wBAAA,EAAE,CAAC,YAAY,CAACC,eAAS,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;EACnD,wBAAA,OAAO,IAAI;EACb,qBAAC,CAAC;;EAGJ,gBAAA,OAAO,MAAM,CAAC,QAAQ,CAAC,QAAQ,EAAE;eAClC;WACF;OACF;MAED,aAAa,GAAA;UACX,OAAO;EACL,YAAAC,2BAAsB,CAAC;EACrB,gBAAA,IAAI,EAAE,kBAAkB;kBACxB,IAAI,EAAE,IAAI,CAAC,IAAI;EACf,gBAAA,aAAa,EAAE,KAAK,KAAK;EACvB,oBAAA,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC;mBACnB,CAAC;eACH,CAAC;EACF,YAAAA,2BAAsB,CAAC;EACrB,gBAAA,IAAI,EAAE,eAAe;kBACrB,IAAI,EAAE,IAAI,CAAC,IAAI;EACf,gBAAA,aAAa,EAAE,KAAK,KAAK;EACvB,oBAAA,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC;mBACnB,CAAC;eACH,CAAC;WACH;OACF;MAED,qBAAqB,GAAA;UACnB,OAAO;;;EAGL,YAAA,IAAIC,YAAM,CAAC;EACT,gBAAA,GAAG,EAAE,IAAIC,eAAS,CAAC,wBAAwB,CAAC;EAC5C,gBAAA,KAAK,EAAE;EACL,oBAAA,WAAW,EAAE,CAAC,IAAI,EAAE,KAAK,KAAI;EAC3B,wBAAA,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE;EACxB,4BAAA,OAAO,KAAK;;;EAId,wBAAA,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;EACxC,4BAAA,OAAO,KAAK;;0BAGd,MAAM,IAAI,GAAG,KAAK,CAAC,aAAa,CAAC,OAAO,CAAC,YAAY,CAAC;0BACtD,MAAM,MAAM,GAAG,KAAK,CAAC,aAAa,CAAC,OAAO,CAAC,oBAAoB,CAAC;EAChE,wBAAA,MAAM,UAAU,GAAG,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,SAAS;0BAC1D,MAAM,QAAQ,GAAG,UAAU,KAAA,IAAA,IAAV,UAAU,KAAV,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,UAAU,CAAE,IAAI;EAEjC,wBAAA,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,EAAE;EACtB,4BAAA,OAAO,KAAK;;0BAGd,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,KAAK;;;;EAKjC,wBAAA,MAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;;;EAI1D,wBAAA,EAAE,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,QAAQ,EAAE,EAAE,QAAQ,CAAC,CAAC;EAEjE,wBAAA,IAAI,EAAE,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,EAAE;;EAEhD,4BAAA,EAAE,CAAC,YAAY,CAACC,mBAAa,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,SAAS,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;;;;;EAMzF,wBAAA,EAAE,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC;EAEzB,wBAAA,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;EAEjB,wBAAA,OAAO,IAAI;uBACZ;EACF,iBAAA;eACF,CAAC;WACH;OACF;EACF,CAAA;;;;;;;;;;;;;"}