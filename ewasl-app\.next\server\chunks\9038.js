exports.id=9038,exports.ids=[9038],exports.modules={39727:()=>{},44524:(e,t,r)=>{"use strict";r.d(t,{sO:()=>m});class i{static{this.providers=new Map}static registerProvider(e){this.providers.set(e.platform,e)}static getProvider(e){return this.providers.get(e)||null}static getAllProviders(){return Array.from(this.providers.values())}static getSupportedPlatforms(){return Array.from(this.providers.keys())}static isSupported(e){return this.providers.has(e)}static unregisterProvider(e){this.providers.delete(e)}static clear(){this.providers.clear()}}class s extends Error{constructor(e,t,r,i=!1){super(e),this.platform=t,this.errorCode=r,this.retryable=i,this.name="SocialProviderError"}}class a extends s{constructor(e){super("Access token has expired",e,"TOKEN_EXPIRED",!0),this.name="TokenExpiredError"}}class o extends s{constructor(e,t){super("Rate limit exceeded",e,"RATE_LIMIT",!0),this.name="RateLimitError"}}var n=r(55511);class c{generateState(){return n.randomBytes(32).toString("hex")}generateCodeVerifier(){return n.randomBytes(32).toString("base64url")}generateCodeChallenge(e){return n.createHash("sha256").update(e).digest("base64url")}async fetch(e,t={}){try{let r=await fetch(e,{...t,headers:{"User-Agent":"eWasl/1.0",...t.headers}});if(!r.ok){let e,t=await r.text();try{e=JSON.parse(t)}catch{e={message:t}}if(401===r.status)throw new a(this.platform);if(429===r.status){let e=r.headers.get("x-rate-limit-reset");throw new o(this.platform,e?new Date(1e3*parseInt(e)):void 0)}throw new s(e.message||`HTTP ${r.status}`,this.platform,e.code||r.status.toString(),r.status>=500)}return r}catch(e){if(e instanceof s)throw e;throw new s(`Network error: ${e instanceof Error?e.message:"Unknown error"}`,this.platform,"NETWORK_ERROR",!0)}}validateEnvironment(){if(!this.clientId)throw Error(`Missing client ID for ${this.platform}`);if(!this.clientSecret)throw Error(`Missing client secret for ${this.platform}`)}async disconnect(e,t){return{success:!0}}buildQueryString(e){let t=new URLSearchParams;for(let[r,i]of Object.entries(e))Array.isArray(i)?t.append(r,i.join(" ")):t.append(r,i);return t.toString()}createSuccessResult(e){return{success:!0,tokens:e}}createErrorResult(e,t){return{success:!1,error:e,errorCode:t}}createTokenRefreshSuccess(e,t,r){return{success:!0,accessToken:e,refreshToken:t,expiresAt:r}}createTokenRefreshError(e){return{success:!1,error:e}}createValidationSuccess(e,t,r){return{isValid:!0,platformUserId:e,platformUsername:t,scopes:r}}createValidationError(e,t=!1){return{isValid:!1,error:e,needsReauth:t}}}var l=r(61106);class d extends c{constructor(){super(),this.platform="facebook",this.clientId=process.env.FACEBOOK_APP_ID||"",this.clientSecret=process.env.FACEBOOK_APP_SECRET||"",this.baseApiUrl="https://graph.facebook.com/v18.0",this.authBaseUrl="https://www.facebook.com/v18.0/dialog/oauth",this.scopes=["pages_show_list","pages_manage_posts","pages_read_engagement","business_management","email","public_profile"],this.dbService=new l.G,this.validateEnvironment()}async generateAuthUrl(e,t){let r=this.generateState();await this.dbService.storeOAuthState(e,this.platform,r,t);let i=this.buildQueryString({client_id:this.clientId,redirect_uri:t,state:r,scope:this.scopes,response_type:"code",access_type:"offline"});return{authUrl:`${this.authBaseUrl}?${i}`,state:r,platform:this.platform}}async authenticate(e){try{let t=await this.dbService.verifyOAuthState(e.state);if(!(t&&t.userId===e.userId))return this.createErrorResult("Invalid OAuth state","INVALID_STATE");let r=await this.fetch(`${this.baseApiUrl}/oauth/access_token`,{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:this.buildQueryString({client_id:this.clientId,client_secret:this.clientSecret,redirect_uri:e.redirectUri,code:e.code})}),i=await r.json();if(i.error)return this.createErrorResult(i.error.message||"Token exchange failed",i.error.code);let s=await this.fetch(`${this.baseApiUrl}/oauth/access_token`,{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:this.buildQueryString({grant_type:"fb_exchange_token",client_id:this.clientId,client_secret:this.clientSecret,fb_exchange_token:i.access_token})}),a=await s.json();if(a.error)return this.createErrorResult(a.error.message||"Long-lived token exchange failed",a.error.code);let o=await this.fetch(`${this.baseApiUrl}/me?fields=id,name,email,picture.type(large)&access_token=${a.access_token}`),n=await o.json();if(n.error)return this.createErrorResult(n.error.message||"Failed to get user info",n.error.code);let c=await this.fetch(`${this.baseApiUrl}/me/accounts?fields=id,name,username,picture.type(large),access_token&access_token=${a.access_token}`),l=await c.json(),d={accessToken:a.access_token,refreshToken:a.access_token,expiresAt:new Date(Date.now()+1e3*(a.expires_in||5183944)),platformUserId:n.id,platformUsername:n.name,scopes:this.scopes,platformData:{email:n.email,picture:n.picture?.data?.url,pages:l.data||[]}};return this.createSuccessResult(d)}catch(e){return console.error("[FacebookProvider] Authentication error:",e),this.createErrorResult(e instanceof Error?e.message:"Authentication failed","AUTH_ERROR")}}async refreshToken(e,t){try{let t=await this.fetch(`${this.baseApiUrl}/oauth/access_token`,{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:this.buildQueryString({grant_type:"fb_exchange_token",client_id:this.clientId,client_secret:this.clientSecret,fb_exchange_token:e})}),r=await t.json();if(r.error)return this.createTokenRefreshError(r.error.message||"Token refresh failed");return this.createTokenRefreshSuccess(r.access_token,r.access_token,new Date(Date.now()+1e3*(r.expires_in||5183944)))}catch(e){return console.error("[FacebookProvider] Token refresh error:",e),this.createTokenRefreshError(e instanceof Error?e.message:"Token refresh failed")}}async validateConnection(e){try{let t=await this.fetch(`${this.baseApiUrl}/me?fields=id,name&access_token=${e}`),r=await t.json();if(r.error){let e=190===r.error.code;return this.createValidationError(r.error.message||"Token validation failed",e)}return this.createValidationSuccess(r.id,r.name,this.scopes)}catch(e){return console.error("[FacebookProvider] Validation error:",e),this.createValidationError(e instanceof Error?e.message:"Validation failed",!0)}}async post(e,t){try{let r={};t.text&&(r.message=t.text),t.link&&(r.link=t.link,t.linkTitle&&(r.name=t.linkTitle),t.linkDescription&&(r.description=t.linkDescription)),t.images&&t.images.length>0&&console.warn("[FacebookProvider] Image posting not fully implemented");let i=await this.fetch(`${this.baseApiUrl}/me/feed`,{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:this.buildQueryString({...r,access_token:e})}),s=await i.json();if(s.error)return{success:!1,error:s.error.message||"Post failed",errorCode:s.error.code?.toString(),retryable:200!==s.error.code};return{success:!0,platformPostId:s.id,postUrl:`https://facebook.com/${s.id}`}}catch(e){return console.error("[FacebookProvider] Post error:",e),{success:!1,error:e instanceof Error?e.message:"Post failed",errorCode:"POST_ERROR",retryable:!0}}}async getUserInfo(e){try{let t=await this.fetch(`${this.baseApiUrl}/me?fields=id,name,email,picture.type(large)&access_token=${e}`);return await t.json()}catch(e){throw console.error("[FacebookProvider] Get user info error:",e),e}}getPostLimits(){return{maxTextLength:63206,maxImages:10,maxVideos:1}}async healthCheck(e){return this.validateConnection(e)}async getPages(e){try{let t=await this.fetch(`${this.baseApiUrl}/me/accounts?fields=id,name,username,picture.type(large),access_token&access_token=${e}`),r=await t.json();if(r.error)return console.error("[FacebookProvider] Get pages error:",r.error),[];return r.data.map(e=>({id:e.id,name:e.name,username:e.username,picture:e.picture?.data?.url,accessToken:e.access_token}))}catch(e){return console.error("[FacebookProvider] Get pages error:",e),[]}}async postToPage(e,t){return this.post(e,t)}}class h extends c{constructor(){super(),this.platform="twitter",this.clientId=process.env.TWITTER_CLIENT_ID||"",this.clientSecret=process.env.TWITTER_CLIENT_SECRET||"",this.baseApiUrl="https://api.twitter.com/2",this.authBaseUrl="https://twitter.com/i/oauth2/authorize",this.scopes=["tweet.read","tweet.write","users.read","media.upload","offline.access"],this.dbService=new l.G,this.validateEnvironment()}async generateAuthUrl(e,t){let r=this.generateState(),i=this.generateCodeVerifier(),s=this.generateCodeChallenge(i);await this.dbService.storeOAuthState(e,this.platform,r,t);let a=this.buildQueryString({response_type:"code",client_id:this.clientId,redirect_uri:t,scope:this.scopes,state:r,code_challenge:s,code_challenge_method:"S256"});return{authUrl:`${this.authBaseUrl}?${a}`,state:r,codeVerifier:i,platform:this.platform}}async authenticate(e){try{let t=await this.dbService.verifyOAuthState(e.state);if(!(t&&t.userId===e.userId))return this.createErrorResult("Invalid OAuth state","INVALID_STATE");if(!e.codeVerifier)return this.createErrorResult("Missing code verifier","MISSING_CODE_VERIFIER");let r=await this.fetch("https://api.twitter.com/2/oauth2/token",{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded",Authorization:`Basic ${Buffer.from(`${this.clientId}:${this.clientSecret}`).toString("base64")}`},body:this.buildQueryString({grant_type:"authorization_code",code:e.code,redirect_uri:e.redirectUri,code_verifier:e.codeVerifier})}),i=await r.json();if(i.error)return this.createErrorResult(i.error_description||"Token exchange failed",i.error);let s=await this.fetch(`${this.baseApiUrl}/users/me?user.fields=id,name,username,profile_image_url,public_metrics`,{headers:{Authorization:`Bearer ${i.access_token}`}}),a=await s.json();if(a.errors)return this.createErrorResult(a.errors[0]?.detail||"Failed to get user info",a.errors[0]?.type);let o=a.data,n={accessToken:i.access_token,refreshToken:i.refresh_token,expiresAt:new Date(Date.now()+1e3*i.expires_in),platformUserId:o.id,platformUsername:o.username,scopes:i.scope?.split(" ")||this.scopes,platformData:{name:o.name,profileImageUrl:o.profile_image_url,publicMetrics:o.public_metrics}};return this.createSuccessResult(n)}catch(e){return console.error("[TwitterProvider] Authentication error:",e),this.createErrorResult(e instanceof Error?e.message:"Authentication failed","AUTH_ERROR")}}async refreshToken(e,t){try{let t=await this.fetch("https://api.twitter.com/2/oauth2/token",{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded",Authorization:`Basic ${Buffer.from(`${this.clientId}:${this.clientSecret}`).toString("base64")}`},body:this.buildQueryString({grant_type:"refresh_token",refresh_token:e})}),r=await t.json();if(r.error)return this.createTokenRefreshError(r.error_description||"Token refresh failed");return this.createTokenRefreshSuccess(r.access_token,r.refresh_token,new Date(Date.now()+1e3*r.expires_in))}catch(e){return console.error("[TwitterProvider] Token refresh error:",e),this.createTokenRefreshError(e instanceof Error?e.message:"Token refresh failed")}}async validateConnection(e){try{let t=await this.fetch(`${this.baseApiUrl}/users/me?user.fields=id,username`,{headers:{Authorization:`Bearer ${e}`}}),r=await t.json();if(r.errors){let e=r.errors[0],t=e?.type==="https://api.twitter.com/2/problems/not-authorized-for-resource";return this.createValidationError(e?.detail||"Token validation failed",t)}let i=r.data;return this.createValidationSuccess(i.id,i.username,this.scopes)}catch(e){return console.error("[TwitterProvider] Validation error:",e),this.createValidationError(e instanceof Error?e.message:"Validation failed",!0)}}async post(e,t){try{let r={};if(t.text&&(r.text=t.text,t.hashtags&&t.hashtags.length>0)){let e=t.hashtags.map(e=>e.startsWith("#")?e:`#${e}`).join(" ");r.text+=` ${e}`}if(t.images&&t.images.length>0){let i=await this.uploadMedia(e,t.images);i.length>0&&(r.media={media_ids:i})}let i=await this.fetch(`${this.baseApiUrl}/tweets`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${e}`},body:JSON.stringify(r)}),s=await i.json();if(s.errors){let e=s.errors[0];return{success:!1,error:e.detail||"Tweet failed",errorCode:e.type,retryable:!e.type?.includes("authorization")}}let a=s.data.id;return{success:!0,platformPostId:a,postUrl:`https://twitter.com/i/web/status/${a}`}}catch(e){return console.error("[TwitterProvider] Post error:",e),{success:!1,error:e instanceof Error?e.message:"Tweet failed",errorCode:"POST_ERROR",retryable:!0}}}async getUserInfo(e){try{let t=await this.fetch(`${this.baseApiUrl}/users/me?user.fields=id,name,username,profile_image_url,public_metrics,description`,{headers:{Authorization:`Bearer ${e}`}});return(await t.json()).data}catch(e){throw console.error("[TwitterProvider] Get user info error:",e),e}}getPostLimits(){return{maxTextLength:280,maxImages:4,maxVideos:1}}async healthCheck(e){return this.validateConnection(e)}async uploadMedia(e,t){try{for(let e of t.slice(0,4))try{console.warn("[TwitterProvider] Media upload not fully implemented - would upload:",e)}catch(e){console.error("[TwitterProvider] Media upload error:",e)}return[]}catch(e){return console.error("[TwitterProvider] Upload media error:",e),[]}}async createThread(e,t){let r,i=[];for(let s of t)try{let t={text:s};r&&(t.reply={in_reply_to_tweet_id:r});let a=await this.fetch(`${this.baseApiUrl}/tweets`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${e}`},body:JSON.stringify(t)}),o=await a.json();if(o.errors){i.push({success:!1,error:o.errors[0]?.detail||"Tweet failed",errorCode:o.errors[0]?.type,retryable:!0});break}let n=o.data.id;r=n,i.push({success:!0,platformPostId:n,postUrl:`https://twitter.com/i/web/status/${n}`})}catch(e){console.error("[TwitterProvider] Thread tweet error:",e),i.push({success:!1,error:e instanceof Error?e.message:"Tweet failed",errorCode:"THREAD_ERROR",retryable:!0});break}return i}}class u extends c{constructor(){super(),this.platform="linkedin",this.clientId=process.env.LINKEDIN_CLIENT_ID||"",this.clientSecret=process.env.LINKEDIN_CLIENT_SECRET||"",this.baseApiUrl="https://api.linkedin.com/v2",this.authBaseUrl="https://www.linkedin.com/oauth/v2/authorization",this.scopes=["r_liteprofile","r_emailaddress","w_member_social","r_organization_social","w_organization_social"],this.dbService=new l.G,this.validateEnvironment()}async generateAuthUrl(e,t){let r=this.generateState();await this.dbService.storeOAuthState(e,this.platform,r,t);let i=this.buildQueryString({response_type:"code",client_id:this.clientId,redirect_uri:t,state:r,scope:this.scopes});return{authUrl:`${this.authBaseUrl}?${i}`,state:r,platform:this.platform}}async authenticate(e){try{let t=await this.dbService.verifyOAuthState(e.state);if(!(t&&t.userId===e.userId))return this.createErrorResult("Invalid OAuth state","INVALID_STATE");let r=await this.fetch("https://www.linkedin.com/oauth/v2/accessToken",{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:this.buildQueryString({grant_type:"authorization_code",code:e.code,redirect_uri:e.redirectUri,client_id:this.clientId,client_secret:this.clientSecret})}),i=await r.json();if(i.error)return this.createErrorResult(i.error_description||"Token exchange failed",i.error);let s=await this.fetch(`${this.baseApiUrl}/people/~:(id,firstName,lastName,profilePicture(displayImage~:mediumElements))`,{headers:{Authorization:`Bearer ${i.access_token}`}}),a=await s.json();if(a.status>=400)return this.createErrorResult(a.message||"Failed to get user profile","PROFILE_ERROR");let o=await this.fetch(`${this.baseApiUrl}/emailAddress?q=members&projection=(elements*(handle~))`,{headers:{Authorization:`Bearer ${i.access_token}`}}),n=await o.json(),c=n.elements?.[0]?.["handle~"]?.emailAddress,l=await this.fetch(`${this.baseApiUrl}/organizationAcls?q=roleAssignee&role=ADMINISTRATOR&projection=(elements*(*,organization~(id,localizedName,logoV2(original~:playableStreams))))`,{headers:{Authorization:`Bearer ${i.access_token}`}}),d=await l.json(),h={accessToken:i.access_token,refreshToken:i.refresh_token,expiresAt:new Date(Date.now()+1e3*i.expires_in),platformUserId:a.id,platformUsername:`${a.firstName?.localized?.en_US||""} ${a.lastName?.localized?.en_US||""}`.trim(),scopes:i.scope?.split(" ")||this.scopes,platformData:{email:c,firstName:a.firstName?.localized?.en_US,lastName:a.lastName?.localized?.en_US,profilePicture:a.profilePicture?.displayImage?.elements?.[0]?.identifiers?.[0]?.identifier,organizations:d.elements?.map(e=>({id:e.organization?.id,name:e.organization?.localizedName,logo:e.organization?.logoV2?.original?.elements?.[0]?.identifiers?.[0]?.identifier}))||[]}};return this.createSuccessResult(h)}catch(e){return console.error("[LinkedInProvider] Authentication error:",e),this.createErrorResult(e instanceof Error?e.message:"Authentication failed","AUTH_ERROR")}}async refreshToken(e,t){try{let t=await this.fetch("https://www.linkedin.com/oauth/v2/accessToken",{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:this.buildQueryString({grant_type:"refresh_token",refresh_token:e,client_id:this.clientId,client_secret:this.clientSecret})}),r=await t.json();if(r.error)return this.createTokenRefreshError(r.error_description||"Token refresh failed");return this.createTokenRefreshSuccess(r.access_token,r.refresh_token,new Date(Date.now()+1e3*r.expires_in))}catch(e){return console.error("[LinkedInProvider] Token refresh error:",e),this.createTokenRefreshError(e instanceof Error?e.message:"Token refresh failed")}}async validateConnection(e){try{let t=await this.fetch(`${this.baseApiUrl}/people/~:(id,firstName,lastName)`,{headers:{Authorization:`Bearer ${e}`}}),r=await t.json();if(r.status>=400){let e=401===r.status;return this.createValidationError(r.message||"Token validation failed",e)}let i=`${r.firstName?.localized?.en_US||""} ${r.lastName?.localized?.en_US||""}`.trim();return this.createValidationSuccess(r.id,i,this.scopes)}catch(e){return console.error("[LinkedInProvider] Validation error:",e),this.createValidationError(e instanceof Error?e.message:"Validation failed",!0)}}async post(e,t){try{let r=await this.fetch(`${this.baseApiUrl}/people/~:(id)`,{headers:{Authorization:`Bearer ${e}`}}),i=await r.json(),s={author:`urn:li:person:${i.id}`,lifecycleState:"PUBLISHED",specificContent:{"com.linkedin.ugc.ShareContent":{shareCommentary:{text:t.text||""},shareMediaCategory:"NONE"}},visibility:{"com.linkedin.ugc.MemberNetworkVisibility":"PUBLIC"}};t.link&&(s.specificContent["com.linkedin.ugc.ShareContent"].shareMediaCategory="ARTICLE",s.specificContent["com.linkedin.ugc.ShareContent"].media=[{status:"READY",description:{text:t.linkDescription||""},originalUrl:t.link,title:{text:t.linkTitle||""}}]),t.images&&t.images.length>0&&(s.specificContent["com.linkedin.ugc.ShareContent"].shareMediaCategory="IMAGE",console.warn("[LinkedInProvider] Image posting not fully implemented"));let a=await this.fetch(`${this.baseApiUrl}/ugcPosts`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${e}`},body:JSON.stringify(s)}),o=await a.json();if(o.status>=400)return{success:!1,error:o.message||"Post failed",errorCode:o.status?.toString(),retryable:o.status>=500};let n=o.id;return{success:!0,platformPostId:n,postUrl:`https://www.linkedin.com/feed/update/${n}`}}catch(e){return console.error("[LinkedInProvider] Post error:",e),{success:!1,error:e instanceof Error?e.message:"Post failed",errorCode:"POST_ERROR",retryable:!0}}}async getUserInfo(e){try{let t=await this.fetch(`${this.baseApiUrl}/people/~:(id,firstName,lastName,profilePicture(displayImage~:mediumElements))`,{headers:{Authorization:`Bearer ${e}`}});return await t.json()}catch(e){throw console.error("[LinkedInProvider] Get user info error:",e),e}}getPostLimits(){return{maxTextLength:3e3,maxImages:9,maxVideos:1}}async healthCheck(e){return this.validateConnection(e)}async getCompanyPages(e){try{let t=await this.fetch(`${this.baseApiUrl}/organizationAcls?q=roleAssignee&role=ADMINISTRATOR&projection=(elements*(*,organization~(id,localizedName,vanityName,logoV2(original~:playableStreams))))`,{headers:{Authorization:`Bearer ${e}`}}),r=await t.json();if(r.status>=400)return console.error("[LinkedInProvider] Get company pages error:",r),[];return r.elements?.map(e=>({id:e.organization?.id,name:e.organization?.localizedName,vanityName:e.organization?.vanityName,logoUrl:e.organization?.logoV2?.original?.elements?.[0]?.identifiers?.[0]?.identifier}))||[]}catch(e){return console.error("[LinkedInProvider] Get company pages error:",e),[]}}async postToCompany(e,t,r){try{let i={author:`urn:li:organization:${t}`,lifecycleState:"PUBLISHED",specificContent:{"com.linkedin.ugc.ShareContent":{shareCommentary:{text:r.text||""},shareMediaCategory:"NONE"}},visibility:{"com.linkedin.ugc.MemberNetworkVisibility":"PUBLIC"}};r.link&&(i.specificContent["com.linkedin.ugc.ShareContent"].shareMediaCategory="ARTICLE",i.specificContent["com.linkedin.ugc.ShareContent"].media=[{status:"READY",description:{text:r.linkDescription||""},originalUrl:r.link,title:{text:r.linkTitle||""}}]);let s=await this.fetch(`${this.baseApiUrl}/ugcPosts`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${e}`},body:JSON.stringify(i)}),a=await s.json();if(a.status>=400)return{success:!1,error:a.message||"Company post failed",errorCode:a.status?.toString(),retryable:a.status>=500};let o=a.id;return{success:!0,platformPostId:o,postUrl:`https://www.linkedin.com/feed/update/${o}`}}catch(e){return console.error("[LinkedInProvider] Company post error:",e),{success:!1,error:e instanceof Error?e.message:"Company post failed",errorCode:"COMPANY_POST_ERROR",retryable:!0}}}}let p=!1;function m(e){return function(){if(!p)try{let e=new d,t=new h,r=new u;i.registerProvider(e),i.registerProvider(t),i.registerProvider(r),p=!0}catch(e){console.error("[ProvidersIndex] Error registering providers:",e)}}(),i.getProvider(e)}},47990:()=>{},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},61106:(e,t,r)=>{"use strict";r.d(t,{G:()=>n,a:()=>c});var i=r(66167);let s=process.env.SUPABASE_SERVICE_ROLE_KEY||"placeholder-key",a=process.env.OAUTH_ENCRYPTION_KEY||"default-dev-key-change-in-production",o=(0,i.createClient)("https://nnxfzhxqzmriggulsudr.supabase.co",s);class n{async storeConnection(e,t,r){await o.rpc("set_config",{parameter:"app.encryption_key",value:a,is_local:!0});let{data:i}=await o.rpc("encrypt_token",{token:r.accessToken}),{data:s}=r.refreshToken?await o.rpc("encrypt_token",{token:r.refreshToken}):{data:null},n={user_id:e,platform:t,platform_user_id:r.platformUserId,access_token_encrypted:i,refresh_token_encrypted:s,token_expires_at:r.expiresAt?.toISOString(),platform_username:r.platformUsername,platform_display_name:r.platformData?.name||r.platformUsername,platform_avatar_url:r.platformData?.avatar||r.platformData?.picture,platform_data:r.platformData||{},scopes:r.scopes,is_active:!0,health_status:"healthy",last_health_check:new Date().toISOString()},{data:c,error:l}=await o.from("oauth_connections").upsert(n,{onConflict:"user_id,platform,platform_user_id"}).select().single();if(l)throw console.error("Failed to store OAuth connection:",l),Error(`Failed to store OAuth connection: ${l.message}`);return this.mapToOAuthConnection(c)}async getUserConnections(e){let{data:t,error:r}=await o.from("oauth_connections").select("*").eq("user_id",e).eq("is_active",!0).order("created_at",{ascending:!1});if(r)throw console.error("Failed to get user connections:",r),Error(`Failed to get connections: ${r.message}`);return(t||[]).map(this.mapToOAuthConnection)}async getConnection(e,t,r){let i=o.from("oauth_connections").select("*").eq("user_id",e).eq("platform",t).eq("is_active",!0);r&&(i=i.eq("platform_user_id",r));let{data:s,error:a}=await i.single();if(a){if("PGRST116"===a.code)return null;throw console.error("Failed to get connection:",a),Error(`Failed to get connection: ${a.message}`)}return this.mapToOAuthConnection(s)}async disconnectConnection(e){let{error:t}=await o.from("oauth_connections").update({is_active:!1,health_status:"error",error_message:"Manually disconnected by user"}).eq("id",e);if(t)throw Error(`Failed to disconnect: ${t.message}`)}async storeOAuthState(e,t,r,i,s){let{error:a}=await o.from("oauth_states").insert({user_id:e,platform:t,state_token:r,code_verifier:s,redirect_uri:i,expires_at:new Date(Date.now()+6e5).toISOString()});if(a)throw Error(`Failed to store OAuth state: ${a.message}`)}async verifyOAuthState(e){let{data:t,error:r}=await o.from("oauth_states").select("*").eq("state_token",e).gte("expires_at",new Date().toISOString()).single();if(r){if("PGRST116"===r.code)return null;throw Error(`Failed to verify OAuth state: ${r.message}`)}return await o.from("oauth_states").delete().eq("state_token",e),{userId:t.user_id,platform:t.platform,redirectUri:t.redirect_uri,codeVerifier:t.code_verifier}}mapToOAuthConnection(e){return{id:e.id,userId:e.user_id,platform:e.platform,platformUserId:e.platform_user_id,accessToken:"",refreshToken:"",tokenExpiresAt:e.token_expires_at?new Date(e.token_expires_at):void 0,platformUsername:e.platform_username,platformData:e.platform_data||{},isActive:e.is_active,lastHealthCheck:e.last_health_check?new Date(e.last_health_check):void 0,healthStatus:e.health_status,errorMessage:e.error_message,createdAt:new Date(e.created_at),updatedAt:new Date(e.updated_at)}}}let c=new n}};