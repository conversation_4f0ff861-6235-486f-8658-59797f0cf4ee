(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2594],{17313:(e,s,t)=>{"use strict";t.d(s,{Xi:()=>o,av:()=>d,j7:()=>c,tU:()=>l});var r=t(95155),a=t(12115),n=t(60704),i=t(59434);let l=n.bL,c=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)(n.B8,{ref:s,className:(0,i.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",t),...a})});c.displayName=n.B8.displayName;let o=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)(n.l9,{ref:s,className:(0,i.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",t),...a})});o.displayName=n.l9.displayName;let d=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)(n.UC,{ref:s,className:(0,i.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",t),...a})});d.displayName=n.UC.displayName},26126:(e,s,t)=>{"use strict";t.d(s,{E:()=>l});var r=t(95155);t(12115);var a=t(74466),n=t(59434);let i=(0,a.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function l(e){let{className:s,variant:t,...a}=e;return(0,r.jsx)("div",{className:(0,n.cn)(i({variant:t}),s),...a})}},28154:(e,s,t)=>{Promise.resolve().then(t.bind(t,45681))},30285:(e,s,t)=>{"use strict";t.d(s,{$:()=>c,r:()=>l});var r=t(95155);t(12115);var a=t(99708),n=t(74466),i=t(59434);let l=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function c(e){let{className:s,variant:t,size:n,asChild:c=!1,...o}=e,d=c?a.DX:"button";return(0,r.jsx)(d,{"data-slot":"button",className:(0,i.cn)(l({variant:t,size:n,className:s})),...o})}},45681:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>E});var r=t(95155),a=t(12115),n=t(12108),i=t(66695),l=t(26126),c=t(30285),o=t(89613),d=t(59434);function u(e){let{delayDuration:s=0,...t}=e;return(0,r.jsx)(o.Kq,{"data-slot":"tooltip-provider",delayDuration:s,...t})}function m(e){let{...s}=e;return(0,r.jsx)(u,{children:(0,r.jsx)(o.bL,{"data-slot":"tooltip",...s})})}function x(e){let{...s}=e;return(0,r.jsx)(o.l9,{"data-slot":"tooltip-trigger",...s})}function f(e){let{className:s,sideOffset:t=0,children:a,...n}=e;return(0,r.jsx)(o.ZL,{children:(0,r.jsxs)(o.UC,{"data-slot":"tooltip-content",sideOffset:t,className:(0,d.cn)("bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance",s),...n,children:[a,(0,r.jsx)(o.i3,{className:"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]"})]})})}var h=t(54861),p=t(40646),g=t(85339),j=t(14186),v=t(53904);function b(e){let{platform:s,userId:t,businessId:n,onRefresh:o}=e,[d,b]=(0,a.useState)(null),[N,y]=(0,a.useState)(!0),[w,k]=(0,a.useState)(null),C=async()=>{y(!0),k(null);try{let e=await fetch("/api/monitoring/social-integration-status?platform=".concat(s,"&userId=").concat(t||"","&businessId=").concat(n||""));if(!e.ok)throw Error("Failed to fetch status: ".concat(e.statusText));let r=await e.json();r.tokenExpiration&&(r.tokenExpiration=new Date(r.tokenExpiration)),r.lastChecked&&(r.lastChecked=new Date(r.lastChecked)),r.rateLimitReset&&(r.rateLimitReset=new Date(r.rateLimitReset)),b(r)}catch(e){k(e instanceof Error?e.message:"Unknown error occurred"),console.error("Error fetching social integration status:",e)}finally{y(!1)}};(0,a.useEffect)(()=>{C();let e=setInterval(C,3e5);return()=>clearInterval(e)},[s,t,n]);let A=()=>{C(),null==o||o()},R=e=>{let s=new Date,t=e.getTime()-s.getTime();if(t<=0)return"expired";let r=Math.floor(t/6e4);if(r<60)return"".concat(r,"m");let a=Math.floor(r/60);if(a<24)return"".concat(a,"h");let n=Math.floor(a/24);return"".concat(n,"d")},T=e=>{switch(e.toLowerCase()){case"twitter":case"x":return"\uD835\uDD4F";case"facebook":return"f";case"instagram":return"\uD83D\uDCF7";case"linkedin":return"in";default:return"\uD83D\uDD17"}};return N&&!d?(0,r.jsxs)(i.Zp,{className:"w-full",children:[(0,r.jsx)(i.aR,{children:(0,r.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:"h-6 w-6 flex items-center justify-center rounded-full bg-muted",children:T(s)}),s.charAt(0).toUpperCase()+s.slice(1)]})}),(0,r.jsx)(i.Wu,{className:"flex justify-center py-6",children:(0,r.jsx)("div",{className:"animate-spin",children:(0,r.jsx)(v.A,{className:"h-6 w-6 text-muted-foreground"})})})]}):w?(0,r.jsxs)(i.Zp,{className:"w-full border-destructive",children:[(0,r.jsx)(i.aR,{children:(0,r.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:"h-6 w-6 flex items-center justify-center rounded-full bg-muted",children:T(s)}),s.charAt(0).toUpperCase()+s.slice(1)]})}),(0,r.jsx)(i.Wu,{children:(0,r.jsxs)("div",{className:"flex items-center gap-2 text-destructive",children:[(0,r.jsx)(g.A,{className:"h-5 w-5"}),(0,r.jsxs)("p",{children:["Error loading status: ",w]})]})}),(0,r.jsx)(i.wL,{children:(0,r.jsxs)(c.$,{variant:"outline",size:"sm",onClick:A,className:"ml-auto",children:[(0,r.jsx)(v.A,{className:"h-4 w-4 mr-1"}),"Retry"]})})]}):d?(0,r.jsxs)(i.Zp,{className:"w-full ".concat(d.connected?"":"border-destructive/50"),children:[(0,r.jsxs)(i.aR,{children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:"h-6 w-6 flex items-center justify-center rounded-full bg-muted",children:T(s)}),s.charAt(0).toUpperCase()+s.slice(1)]}),(e=>{if(!e.connected)return(0,r.jsxs)(l.E,{variant:"destructive",className:"flex items-center gap-1",children:[(0,r.jsx)(h.A,{className:"h-3 w-3"}),"Disconnected"]});switch(e.apiStatus){case"operational":return(0,r.jsxs)(l.E,{variant:"success",className:"flex items-center gap-1",children:[(0,r.jsx)(p.A,{className:"h-3 w-3"}),"Connected"]});case"degraded":return(0,r.jsxs)(l.E,{variant:"warning",className:"flex items-center gap-1",children:[(0,r.jsx)(g.A,{className:"h-3 w-3"}),"Degraded"]});case"down":return(0,r.jsxs)(l.E,{variant:"destructive",className:"flex items-center gap-1",children:[(0,r.jsx)(h.A,{className:"h-3 w-3"}),"API Down"]});default:return(0,r.jsxs)(l.E,{variant:"secondary",className:"flex items-center gap-1",children:[(0,r.jsx)(j.A,{className:"h-3 w-3"}),"Unknown"]})}})(d)]}),(0,r.jsxs)(i.BT,{children:["Last checked: ",(e=>{let s=Math.floor((new Date().getTime()-e.getTime())/6e4);if(s<1)return"just now";if(1===s)return"1 minute ago";if(s<60)return"".concat(s," minutes ago");let t=Math.floor(s/60);if(1===t)return"1 hour ago";if(t<24)return"".concat(t," hours ago");let r=Math.floor(t/24);return 1===r?"1 day ago":"".concat(r," days ago")})(d.lastChecked)]})]}),(0,r.jsxs)(i.Wu,{className:"space-y-2",children:[d.tokenExpiration&&(0,r.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,r.jsx)("span",{className:"text-muted-foreground",children:"Token expires:"}),(0,r.jsx)(u,{children:(0,r.jsxs)(m,{children:[(0,r.jsx)(x,{children:(0,r.jsx)("span",{className:"font-medium ".concat(new Date>d.tokenExpiration?"text-destructive":new Date(d.tokenExpiration.getTime()-2592e5)<new Date?"text-amber-500":"text-green-500"),children:R(d.tokenExpiration)})}),(0,r.jsx)(f,{children:(0,r.jsx)("p",{children:d.tokenExpiration.toLocaleString()})})]})})]}),void 0!==d.rateLimitRemaining&&(0,r.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,r.jsx)("span",{className:"text-muted-foreground",children:"Rate limit:"}),(0,r.jsxs)("span",{className:"font-medium",children:[d.rateLimitRemaining," remaining",d.rateLimitReset&&(0,r.jsx)(u,{children:(0,r.jsxs)(m,{children:[(0,r.jsxs)(x,{className:"ml-1 text-muted-foreground",children:["(resets in ",R(d.rateLimitReset),")"]}),(0,r.jsx)(f,{children:(0,r.jsxs)("p",{children:["Resets at ",d.rateLimitReset.toLocaleString()]})})]})})]})]}),d.errorCount>0&&(0,r.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,r.jsx)("span",{className:"text-muted-foreground",children:"Errors:"}),(0,r.jsx)("span",{className:"font-medium text-destructive",children:d.errorCount})]})]}),(0,r.jsx)(i.wL,{children:(0,r.jsx)(c.$,{variant:"outline",size:"sm",onClick:A,className:"ml-auto",disabled:N,children:N?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(v.A,{className:"h-4 w-4 mr-1 animate-spin"}),"Refreshing..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(v.A,{className:"h-4 w-4 mr-1"}),"Refresh"]})})})]}):null}function N(e){let{userId:s,businessId:t,title:n="Social Media Integrations",description:l="Monitor the status of your connected social media accounts"}=e,[o,d]=(0,a.useState)(0),[u,m]=(0,a.useState)(!1);return(0,r.jsxs)(i.Zp,{className:"w-full",children:[(0,r.jsx)(i.aR,{children:(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(i.ZB,{children:n}),(0,r.jsx)(i.BT,{children:l})]}),(0,r.jsxs)(c.$,{variant:"outline",size:"sm",onClick:()=>{m(!0),d(e=>e+1),setTimeout(()=>{m(!1)},2e3)},disabled:u,children:[(0,r.jsx)(v.A,{className:"h-4 w-4 mr-1 ".concat(u?"animate-spin":"")}),u?"Refreshing...":"Refresh All"]})]})}),(0,r.jsx)(i.Wu,{children:(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:["twitter","facebook","instagram","linkedin"].map(e=>(0,r.jsx)(b,{platform:e,userId:s,businessId:t},"".concat(e,"-").concat(o)))})})]})}var y=t(32461),w=t(35695);function k(e){let{title:s,description:t,showBackButton:a=!1,children:n,actions:i}=e,l=(0,w.useRouter)();return(0,r.jsxs)("div",{className:"border-b border-gray-200 pb-4 mb-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[a&&(0,r.jsx)(c.$,{variant:"ghost",size:"sm",onClick:()=>l.back(),className:"p-2",children:(0,r.jsx)(y.A,{className:"h-4 w-4"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:s}),t&&(0,r.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:t})]})]}),i&&(0,r.jsx)("div",{className:"flex items-center space-x-2",children:i})]}),n&&(0,r.jsx)("div",{className:"mt-4",children:n})]})}var C=t(17313),A=t(55365),R=t(81284),T=t(6874),I=t.n(T);function E(){var e;let{data:s}=(0,n.useSession)(),t=null==s||null==(e=s.user)?void 0:e.id,a=[{id:"business1",name:"Business One"},{id:"business2",name:"Business Two"}];return(0,r.jsxs)("div",{className:"container mx-auto py-6 space-y-6",children:[(0,r.jsx)(k,{title:"Social Media Monitoring",description:"Monitor the status and health of your social media integrations"}),(0,r.jsxs)(A.Fc,{children:[(0,r.jsx)(R.A,{className:"h-4 w-4"}),(0,r.jsx)(A.XL,{children:"Information"}),(0,r.jsx)(A.TN,{children:"This dashboard shows the status of your social media integrations, including connection status, token expiration dates, and API rate limits. If you encounter any issues, you can refresh the status or reconnect your accounts."})]}),(0,r.jsxs)(C.tU,{defaultValue:"personal",children:[(0,r.jsxs)(C.j7,{children:[(0,r.jsx)(C.Xi,{value:"personal",children:"Personal Accounts"}),(0,r.jsx)(C.Xi,{value:"business",children:"Business Accounts"})]}),(0,r.jsxs)(C.av,{value:"personal",className:"space-y-6",children:[(0,r.jsx)(N,{userId:t,title:"Personal Social Media Accounts",description:"Status of your personal social media connections"}),(0,r.jsxs)(i.Zp,{children:[(0,r.jsxs)(i.aR,{children:[(0,r.jsx)(i.ZB,{children:"Connection Management"}),(0,r.jsx)(i.BT,{children:"Connect or reconnect your social media accounts"})]}),(0,r.jsxs)(i.Wu,{className:"space-y-4",children:[(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"If you need to reconnect an account or connect a new platform, use the buttons below. This will take you through the OAuth flow for the selected platform."}),(0,r.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,r.jsx)(c.$,{variant:"outline",asChild:!0,children:(0,r.jsx)(I(),{href:"/dashboard/settings/connections/twitter",children:"Connect Twitter"})}),(0,r.jsx)(c.$,{variant:"outline",asChild:!0,children:(0,r.jsx)(I(),{href:"/dashboard/settings/connections/facebook",children:"Connect Facebook"})}),(0,r.jsx)(c.$,{variant:"outline",asChild:!0,children:(0,r.jsx)(I(),{href:"/dashboard/settings/connections/instagram",children:"Connect Instagram"})}),(0,r.jsx)(c.$,{variant:"outline",asChild:!0,children:(0,r.jsx)(I(),{href:"/dashboard/settings/connections/linkedin",children:"Connect LinkedIn"})})]})]})]})]}),(0,r.jsx)(C.av,{value:"business",className:"space-y-6",children:a.length>0?(0,r.jsxs)(r.Fragment,{children:[a.map(e=>(0,r.jsx)(N,{businessId:e.id,title:"".concat(e.name," Social Media Accounts"),description:"Status of social media connections for ".concat(e.name)},e.id)),(0,r.jsxs)(i.Zp,{children:[(0,r.jsxs)(i.aR,{children:[(0,r.jsx)(i.ZB,{children:"Business Connection Management"}),(0,r.jsx)(i.BT,{children:"Connect or reconnect social media accounts for your businesses"})]}),(0,r.jsxs)(i.Wu,{children:[(0,r.jsx)("p",{className:"text-sm text-muted-foreground mb-4",children:"Select a business to manage its social media connections:"}),(0,r.jsx)("div",{className:"flex flex-wrap gap-2",children:a.map(e=>(0,r.jsx)(c.$,{variant:"outline",asChild:!0,children:(0,r.jsx)(I(),{href:"/dashboard/businesses/".concat(e.id,"/settings/connections"),children:e.name})},e.id))})]})]})]}):(0,r.jsxs)(A.Fc,{variant:"default",children:[(0,r.jsx)(g.A,{className:"h-4 w-4"}),(0,r.jsx)(A.XL,{children:"No businesses found"}),(0,r.jsx)(A.TN,{children:"You don't have any businesses set up yet. Create a business to manage its social media connections."})]})})]}),(0,r.jsxs)(i.Zp,{children:[(0,r.jsxs)(i.aR,{children:[(0,r.jsx)(i.ZB,{children:"Social Media API Status"}),(0,r.jsx)(i.BT,{children:"Current status of social media platform APIs"})]}),(0,r.jsx)(i.Wu,{children:(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("p",{className:"text-sm",children:"These statuses are based on our system's ability to connect to each platform's API. For official status, visit the platform's status page:"}),(0,r.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-sm",children:[(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"https://api.twitterstat.us/",target:"_blank",rel:"noopener noreferrer",className:"text-primary hover:underline",children:"Twitter API Status"})}),(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"https://developers.facebook.com/status/dashboard/",target:"_blank",rel:"noopener noreferrer",className:"text-primary hover:underline",children:"Facebook API Status"})}),(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"https://developers.facebook.com/status/dashboard/",target:"_blank",rel:"noopener noreferrer",className:"text-primary hover:underline",children:"Instagram API Status"})}),(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"https://www.linkedin.com/developers/apps",target:"_blank",rel:"noopener noreferrer",className:"text-primary hover:underline",children:"LinkedIn Developers"})})]})]})})]})]})}},55365:(e,s,t)=>{"use strict";t.d(s,{Fc:()=>c,TN:()=>d,XL:()=>o});var r=t(95155),a=t(12115),n=t(74466),i=t(59434);let l=(0,n.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),c=a.forwardRef((e,s)=>{let{className:t,variant:a,...n}=e;return(0,r.jsx)("div",{ref:s,role:"alert",className:(0,i.cn)(l({variant:a}),t),...n})});c.displayName="Alert";let o=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("h5",{ref:s,className:(0,i.cn)("mb-1 font-medium leading-none tracking-tight",t),...a})});o.displayName="AlertTitle";let d=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("div",{ref:s,className:(0,i.cn)("text-sm [&_p]:leading-relaxed",t),...a})});d.displayName="AlertDescription"},59434:(e,s,t)=>{"use strict";t.d(s,{Yq:()=>i,cn:()=>n,uD:()=>l});var r=t(52596),a=t(39688);function n(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,a.QP)((0,r.$)(s))}function i(e){return new Date(e).toLocaleDateString("ar-SA",{day:"numeric",month:"long",year:"numeric"})}function l(e){switch(e){case"TWITTER":return"تويتر";case"FACEBOOK":return"فيسبوك";case"INSTAGRAM":return"انستغرام";case"LINKEDIN":return"لينكد إن";case"TIKTOK":return"تيك توك";default:return e}}},66695:(e,s,t)=>{"use strict";t.d(s,{BT:()=>o,Wu:()=>d,ZB:()=>c,Zp:()=>i,aR:()=>l,wL:()=>u});var r=t(95155),a=t(12115),n=t(59434);let i=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("div",{ref:s,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...a})});i.displayName="Card";let l=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("div",{ref:s,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",t),...a})});l.displayName="CardHeader";let c=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("div",{ref:s,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",t),...a})});c.displayName="CardTitle";let o=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("div",{ref:s,className:(0,n.cn)("text-sm text-muted-foreground",t),...a})});o.displayName="CardDescription";let d=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("div",{ref:s,className:(0,n.cn)("p-6 pt-0",t),...a})});d.displayName="CardContent";let u=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("div",{ref:s,className:(0,n.cn)("flex items-center p-6 pt-0",t),...a})});u.displayName="CardFooter"}},e=>{var s=s=>e(e.s=s);e.O(0,[8096,7358],()=>s(28154)),_N_E=e.O()}]);