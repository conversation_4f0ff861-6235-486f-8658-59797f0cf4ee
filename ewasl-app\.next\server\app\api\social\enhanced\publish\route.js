"use strict";(()=>{var e={};e.id=5952,e.ids=[5952],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{e.exports=require("punycode")},27910:e=>{e.exports=require("stream")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{e.exports=require("tls")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94035:(e,t,r)=>{r.d(t,{g:()=>d});var s=r(15900),a=r(96606),o=r(27002);class n extends o.g{async refreshToken(e){return{refreshToken:"",expiresIn:0,accessToken:"",id:"",name:"",picture:"",username:""}}async generateAuthUrl(){let e=`instagram_${this.makeId(6)}`,t=this.makeId(10);return{url:`https://www.facebook.com/v20.0/dialog/oauth?client_id=${process.env.FACEBOOK_APP_ID}&redirect_uri=${encodeURIComponent("https://app.ewasl.com/api/facebook/callback")}&state=${e}&scope=${this.scopes.join(",")}`,codeVerifier:t,state:e}}async authenticate(e){try{let t=await this.fetch(`https://graph.facebook.com/v20.0/oauth/access_token?client_id=${process.env.FACEBOOK_APP_ID}&redirect_uri=${encodeURIComponent(`https://app.ewasl.com/api/facebook/callback${e.refresh?`?refresh=${e.refresh}`:""}`)}&client_secret=${process.env.FACEBOOK_APP_SECRET}&code=${e.code}`),{access_token:r}=await t.json(),s=await this.fetch(`https://graph.facebook.com/v20.0/me?fields=id,name,picture&access_token=${r}`),{id:a,name:o,picture:{data:{url:n}}}=await s.json();return{id:a,name:o,accessToken:r,refreshToken:r,expiresIn:5184e3,picture:n,username:""}}catch(e){throw console.error("[InstagramEnhanced] Authentication error:",e),Error(`Instagram authentication failed: ${e instanceof Error?e.message:e}`)}}async getInstagramBusinessAccounts(e){try{let t=await this.fetch(`https://graph.facebook.com/v20.0/me/accounts?fields=id,name,instagram_business_account&access_token=${e}`),{data:r}=await t.json(),s=[];for(let e of r)if(e.instagram_business_account)try{let t=await this.fetch(`https://graph.facebook.com/v20.0/${e.instagram_business_account.id}?fields=id,username,name,profile_picture_url&access_token=${e.access_token}`),r=await t.json();s.push({id:r.id,name:r.name||r.username,username:r.username,picture:r.profile_picture_url,type:"business",pageId:e.id,pageAccessToken:e.access_token})}catch(e){console.error("[InstagramEnhanced] Error fetching IG account details:",e)}return s}catch(e){return console.error("[InstagramEnhanced] Get business accounts error:",e),[]}}async post(e,t,r,s){try{let[s]=r;if(!s.media||0===s.media.length)throw Error("Instagram posts require at least one media item");let a=[];if(1===s.media.length){let r=s.media[0],o=await this.createMediaContainer(e,t,r,s.message),n=await this.publishMedia(e,t,o);a.push({id:s.id,postId:n.id,releaseURL:`https://www.instagram.com/p/${n.id}/`,status:"posted"})}else{let r=await Promise.all(s.media.map(r=>this.createMediaContainer(e,t,r))),o=await this.createCarouselContainer(e,t,r,s.message),n=await this.publishMedia(e,t,o);a.push({id:s.id,postId:n.id,releaseURL:`https://www.instagram.com/p/${n.id}/`,status:"posted"})}return a}catch(e){throw console.error("[InstagramEnhanced] Post error:",e),Error(`Instagram posting failed: ${e instanceof Error?e.message:e}`)}}async createMediaContainer(e,t,r,s){try{let t="video"===r.type||r.url.includes(".mp4"),a={image_url:t?void 0:r.url,video_url:t?r.url:void 0,media_type:t?"VIDEO":"IMAGE"};s&&(a.caption=s);let o=await this.fetch(`https://graph.facebook.com/v20.0/${e}/media`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)}),{id:n}=await o.json();return n}catch(e){throw console.error("[InstagramEnhanced] Create media container error:",e),e}}async createCarouselContainer(e,t,r,s){try{let t=await this.fetch(`https://graph.facebook.com/v20.0/${e}/media`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({media_type:"CAROUSEL",children:r.join(","),caption:s})}),{id:a}=await t.json();return a}catch(e){throw console.error("[InstagramEnhanced] Create carousel container error:",e),e}}async publishMedia(e,t,r){try{let t=await this.fetch(`https://graph.facebook.com/v20.0/${e}/media_publish`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({creation_id:r})});return await t.json()}catch(e){throw console.error("[InstagramEnhanced] Publish media error:",e),e}}async getMediaInsights(e,t){try{let r=await this.fetch(`https://graph.facebook.com/v20.0/${e}/insights?metric=impressions,reach,engagement&access_token=${t}`);return await r.json()}catch(e){return console.error("[InstagramEnhanced] Get media insights error:",e),null}}async getAccountInsights(e,t,r="day",s,a){try{let o=`https://graph.facebook.com/v20.0/${e}/insights?metric=impressions,reach,profile_views&period=${r}&access_token=${t}`;s&&(o+=`&since=${s}`),a&&(o+=`&until=${a}`);let n=await this.fetch(o);return await n.json()}catch(e){return console.error("[InstagramEnhanced] Get account insights error:",e),null}}constructor(...e){super(...e),this.identifier="instagram",this.name="Instagram Business",this.isBetweenSteps=!0,this.scopes=["instagram_basic","instagram_content_publish","pages_show_list","pages_read_engagement","business_management"]}}var i=r(68575),c=r(80726);class l{constructor(){this.providers=new Map,this.supabase=c.sY,this.initializeProviders()}initializeProviders(){let e=new s.K;this.providers.set(e.identifier,e);let t=new a.i;this.providers.set(t.identifier,t);let r=new n;this.providers.set(r.identifier,r);let o=new i.$;this.providers.set(o.identifier,o)}getProvider(e){return this.providers.get(e)||null}getAllProviders(){return Array.from(this.providers.values())}async generateAuthUrl(e){let t=this.getProvider(e);if(!t)throw Error(`Provider not found for platform: ${e}`);try{return await t.generateAuthUrl()}catch(t){throw console.error(`[IntegrationManager] Auth URL generation failed for ${e}:`,t),t}}async authenticate(e,t){let r=this.getProvider(e);if(!r)throw Error(`Provider not found for platform: ${e}`);try{return await r.authenticate(t)}catch(t){throw console.error(`[IntegrationManager] Authentication failed for ${e}:`,t),t}}async refreshToken(e,t){let r=this.getProvider(e);if(!r)throw Error(`Provider not found for platform: ${e}`);try{return await r.refreshToken(t)}catch(t){throw console.error(`[IntegrationManager] Token refresh failed for ${e}:`,t),t}}async post(e,t){try{let{data:r,error:s}=await this.supabase.from("social_accounts").select("*").eq("id",e).single();if(s||!r)throw Error(`Integration not found: ${e}`);let a=this.getProvider(r.platform);if(!a)throw Error(`Provider not found for platform: ${r.platform}`);if(r.expires_at&&new Date(r.expires_at)<=new Date)if(r.refresh_token){let t=await a.refreshToken(r.refresh_token);await this.supabase.from("social_accounts").update({access_token:t.accessToken,refresh_token:t.refreshToken,expires_at:t.expiresIn?new Date(Date.now()+1e3*t.expiresIn).toISOString():null,updated_at:new Date().toISOString()}).eq("id",e),r.access_token=t.accessToken}else throw Error(`Token expired and no refresh token available for ${r.platform}`);let o=await a.post(r.account_id,r.access_token,t,r);return await this.logPostActivity(r.user_id,r.platform,t,o),o}catch(t){throw console.error(`[IntegrationManager] Post failed for integration ${e}:`,t),t}}async testConnection(e){try{let{data:t,error:r}=await this.supabase.from("social_accounts").select("*").eq("id",e).single();if(r||!t)return{success:!1,error:`Integration not found: ${e}`};if(!this.getProvider(t.platform))return{success:!1,error:`Provider not found for platform: ${t.platform}`};return{success:!0,accountInfo:{platform:t.platform,accountName:t.account_name,accountUsername:t.account_username,lastUsed:t.updated_at}}}catch(e){return{success:!1,error:e instanceof Error?e.message:"Unknown error"}}}async getBusinessAccounts(e,t){let r=this.getProvider(e);if(!r)throw Error(`Provider not found for platform: ${e}`);try{switch(e){case"linkedin":if(r.getCompanyPages)return(await r.getCompanyPages(t)).map(e=>({id:e.id,name:e.name,username:e.vanityName,picture:e.logoUrl,type:"business"}));break;case"facebook":if(r.getBusinessPages)return await r.getBusinessPages(t);break;case"instagram":if(r.getInstagramBusinessAccounts)return await r.getInstagramBusinessAccounts(t)}return[]}catch(t){throw console.error(`[IntegrationManager] Failed to get business accounts for ${e}:`,t),t}}async logPostActivity(e,t,r,s){try{await this.supabase.from("activity_logs").insert({user_id:e,platform:t,action:"post_published",details:{post_count:r.length,success_count:s.filter(e=>"posted"===e.status).length,failed_count:s.filter(e=>"failed"===e.status).length,post_ids:s.map(e=>e.postId)},created_at:new Date().toISOString()})}catch(e){console.error("[IntegrationManager] Failed to log post activity:",e)}}async getIntegrationStats(e){try{let{data:t,error:r}=await this.supabase.from("social_accounts").select("platform, is_active, updated_at").eq("user_id",e);if(r)throw r;let{data:s,error:a}=await this.supabase.from("activity_logs").select("platform, action, created_at, details").eq("user_id",e).order("created_at",{ascending:!1}).limit(10);if(a)throw a;let o=t?.reduce((e,t)=>(e[t.platform]=(e[t.platform]||0)+1,e),{})||{};return{totalIntegrations:t?.length||0,activeIntegrations:t?.filter(e=>e.is_active).length||0,platformBreakdown:o,recentActivity:s||[]}}catch(e){throw console.error("[IntegrationManager] Failed to get integration stats:",e),e}}}let d=new l},94419:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>_,routeModule:()=>h,serverHooks:()=>f,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>g});var s={};r.r(s),r.d(s,{GET:()=>p,POST:()=>u});var a=r(96559),o=r(48088),n=r(37719),i=r(32190),c=r(2507),l=r(94035),d=r(80726);async function u(e){let t=(0,c.createClient)();try{let t,{integrationId:r,content:s,mediaUrls:a=[],scheduledAt:o,platformSettings:n={}}=await e.json();if(!r||!s)return i.NextResponse.json({error:"Integration ID and content are required"},{status:400});let c=d.sY,{data:u,error:p}=await c.from("social_accounts").select("*").eq("id",r).eq("is_active",!0).single();if(p||!u)return i.NextResponse.json({error:"Integration not found or inactive"},{status:404});let h=[{id:`post_${Date.now()}`,message:s,settings:n,media:a.map((e,t)=>({type:e.includes(".mp4")||e.includes(".mov")?"video":"image",url:e,path:`media_${t}`}))}];if(o){let{data:e,error:t}=await c.from("scheduled_posts").insert({user_id:u.user_id,integration_id:r,content:s,media_urls:a,scheduled_at:o,platform_settings:n,status:"scheduled",created_at:new Date().toISOString()}).select().single();if(t)throw t;return i.NextResponse.json({success:!0,scheduled:!0,postId:e.id,scheduledAt:o,message:"Post scheduled successfully"})}let m=0;for(;m<3;)try{t=await l.g.post(r,h);break}catch(e){if(m++,console.error(`[Enhanced Publish API] Attempt ${m} failed:`,e),m>=3)throw e;await new Promise(e=>setTimeout(e,1e3*Math.pow(2,m)))}if(!t||0===t.length)throw Error("No results returned from publishing");let{data:g,error:f}=await c.from("posts").insert({user_id:u.user_id,integration_id:r,content:s,media_urls:a,platform_post_id:t[0].postId,platform_url:t[0].releaseURL,status:"posted"===t[0].status?"published":"failed",platform_settings:n,published_at:new Date().toISOString(),created_at:new Date().toISOString()}).select().single();return f&&console.error("[Enhanced Publish API] Failed to save post record:",f),await c.from("activity_logs").insert({user_id:u.user_id,platform:u.platform,action:"post_published",details:{post_id:g?.id,platform_post_id:t[0].postId,platform_url:t[0].releaseURL,content_length:s.length,media_count:a.length,retry_count:m},created_at:new Date().toISOString()}),i.NextResponse.json({success:!0,results:t,post:g?{id:g.id,platformPostId:t[0].postId,platformUrl:t[0].releaseURL,status:t[0].status,publishedAt:g.published_at}:null,retryCount:m})}catch(e){console.error("[Enhanced Publish API] Error:",e);try{await t.from("activity_logs").insert({user_id:"system",platform:"system",action:"publish_error",details:{error:e instanceof Error?e.message:"Unknown error",stack:e instanceof Error?e.stack:void 0,timestamp:new Date().toISOString()},created_at:new Date().toISOString()})}catch(e){console.error("[Enhanced Publish API] Failed to log error:",e)}return i.NextResponse.json({error:"Failed to publish post",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}async function p(e){(0,c.createClient)();try{let{searchParams:t}=new URL(e.url),r=t.get("integrationId");if(!r)return i.NextResponse.json({error:"Integration ID is required"},{status:400});let s=await l.g.testConnection(r);return i.NextResponse.json({success:s.success,...s})}catch(e){return console.error("[Enhanced Publish API] Connection test error:",e),i.NextResponse.json({error:"Failed to test connection",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}let h=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/social/enhanced/publish/route",pathname:"/api/social/enhanced/publish",filename:"route",bundlePath:"app/api/social/enhanced/publish/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Ewasl\\eWasl.com_Cursor_Digital_Ocean\\ewasl-app\\src\\app\\api\\social\\enhanced\\publish\\route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:m,workUnitAsyncStorage:g,serverHooks:f}=h;function _(){return(0,n.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:g})}},94735:e=>{e.exports=require("events")}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4243,6167,580,4386,1053,2721],()=>r(94419));module.exports=s})();