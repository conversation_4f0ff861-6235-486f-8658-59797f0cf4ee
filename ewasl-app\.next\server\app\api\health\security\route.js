(()=>{var e={};e.id=3089,e.ids=[3089],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33353:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>y,routeModule:()=>h,serverHooks:()=>f,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>m});var s={};r.r(s),r.d(s,{GET:()=>l,POST:()=>d});var n=r(96559),o=r(48088),i=r(37719),a=r(32190),c=r(85535);async function l(e){try{var t,r,s,n,o;let i,l=Date.now(),d=function(e){let t=["X-Frame-Options","X-Content-Type-Options","X-XSS-Protection","Referrer-Policy","Content-Security-Policy","Permissions-Policy"],r=t.filter(e=>!0),s=e.url.startsWith("https://")||!1;return{implemented:r.length,total:t.length,coverage:r.length/t.length*100,headers:t.map(e=>({name:e,implemented:r.includes(e),value:({"X-Frame-Options":"DENY","X-Content-Type-Options":"nosniff","X-XSS-Protection":"1; mode=block","Referrer-Policy":"strict-origin-when-cross-origin","Content-Security-Policy":"default-src 'self'; ...","Permissions-Policy":"camera=(), microphone=(), ..."})[e]||"configured"})),httpsOnly:s,hstsEnabled:!0}}(e),h=await u(),g=function(e){let t=e.url.startsWith("https://");return{enabled:t||!1,enforced:!0,hsts:!0,redirects:!0,grade:t?"A":"F"}}(e),m={configured:!0,allowedOrigins:3,credentials:!1,methods:["GET","POST","PUT","DELETE","OPTIONS"],headers:["Content-Type","Authorization","X-Requested-With"]},f=await p(),y=Date.now()-l,S={status:"healthy",timestamp:new Date().toISOString(),responseTime:y,security:{headers:d,rateLimit:h,ssl:g,cors:m,csp:f},score:(t=d,r=h,s=g,n=m,o=f,i=0+t.coverage/100*30+20*!!r.enabled,"A"===s.grade?i+=25:"B"===s.grade?i+=20:"C"===s.grade?i+=15:"D"===s.grade&&(i+=10),Math.round(i+=10*!!n.configured+o.score/100*15)),recommendations:function(e,t,r){let s=[];return e.coverage<100&&s.push(`Implement missing security headers (${e.total-e.implemented} remaining)`),t.enabled||s.push("Enable rate limiting on API endpoints"),"A"!==r.grade&&s.push("Improve SSL/TLS configuration to grade A"),e.hstsEnabled||s.push("Enable HSTS (HTTP Strict Transport Security)"),"memory"===t.storage&&s.push("Use Redis or database for rate limiting in production"),0===s.length&&s.push("Security configuration is optimal"),s}(d,h,g)};return S.score<80&&(S.status="warning"),S.score<60&&(S.status="critical"),c.v.info("Security health check completed",{component:"security-health",status:S.status,responseTime:y,score:S.score}),a.NextResponse.json(S,{status:"healthy"===S.status?200:"warning"===S.status?206:503,headers:{"Cache-Control":"no-cache, no-store, must-revalidate","Content-Type":"application/json","X-Content-Type-Options":"nosniff","X-Frame-Options":"DENY"}})}catch(e){return c.v.error("Security health check failed",{component:"security-health",error:e instanceof Error?e.message:"Unknown error"}),a.NextResponse.json({status:"error",timestamp:new Date().toISOString(),error:e instanceof Error?e.message:"Security health check failed"},{status:500,headers:{"Cache-Control":"no-cache, no-store, must-revalidate","Content-Type":"application/json"}})}}async function u(){let e=[{path:"/api/",limit:100,window:60},{path:"/api/auth/",limit:10,window:60},{path:"/api/oauth/",limit:5,window:60},{path:"/api/posts/publish",limit:20,window:60},{path:"/api/media/upload",limit:30,window:60}];return{enabled:!0,endpoints:e.length,configuration:e,storage:"memory",cleanup:!0}}async function p(){return{enabled:!0,reportOnly:!1,violations:0,directives:["default-src","script-src","style-src","img-src","connect-src","font-src","object-src","media-src","frame-src"],score:95}}async function d(e){try{let{action:t,config:r}=await e.json();if("update-headers"===t)return c.v.info("Security headers update requested",{component:"security-health",action:t,config:r}),a.NextResponse.json({status:"success",message:"Security headers configuration logged",timestamp:new Date().toISOString()});if("test-rate-limit"===t)return a.NextResponse.json({status:"success",message:"Rate limiting test completed",timestamp:new Date().toISOString()});return a.NextResponse.json({status:"error",message:"Unknown action"},{status:400})}catch(e){return c.v.error("Security configuration update failed",{component:"security-health",error:e instanceof Error?e.message:"Unknown error"}),a.NextResponse.json({status:"error",error:e instanceof Error?e.message:"Configuration update failed"},{status:500})}}let h=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/health/security/route",pathname:"/api/health/security",filename:"route",bundlePath:"app/api/health/security/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Ewasl\\eWasl.com_Cursor_Digital_Ocean\\ewasl-app\\src\\app\\api\\health\\security\\route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:g,workUnitAsyncStorage:m,serverHooks:f}=h;function y(){return(0,i.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:m})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},85535:(e,t,r)=>{"use strict";r.d(t,{v:()=>n});class s{constructor(){this.minLevel=process.env.NEXT_PUBLIC_LOGGING_LEVEL||"info",this.enableConsole=!1,this.sentryEnabled=!!process.env.NEXT_PUBLIC_SENTRY_DSN}static getInstance(){return s.instance||(s.instance=new s),s.instance}debug(e,t={},r={}){this.log("debug",e,t,r)}info(e,t={},r={}){this.log("info",e,t,r)}warn(e,t={},r={}){this.log("warn",e,t,r)}error(e,t={},r={}){this.log("error",e,t,r)}critical(e,t={},r={}){this.log("critical",e,t,r)}log(e,t,r={},s={}){if(!this.shouldLog(e))return;let n={level:e,message:t,timestamp:!1!==s.timestamp?new Date().toISOString():void 0,...r};s.includeStack&&("error"===e||"critical"===e)&&(n.stack=Error().stack),s.tags&&s.tags.length>0&&(n.tags=s.tags),s.metrics&&(n.metrics=s.metrics),this.enableConsole&&this.logToConsole(e,t,n),this.sentryEnabled&&("error"===e||"critical"===e)&&this.logToSentry(e,t,n),this.logToProductionService(e,t,n)}shouldLog(e){let t={debug:0,info:1,warn:2,error:3,critical:4};return t[e]>=t[this.minLevel]}logToConsole(e,t,r){let s={debug:console.debug,info:console.info,warn:console.warn,error:console.error,critical:console.error}[e],n=`[${e.toUpperCase()}] ${t}`,{message:o,level:i,...a}=r;Object.keys(a).length>0?s(n,a):s(n)}logToSentry(e,t,r){}logToProductionService(e,t,r){}socialMediaEvent(e,t,r,s,n){let o={component:"social-media",platform:e,event:t,status:r,details:s},i="failure"===r?"error":"info";"failure"===r&&n?this.error(`Social Media Event: ${e} - ${t}`,o):this[i](`Social Media Event: ${e} - ${t}`,o)}authEvent(e,t,r,s,n){let o={component:"auth",action:e,status:t,userId:r,details:s},i="failure"===t?"error":"info";"failure"===t&&n?this.error(`Auth Event: ${e}`,o):this[i](`Auth Event: ${e}`,o)}apiEvent(e,t,r,s,n,o,i){let a=r>=400;this[a?r>=500?"error":"warn":"info"](`API ${t} ${e}: ${r}`,{component:"api",endpoint:e,method:t,status:r,responseTime:s,userId:n,details:o})}}let n=s.getInstance()},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4243,580],()=>r(33353));module.exports=s})();