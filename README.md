# eWasl Social Media Management Platform

A comprehensive social media management platform built with Next.js, Supabase, and modern web technologies.

## Features

- 📱 Multi-platform social media management (Facebook, Instagram, Twitter, LinkedIn)
- 📝 Rich text post editor with Arabic RTL support
- 📅 Advanced post scheduling and automation
- 📊 Analytics and performance tracking
- 🔐 Secure OAuth authentication
- 🌐 Arabic and English language support
- 📱 Responsive design for all devices

## Tech Stack

- **Frontend**: Next.js 14, React, TypeScript, Tailwind CSS
- **Backend**: Next.js API Routes, Supabase
- **Database**: PostgreSQL (Supabase)
- **Authentication**: Supabase Auth with OAuth
- **Storage**: Supabase Storage
- **Deployment**: Vercel

## Getting Started

1. Clone the repository
2. Install dependencies: `npm install`
3. Set up environment variables
4. Run the development server: `npm run dev`

## Environment Variables

Create a `.env.local` file with the following variables:

```
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
FACEBOOK_APP_ID=your_facebook_app_id
FACEBOOK_APP_SECRET=your_facebook_app_secret
NEXTAUTH_SECRET=your_nextauth_secret
NEXTAUTH_URL=your_app_url
```

## Deployment

The application is automatically deployed to Vercel when changes are pushed to the master branch.
✅ Webhook integration fixed and tested - automatic deployments now working!

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

## License

This project is licensed under the MIT License.

---

**Last Updated**: 2025-07-20 - Repository migration completed, triggering new deployment from Mr-Taha-1/eWasl.com_Cursor_Digital_Ocean