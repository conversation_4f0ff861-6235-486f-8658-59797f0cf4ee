{"version": 3, "file": "index.umd.js", "sources": ["../src/gapcursor.ts"], "sourcesContent": ["import {\n  callOrReturn,\n  Extension,\n  getExtensionField,\n  ParentConfig,\n} from '@tiptap/core'\nimport { gapCursor } from '@tiptap/pm/gapcursor'\n\ndeclare module '@tiptap/core' {\n  interface NodeConfig<Options, Storage> {\n    /**\n     * A function to determine whether the gap cursor is allowed at the current position. Must return `true` or `false`.\n     * @default null\n     */\n    allowGapCursor?:\n      | boolean\n      | null\n      | ((this: {\n        name: string,\n        options: Options,\n        storage: Storage,\n        parent: ParentConfig<NodeConfig<Options>>['allowGapCursor'],\n      }) => boolean | null),\n  }\n}\n\n/**\n * This extension allows you to add a gap cursor to your editor.\n * A gap cursor is a cursor that appears when you click on a place\n * where no content is present, for example inbetween nodes.\n * @see https://tiptap.dev/api/extensions/gapcursor\n */\nexport const Gapcursor = Extension.create({\n  name: 'gapCursor',\n\n  addProseMirrorPlugins() {\n    return [\n      gapCursor(),\n    ]\n  },\n\n  extendNodeSchema(extension) {\n    const context = {\n      name: extension.name,\n      options: extension.options,\n      storage: extension.storage,\n    }\n\n    return {\n      allowGapCursor: callOrReturn(getExtensionField(extension, 'allowGapCursor', context)) ?? null,\n    }\n  },\n})\n"], "names": ["Extension", "gapCursor", "callOrReturn", "getExtensionField"], "mappings": ";;;;;;EA0BA;;;;;EAKG;AACU,QAAA,SAAS,GAAGA,cAAS,CAAC,MAAM,CAAC;EACxC,IAAA,IAAI,EAAE,WAAW;MAEjB,qBAAqB,GAAA;UACnB,OAAO;EACL,YAAAC,mBAAS,EAAE;WACZ;OACF;EAED,IAAA,gBAAgB,CAAC,SAAS,EAAA;;EACxB,QAAA,MAAM,OAAO,GAAG;cACd,IAAI,EAAE,SAAS,CAAC,IAAI;cACpB,OAAO,EAAE,SAAS,CAAC,OAAO;cAC1B,OAAO,EAAE,SAAS,CAAC,OAAO;WAC3B;UAED,OAAO;EACL,YAAA,cAAc,EAAE,CAAA,EAAA,GAAAC,iBAAY,CAACC,sBAAiB,CAAC,SAAS,EAAE,gBAAgB,EAAE,OAAO,CAAC,CAAC,mCAAI,IAAI;WAC9F;OACF;EACF,CAAA;;;;;;;;;;;"}