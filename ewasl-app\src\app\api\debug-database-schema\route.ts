import { NextRequest, NextResponse } from 'next/server';
import { createServiceRoleClient } from '@/lib/supabase/server';
import { createErrorResponse } from '@/lib/auth/api-auth';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

// Debug endpoint to check database schema and relationships
export async function POST(request: NextRequest) {
  console.log('🔍 Database Schema Debug - Started');
  
  try {
    // Use service role client to bypass RLS and access all data
    const supabase = createServiceRoleClient();
    
    const debugResults = {
      timestamp: new Date().toISOString(),
      phase: 'DATABASE_SCHEMA_DEBUG',
      tests: []
    };
    
    // Test 1: Check if publishing_results table exists
    console.log('📋 Test 1: Check Publishing Results Table');
    
    try {
      const { data: publishingResults, error: publishingError } = await supabase
        .from('publishing_results')
        .select('*')
        .limit(1);
      
      debugResults.tests.push({
        testName: 'Publishing Results Table Exists',
        success: !publishingError,
        error: publishingError?.message || null,
        tableExists: !publishingError
      });
      
      console.log(`${!publishingError ? '✅' : '❌'} Publishing Results Table: ${!publishingError ? 'EXISTS' : 'MISSING'}`);
      if (publishingError) {
        console.log(`   Error: ${publishingError.message}`);
      }
      
    } catch (error) {
      debugResults.tests.push({
        testName: 'Publishing Results Table Exists',
        success: false,
        error: error.message,
        tableExists: false
      });
      console.error('❌ Publishing results table check failed:', error);
    }
    
    // Test 2: Check social_accounts table structure
    console.log('👥 Test 2: Check Social Accounts Table Structure');
    
    try {
      const { data: socialAccounts, error: socialError } = await supabase
        .from('social_accounts')
        .select('id, platform, account_name, connection_status')
        .limit(5);
      
      debugResults.tests.push({
        testName: 'Social Accounts Table Structure',
        success: !socialError,
        error: socialError?.message || null,
        sampleData: socialAccounts || [],
        count: socialAccounts?.length || 0
      });
      
      console.log(`${!socialError ? '✅' : '❌'} Social Accounts Table: ${!socialError ? 'ACCESSIBLE' : 'ERROR'}`);
      if (socialError) {
        console.log(`   Error: ${socialError.message}`);
      } else {
        console.log(`   Sample Count: ${socialAccounts?.length || 0}`);
      }
      
    } catch (error) {
      debugResults.tests.push({
        testName: 'Social Accounts Table Structure',
        success: false,
        error: error.message
      });
      console.error('❌ Social accounts table check failed:', error);
    }
    
    // Test 3: Test relationship query between publishing_results and social_accounts
    console.log('🔗 Test 3: Test Publishing Results to Social Accounts Relationship');
    
    try {
      const { data: relationshipData, error: relationshipError } = await supabase
        .from('publishing_results')
        .select(`
          id,
          post_id,
          social_account_id,
          platform,
          success,
          social_accounts!inner(
            id,
            platform,
            account_name,
            connection_status
          )
        `)
        .limit(5);
      
      debugResults.tests.push({
        testName: 'Publishing Results to Social Accounts Relationship',
        success: !relationshipError,
        error: relationshipError?.message || null,
        relationshipData: relationshipData || [],
        count: relationshipData?.length || 0
      });
      
      console.log(`${!relationshipError ? '✅' : '❌'} Relationship Query: ${!relationshipError ? 'SUCCESS' : 'FAILED'}`);
      if (relationshipError) {
        console.log(`   Error: ${relationshipError.message}`);
      } else {
        console.log(`   Relationship Count: ${relationshipData?.length || 0}`);
      }
      
    } catch (error) {
      debugResults.tests.push({
        testName: 'Publishing Results to Social Accounts Relationship',
        success: false,
        error: error.message
      });
      console.error('❌ Relationship query failed:', error);
    }
    
    // Test 4: Check database schema information
    console.log('🗄️ Test 4: Check Database Schema Information');
    
    try {
      // Query information_schema to get table and column information
      const { data: tableInfo, error: schemaError } = await supabase
        .rpc('get_table_columns', { 
          table_names: ['publishing_results', 'social_accounts', 'posts', 'post_social_accounts', 'scheduled_posts_queue'] 
        });
      
      debugResults.tests.push({
        testName: 'Database Schema Information',
        success: !schemaError,
        error: schemaError?.message || null,
        schemaInfo: tableInfo || null
      });
      
      console.log(`${!schemaError ? '✅' : '❌'} Schema Information: ${!schemaError ? 'RETRIEVED' : 'FAILED'}`);
      if (schemaError) {
        console.log(`   Error: ${schemaError.message}`);
      }
      
    } catch (error) {
      debugResults.tests.push({
        testName: 'Database Schema Information',
        success: false,
        error: 'Schema RPC function not available'
      });
      console.log('⚠️ Schema RPC function not available - using alternative approach');
    }
    
    // Test 5: Check foreign key constraints
    console.log('🔑 Test 5: Check Foreign Key Constraints');
    
    try {
      // Try to create a test publishing_results entry to see if foreign key works
      const { data: testAccount } = await supabase
        .from('social_accounts')
        .select('id')
        .limit(1)
        .single();
      
      if (testAccount) {
        // Try to insert a test record to check foreign key constraint
        const testData = {
          post_id: '********-0000-0000-0000-************', // This will fail but test the constraint
          social_account_id: testAccount.id,
          platform: 'INSTAGRAM',
          success: false,
          error_message: 'Test constraint check'
        };
        
        const { error: insertError } = await supabase
          .from('publishing_results')
          .insert(testData);
        
        debugResults.tests.push({
          testName: 'Foreign Key Constraints',
          success: true, // Success means we could test the constraint
          constraintWorking: !!insertError, // Error means constraint is working
          error: insertError?.message || null,
          testAccountId: testAccount.id
        });
        
        console.log(`✅ Foreign Key Constraint Test: ${insertError ? 'WORKING' : 'MISSING'}`);
        if (insertError) {
          console.log(`   Constraint Error (Expected): ${insertError.message}`);
        }
      } else {
        debugResults.tests.push({
          testName: 'Foreign Key Constraints',
          success: false,
          error: 'No social accounts available for testing'
        });
      }
      
    } catch (error) {
      debugResults.tests.push({
        testName: 'Foreign Key Constraints',
        success: false,
        error: error.message
      });
      console.error('❌ Foreign key constraint test failed:', error);
    }
    
    // Analysis
    const publishingTableExists = debugResults.tests.find(t => t.testName === 'Publishing Results Table Exists')?.tableExists || false;
    const socialTableWorks = debugResults.tests.find(t => t.testName === 'Social Accounts Table Structure')?.success || false;
    const relationshipWorks = debugResults.tests.find(t => t.testName === 'Publishing Results to Social Accounts Relationship')?.success || false;
    const constraintTest = debugResults.tests.find(t => t.testName === 'Foreign Key Constraints');
    
    const analysis = {
      publishing_table_exists: publishingTableExists,
      social_accounts_accessible: socialTableWorks,
      relationship_query_works: relationshipWorks,
      foreign_key_constraint_working: constraintTest?.constraintWorking || false,
      schema_cache_issue: !relationshipWorks && publishingTableExists && socialTableWorks
    };
    
    const recommendations = [];
    
    if (!publishingTableExists) {
      recommendations.push('إنشاء جدول publishing_results مفقود');
    }
    
    if (!socialTableWorks) {
      recommendations.push('إصلاح مشاكل جدول social_accounts');
    }
    
    if (publishingTableExists && socialTableWorks && !relationshipWorks) {
      recommendations.push('إصلاح العلاقة بين publishing_results و social_accounts');
      recommendations.push('تحديث schema cache في Supabase');
    }
    
    if (!constraintTest?.constraintWorking && publishingTableExists) {
      recommendations.push('إضافة foreign key constraints مفقودة');
    }
    
    if (recommendations.length === 0) {
      recommendations.push('جميع العلاقات تعمل بشكل صحيح');
    }
    
    console.log('📊 Database Schema Debug Analysis Complete');
    console.log(`   Publishing Table: ${publishingTableExists ? 'EXISTS' : 'MISSING'}`);
    console.log(`   Relationship Query: ${relationshipWorks ? 'WORKS' : 'FAILED'}`);
    console.log(`   Schema Cache Issue: ${analysis.schema_cache_issue ? 'YES' : 'NO'}`);
    
    return NextResponse.json({
      success: true,
      message: 'تم فحص schema قاعدة البيانات',
      results: debugResults,
      analysis,
      recommendations,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('❌ Database schema debug error:', error);
    return createErrorResponse('خطأ في فحص schema قاعدة البيانات', 500, error);
  }
}
