(()=>{var e={};e.id=1014,e.ids=[1014],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},11997:e=>{"use strict";e.exports=require("punycode")},12412:e=>{"use strict";e.exports=require("assert")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function r(e){var r=Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}r.keys=()=>[],r.resolve=r,r.id=51906,e.exports=r},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58178:(e,r,s)=>{"use strict";s.d(r,{Nh:()=>u});var t=s(66167),i=s(66699),a=s(67008),o=s(4073),n=s(97287);(0,t.createClient)("https://nnxfzhxqzmriggulsudr.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY);let u={adapter:(0,n.O)({url:"https://nnxfzhxqzmriggulsudr.supabase.co",secret:process.env.SUPABASE_SERVICE_ROLE_KEY}),providers:[(0,i.A)({clientId:process.env.FACEBOOK_APP_ID,clientSecret:process.env.FACEBOOK_APP_SECRET,authorization:{params:{scope:"email,public_profile,pages_show_list,pages_manage_posts,pages_read_engagement,business_management,instagram_basic,instagram_content_publish"}}}),(0,a.Ay)({clientId:process.env.TWITTER_CLIENT_ID,clientSecret:process.env.TWITTER_CLIENT_SECRET,version:"2.0",authorization:{params:{scope:"tweet.read tweet.write users.read follows.read follows.write offline.access"}}}),(0,o.A)({clientId:process.env.LINKEDIN_CLIENT_ID,clientSecret:process.env.LINKEDIN_CLIENT_SECRET,authorization:{params:{scope:"r_liteprofile r_emailaddress w_member_social"}}})],callbacks:{jwt:async({token:e,user:r,account:s})=>(r&&(e.id=r.id,e.email=r.email),e),session:async({session:e,token:r})=>(r&&(e.user.id=r.id,e.user.email=r.email),e),signIn:async({user:e,account:r,profile:s})=>!0},pages:{signIn:"/auth/signin",signUp:"/auth/signup",error:"/auth/error"},session:{strategy:"jwt"},secret:process.env.NEXTAUTH_SECRET}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},74557:(e,r,s)=>{"use strict";s.r(r),s.d(r,{patchFetch:()=>E,routeModule:()=>l,serverHooks:()=>_,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>x});var t={};s.r(t),s.d(t,{GET:()=>c,POST:()=>c});var i=s(96559),a=s(48088),o=s(37719),n=s(19854),u=s.n(n),p=s(58178);let c=u()(p.Nh),l=new i.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/auth/[...nextauth]/route",pathname:"/api/auth/[...nextauth]",filename:"route",bundlePath:"app/api/auth/[...nextauth]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Ewasl\\eWasl.com_Cursor_Digital_Ocean\\ewasl-app\\src\\app\\api\\auth\\[...nextauth]\\route.ts",nextConfigOutput:"standalone",userland:t}),{workAsyncStorage:d,workUnitAsyncStorage:x,serverHooks:_}=l;function E(){return(0,o.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:x})}},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{},96559:(e,r,s)=>{"use strict";e.exports=s(44870)}};var r=require("../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[4243,6167,4999,8629],()=>s(74557));module.exports=t})();