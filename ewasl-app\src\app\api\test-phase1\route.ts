import { NextRequest, NextResponse } from 'next/server';
import { createClient, createServiceRoleClient } from '@/lib/supabase/server';
import { logger } from '@/lib/logging/logger';
import { createErrorResponse, EnhancedError } from '@/lib/errors/error-codes';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

/**
 * Phase 1 Implementation Test Endpoint
 * Tests all Phase 1 features: token refresh, monitoring, error handling
 * 
 * GET /api/test-phase1?test=all|tokens|health|metrics|logging
 */
export async function GET(request: NextRequest) {
  const startTime = Date.now();
  const { searchParams } = new URL(request.url);
  const testType = searchParams.get('test') || 'all';

  console.log('🧪 Phase 1 testing started:', { testType });

  try {
    // Get authenticated user
    const supabase = createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      throw new EnhancedError('AUTH_REQUIRED');
    }

    const testResults = {
      timestamp: new Date().toISOString(),
      testType,
      user: { id: user.id, email: user.email },
      results: {} as any,
      summary: {
        total: 0,
        passed: 0,
        failed: 0,
        duration: 0
      }
    };

    // Test token management
    if (testType === 'all' || testType === 'tokens') {
      console.log('🔄 Testing token management...');
      testResults.results.tokenManagement = await testTokenManagement(user.id);
      testResults.summary.total++;
      if (testResults.results.tokenManagement.success) {
        testResults.summary.passed++;
      } else {
        testResults.summary.failed++;
      }
    }

    // Test health monitoring
    if (testType === 'all' || testType === 'health') {
      console.log('🏥 Testing health monitoring...');
      testResults.results.healthMonitoring = await testHealthMonitoring();
      testResults.summary.total++;
      if (testResults.results.healthMonitoring.success) {
        testResults.summary.passed++;
      } else {
        testResults.summary.failed++;
      }
    }

    // Test metrics collection
    if (testType === 'all' || testType === 'metrics') {
      console.log('📊 Testing metrics collection...');
      testResults.results.metricsCollection = await testMetricsCollection(user.id);
      testResults.summary.total++;
      if (testResults.results.metricsCollection.success) {
        testResults.summary.passed++;
      } else {
        testResults.summary.failed++;
      }
    }

    // Test logging system
    if (testType === 'all' || testType === 'logging') {
      console.log('📝 Testing logging system...');
      testResults.results.loggingSystem = await testLoggingSystem(user.id);
      testResults.summary.total++;
      if (testResults.results.loggingSystem.success) {
        testResults.summary.passed++;
      } else {
        testResults.summary.failed++;
      }
    }

    // Test error handling
    if (testType === 'all' || testType === 'errors') {
      console.log('❌ Testing error handling...');
      testResults.results.errorHandling = await testErrorHandling();
      testResults.summary.total++;
      if (testResults.results.errorHandling.success) {
        testResults.summary.passed++;
      } else {
        testResults.summary.failed++;
      }
    }

    testResults.summary.duration = Date.now() - startTime;

    console.log(`🧪 Phase 1 testing completed: ${testResults.summary.passed}/${testResults.summary.total} passed`);

    return NextResponse.json({
      success: testResults.summary.failed === 0,
      message: testResults.summary.failed === 0 
        ? 'جميع اختبارات المرحلة الأولى نجحت'
        : `${testResults.summary.failed} اختبارات فشلت من أصل ${testResults.summary.total}`,
      ...testResults
    });

  } catch (error) {
    console.error('❌ Phase 1 testing failed:', error);
    
    if (error instanceof EnhancedError) {
      return NextResponse.json(
        createErrorResponse(error.code, { duration: Date.now() - startTime }),
        { status: error.httpStatus }
      );
    }

    return NextResponse.json(
      createErrorResponse('SYS_INTERNAL_ERROR', { 
        error: error instanceof Error ? error.message : 'Unknown error',
        duration: Date.now() - startTime 
      }),
      { status: 500 }
    );
  }
}

/**
 * Test token management functionality
 */
async function testTokenManagement(userId: string) {
  const startTime = Date.now();
  const results = {
    success: false,
    message: '',
    tests: {} as any,
    duration: 0
  };

  try {
    // Test 1: Check if token refresh endpoint exists
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_APP_URL}/api/oauth/instagram/refresh-token`, {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' }
      });
      
      results.tests.refreshEndpointExists = {
        success: response.status !== 404,
        message: response.status !== 404 ? 'Token refresh endpoint exists' : 'Token refresh endpoint not found'
      };
    } catch (error) {
      results.tests.refreshEndpointExists = {
        success: false,
        message: 'Failed to test token refresh endpoint'
      };
    }

    // Test 2: Check token manager functionality
    try {
      const { tokenManager } = await import('@/lib/social/token-manager');
      
      // Test getting accounts needing refresh
      const accountsNeedingRefresh = await tokenManager.getAccountsNeedingRefresh(userId);
      
      results.tests.tokenManagerFunctionality = {
        success: true,
        message: `Token manager working, found ${accountsNeedingRefresh.length} accounts needing refresh`,
        accountsCount: accountsNeedingRefresh.length
      };
    } catch (error) {
      results.tests.tokenManagerFunctionality = {
        success: false,
        message: error instanceof Error ? error.message : 'Token manager test failed'
      };
    }

    // Test 3: Check database schema for token fields
    try {
      const supabase = createServiceRoleClient();

      const { data: accounts, error } = await supabase
        .from('social_accounts')
        .select('id, expires_at, connection_status, last_validated_at')
        .limit(1);

      results.tests.databaseSchema = {
        success: !error,
        message: error ? `Database schema issue: ${error.message}` : 'Database schema supports token management'
      };
    } catch (error) {
      results.tests.databaseSchema = {
        success: false,
        message: 'Failed to test database schema'
      };
    }

    // Calculate overall success
    const testCount = Object.keys(results.tests).length;
    const passedCount = Object.values(results.tests).filter((test: any) => test.success).length;
    
    results.success = passedCount === testCount;
    results.message = `Token management: ${passedCount}/${testCount} tests passed`;
    results.duration = Date.now() - startTime;

    return results;
  } catch (error) {
    results.success = false;
    results.message = error instanceof Error ? error.message : 'Token management test failed';
    results.duration = Date.now() - startTime;
    return results;
  }
}

/**
 * Test health monitoring functionality
 */
async function testHealthMonitoring() {
  const startTime = Date.now();
  const results = {
    success: false,
    message: '',
    tests: {} as any,
    duration: 0
  };

  try {
    // Test health endpoint
    const response = await fetch(`${process.env.NEXT_PUBLIC_APP_URL}/api/health`);
    const healthData = await response.json();

    results.tests.healthEndpoint = {
      success: response.ok,
      message: response.ok ? 'Health endpoint working' : 'Health endpoint failed',
      status: healthData.status,
      components: Object.keys(healthData.components || {})
    };

    // Test detailed health check
    const detailedResponse = await fetch(`${process.env.NEXT_PUBLIC_APP_URL}/api/health?detailed=true`);
    const detailedData = await detailedResponse.json();

    results.tests.detailedHealth = {
      success: detailedResponse.ok,
      message: detailedResponse.ok ? 'Detailed health check working' : 'Detailed health check failed',
      hasTokens: !!detailedData.components?.tokens,
      hasQueue: !!detailedData.components?.queue
    };

    const testCount = Object.keys(results.tests).length;
    const passedCount = Object.values(results.tests).filter((test: any) => test.success).length;
    
    results.success = passedCount === testCount;
    results.message = `Health monitoring: ${passedCount}/${testCount} tests passed`;
    results.duration = Date.now() - startTime;

    return results;
  } catch (error) {
    results.success = false;
    results.message = error instanceof Error ? error.message : 'Health monitoring test failed';
    results.duration = Date.now() - startTime;
    return results;
  }
}

/**
 * Test metrics collection functionality
 */
async function testMetricsCollection(userId: string) {
  const startTime = Date.now();
  const results = {
    success: false,
    message: '',
    tests: {} as any,
    duration: 0
  };

  try {
    // Test metrics endpoint
    const response = await fetch(`${process.env.NEXT_PUBLIC_APP_URL}/api/metrics?userId=${userId}`);
    const metricsData = await response.json();

    results.tests.metricsEndpoint = {
      success: response.ok,
      message: response.ok ? 'Metrics endpoint working' : 'Metrics endpoint failed',
      hasSystemMetrics: !!metricsData.system,
      hasPostMetrics: !!metricsData.posts,
      hasPublishingMetrics: !!metricsData.publishing
    };

    // Test detailed metrics
    const detailedResponse = await fetch(`${process.env.NEXT_PUBLIC_APP_URL}/api/metrics?detailed=true&userId=${userId}`);
    const detailedData = await detailedResponse.json();

    results.tests.detailedMetrics = {
      success: detailedResponse.ok,
      message: detailedResponse.ok ? 'Detailed metrics working' : 'Detailed metrics failed',
      hasDetailedData: !!detailedData.detailed
    };

    const testCount = Object.keys(results.tests).length;
    const passedCount = Object.values(results.tests).filter((test: any) => test.success).length;
    
    results.success = passedCount === testCount;
    results.message = `Metrics collection: ${passedCount}/${testCount} tests passed`;
    results.duration = Date.now() - startTime;

    return results;
  } catch (error) {
    results.success = false;
    results.message = error instanceof Error ? error.message : 'Metrics collection test failed';
    results.duration = Date.now() - startTime;
    return results;
  }
}

/**
 * Test logging system functionality
 */
async function testLoggingSystem(userId: string) {
  const startTime = Date.now();
  const results = {
    success: false,
    message: '',
    tests: {} as any,
    duration: 0
  };

  try {
    // Test logger import and basic functionality
    await logger.info('Phase 1 test log entry', { userId, testType: 'logging' });
    await logger.error('Phase 1 test error log', new Error('Test error'), { userId });

    results.tests.loggerFunctionality = {
      success: true,
      message: 'Logger functionality working'
    };

    // Test Arabic error messages
    const { arabicErrors } = await import('@/lib/logging/logger');
    
    results.tests.arabicErrors = {
      success: Object.keys(arabicErrors).length > 0,
      message: `Arabic error messages available: ${Object.keys(arabicErrors).length} messages`
    };

    const testCount = Object.keys(results.tests).length;
    const passedCount = Object.values(results.tests).filter((test: any) => test.success).length;
    
    results.success = passedCount === testCount;
    results.message = `Logging system: ${passedCount}/${testCount} tests passed`;
    results.duration = Date.now() - startTime;

    return results;
  } catch (error) {
    results.success = false;
    results.message = error instanceof Error ? error.message : 'Logging system test failed';
    results.duration = Date.now() - startTime;
    return results;
  }
}

/**
 * Test error handling functionality
 */
async function testErrorHandling() {
  const startTime = Date.now();
  const results = {
    success: false,
    message: '',
    tests: {} as any,
    duration: 0
  };

  try {
    // Test error codes import
    const { ERROR_CODES, createErrorResponse, EnhancedError } = await import('@/lib/errors/error-codes');
    
    results.tests.errorCodesAvailable = {
      success: Object.keys(ERROR_CODES).length > 0,
      message: `Error codes available: ${Object.keys(ERROR_CODES).length} codes`
    };

    // Test enhanced error creation
    const testError = new EnhancedError('AUTH_REQUIRED');
    
    results.tests.enhancedErrorCreation = {
      success: testError.arabicMessage.includes('المصادقة'),
      message: 'Enhanced error with Arabic messages working'
    };

    // Test error response creation
    const errorResponse = createErrorResponse('PUB_MEDIA_REQUIRED');
    
    results.tests.errorResponseCreation = {
      success: errorResponse.error.arabicMessage.includes('انستغرام'),
      message: 'Error response creation working'
    };

    const testCount = Object.keys(results.tests).length;
    const passedCount = Object.values(results.tests).filter((test: any) => test.success).length;
    
    results.success = passedCount === testCount;
    results.message = `Error handling: ${passedCount}/${testCount} tests passed`;
    results.duration = Date.now() - startTime;

    return results;
  } catch (error) {
    results.success = false;
    results.message = error instanceof Error ? error.message : 'Error handling test failed';
    results.duration = Date.now() - startTime;
    return results;
  }
}
