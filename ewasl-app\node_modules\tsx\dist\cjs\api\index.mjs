import"../../get-pipe-path-BHW2eJdv.mjs";import{r as d}from"../../register-CuoYSLaL.mjs";import{t as j}from"../../require-BLYAcZms.mjs";import"module";import"node:path";import"../../temporary-directory-CwHp0_NW.mjs";import"node:os";import"node:module";import"node:url";import"get-tsconfig";import"node:fs";import"../../index-DGv_vkxZ.mjs";import"esbuild";import"node:crypto";import"../../client-BQVF1NaW.mjs";import"node:net";export{d as register,j as require};
