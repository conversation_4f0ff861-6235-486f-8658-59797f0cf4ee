{"name": "ewasl-app", "version": "0.1.0", "private": true, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "mcpServers": {"browsermcp": {"command": "npx", "args": ["@browsermcp/mcp@latest"]}}, "scripts": {"dev": "next dev --experimental-https --experimental-https-key ./cert.key --experimental-https-cert ./cert.crt -p 3003", "dev:http": "next dev -p 3003", "build": "next build", "postinstall": "prisma generate", "build:production": "npm ci --production=false && next build", "build:do": "npm ci --production=false --prefer-offline --no-audit && next build", "start": "next start -p ${PORT:-3000}", "postbuild": "echo 'Build completed successfully'", "init:monitoring": "node scripts/initialize-monitoring.js", "db:generate": "prisma generate", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "test:teams": "jest --config=jest.config.teams.js", "test:teams:watch": "jest --config=jest.config.teams.js --watch", "test:teams:coverage": "jest --config=jest.config.teams.js --coverage", "test:teams:ci": "jest --config=jest.config.teams.js --ci --coverage --watchAll=false", "test:e2e": "playwright test src/__tests__/teams/e2e.test.ts", "test:e2e:headed": "playwright test src/__tests__/teams/e2e.test.ts --headed", "test:performance": "jest --config=jest.config.teams.js --testPathPattern=performance", "test:all": "npm run test:teams && npm run test:e2e", "test:all:ci": "npm run test:teams:ci && npm run test:e2e", "analyze": "cross-env ANALYZE=true npm run build", "analyze:server": "cross-env BUNDLE_ANALYZE=server npm run build", "analyze:browser": "cross-env BUNDLE_ANALYZE=browser npm run build", "db:push": "echo 'Using Supabase - database managed via Supabase Dashboard'", "db:seed": "echo 'Using Supabase - seeding managed via Supabase'", "db:setup": "echo 'Using Supabase - setup managed via Supabase Dashboard'", "test:enhanced-integration": "tsx src/scripts/test-enhanced-integrations.ts", "test:enhanced-integration:watch": "tsx watch src/scripts/test-enhanced-integrations.ts", "db:enhanced-schema": "psql $DATABASE_URL -f src/lib/database/enhanced-integration-schema.sql", "deploy": "echo 'Deployment triggered at 2025-06-09T13:45:00Z'", "check:monitoring": "tsx scripts/check-monitoring-status.ts", "generate-certs": "node scripts/generate-ssl-certs.js || powershell -ExecutionPolicy Bypass -File scripts/generate-ssl-certs.ps1", "test:https": "node scripts/test-https-setup.js", "test:facebook": "node scripts/test-facebook-config.js", "test:oauth": "node scripts/test-oauth-flow.js", "test:publishing": "node scripts/test-post-publishing.js", "test:facebook-integration": "npm run test:https && npm run test:facebook && npm run test:oauth && npm run test:publishing", "setup:facebook": "npm run generate-certs && npm run test:https && npm run test:facebook"}, "dependencies": {"@auth/supabase-adapter": "^1.10.0", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.0.1", "@playwright/test": "^1.52.0", "@prisma/client": "^6.9.0", "prisma": "^6.9.0", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-tooltip": "^1.2.7", "@stripe/stripe-js": "^7.3.1", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.50.0", "@tiptap/extension-character-count": "^2.25.1", "@tiptap/extension-link": "^2.25.0", "@tiptap/extension-mention": "^2.25.0", "@tiptap/extension-placeholder": "^2.25.1", "@tiptap/pm": "^2.25.0", "@tiptap/react": "^2.25.1", "@tiptap/starter-kit": "^2.25.1", "@types/react-big-calendar": "^1.16.1", "@types/sharp": "^0.31.1", "@types/uuid": "^10.0.0", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "dotenv": "^16.6.1", "lucide-react": "^0.511.0", "moment": "^2.30.1", "next": "^15.3.2", "next-auth": "^4.24.11", "node-fetch": "^2.7.0", "openai": "^4.103.0", "pg": "^8.16.0", "postcss": "^8.5.6", "react": "^18.2.0", "react-big-calendar": "^1.19.4", "react-day-picker": "^9.7.0", "react-dom": "^18.2.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.56.4", "react-icons": "^5.5.0", "recharts": "^2.15.4", "sharp": "^0.33.5", "sonner": "^1.7.4", "stripe": "^18.2.0", "tailwind-merge": "^3.3.0", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "web-vitals": "^4.2.4", "zod": "^3.25.28"}, "devDependencies": {"@eslint/eslintrc": "^3", "@next/bundle-analyzer": "^14.2.29", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^14.3.1", "@testing-library/user-event": "^14.6.1", "@types/bcryptjs": "^2.4.6", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/stripe": "^8.0.416", "autoprefixer": "^10.4.21", "cross-env": "^7.0.3", "jest": "^29.7.0", "playwright": "^1.52.0", "tsx": "^4.19.4", "typescript": "^5"}}