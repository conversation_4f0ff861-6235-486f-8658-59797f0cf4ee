(()=>{var e={};e.id=9061,e.ids=[9061],e.modules={43:(e,t,s)=>{"use strict";s.d(t,{jH:()=>n});var r=s(43210);s(60687);var a=r.createContext(void 0);function n(e){let t=r.useContext(a);return e||t||"ltr"}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11437:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},11997:e=>{"use strict";e.exports=require("punycode")},12720:(e,t,s)=>{"use strict";s.d(t,{eu:()=>w,q5:()=>k,BK:()=>A});var r=s(60687),a=s(43210),n=s(11273),i=s(13495),l=s(66156),c=s(14163),o=s(57379);function d(){return()=>{}}var u="Avatar",[m,x]=(0,n.A)(u),[f,h]=m(u),p=a.forwardRef((e,t)=>{let{__scopeAvatar:s,...n}=e,[i,l]=a.useState("idle");return(0,r.jsx)(f,{scope:s,imageLoadingStatus:i,onImageLoadingStatusChange:l,children:(0,r.jsx)(c.sG.span,{...n,ref:t})})});p.displayName=u;var g="AvatarImage",v=a.forwardRef((e,t)=>{let{__scopeAvatar:s,src:n,onLoadingStatusChange:u=()=>{},...m}=e,x=h(g,s),f=function(e,{referrerPolicy:t,crossOrigin:s}){let r=(0,o.useSyncExternalStore)(d,()=>!0,()=>!1),n=a.useRef(null),i=r?(n.current||(n.current=new window.Image),n.current):null,[c,u]=a.useState(()=>y(i,e));return(0,l.N)(()=>{u(y(i,e))},[i,e]),(0,l.N)(()=>{let e=e=>()=>{u(e)};if(!i)return;let r=e("loaded"),a=e("error");return i.addEventListener("load",r),i.addEventListener("error",a),t&&(i.referrerPolicy=t),"string"==typeof s&&(i.crossOrigin=s),()=>{i.removeEventListener("load",r),i.removeEventListener("error",a)}},[i,s,t]),c}(n,m),p=(0,i.c)(e=>{u(e),x.onImageLoadingStatusChange(e)});return(0,l.N)(()=>{"idle"!==f&&p(f)},[f,p]),"loaded"===f?(0,r.jsx)(c.sG.img,{...m,ref:t,src:n}):null});v.displayName=g;var j="AvatarFallback",b=a.forwardRef((e,t)=>{let{__scopeAvatar:s,delayMs:n,...i}=e,l=h(j,s),[o,d]=a.useState(void 0===n);return a.useEffect(()=>{if(void 0!==n){let e=window.setTimeout(()=>d(!0),n);return()=>window.clearTimeout(e)}},[n]),o&&"loaded"!==l.imageLoadingStatus?(0,r.jsx)(c.sG.span,{...i,ref:t}):null});function y(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}b.displayName=j;var N=s(4780);let w=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)(p,{ref:s,className:(0,N.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",e),...t}));w.displayName=p.displayName;let A=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)(v,{ref:s,className:(0,N.cn)("aspect-square h-full w-full",e),...t}));A.displayName=v.displayName;let k=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)(b,{ref:s,className:(0,N.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted",e),...t}));k.displayName=b.displayName},16189:(e,t,s)=>{"use strict";var r=s(65773);s.o(r,"useParams")&&s.d(t,{useParams:function(){return r.useParams}}),s.o(r,"usePathname")&&s.d(t,{usePathname:function(){return r.usePathname}}),s.o(r,"useRouter")&&s.d(t,{useRouter:function(){return r.useRouter}}),s.o(r,"useSearchParams")&&s.d(t,{useSearchParams:function(){return r.useSearchParams}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19526:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("facebook",[["path",{d:"M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z",key:"1jg4f8"}]])},23562:(e,t,s)=>{"use strict";s.d(t,{k:()=>i});var r=s(60687);s(43210);var a=s(25177),n=s(4780);function i({className:e,value:t,...s}){return(0,r.jsx)(a.bL,{"data-slot":"progress",className:(0,n.cn)("bg-primary/20 relative h-2 w-full overflow-hidden rounded-full",e),...s,children:(0,r.jsx)(a.C1,{"data-slot":"progress-indicator",className:"bg-primary h-full w-full flex-1 transition-all",style:{transform:`translateX(-${100-(t||0)}%)`}})})}},25177:(e,t,s)=>{"use strict";s.d(t,{C1:()=>y,bL:()=>b});var r=s(43210),a=s(11273),n=s(14163),i=s(60687),l="Progress",[c,o]=(0,a.A)(l),[d,u]=c(l),m=r.forwardRef((e,t)=>{var s,r;let{__scopeProgress:a,value:l=null,max:c,getValueLabel:o=h,...u}=e;(c||0===c)&&!v(c)&&console.error((s=`${c}`,`Invalid prop \`max\` of value \`${s}\` supplied to \`Progress\`. Only numbers greater than 0 are valid max values. Defaulting to \`100\`.`));let m=v(c)?c:100;null===l||j(l,m)||console.error((r=`${l}`,`Invalid prop \`value\` of value \`${r}\` supplied to \`Progress\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or 100 if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`));let x=j(l,m)?l:null,f=g(x)?o(x,m):void 0;return(0,i.jsx)(d,{scope:a,value:x,max:m,children:(0,i.jsx)(n.sG.div,{"aria-valuemax":m,"aria-valuemin":0,"aria-valuenow":g(x)?x:void 0,"aria-valuetext":f,role:"progressbar","data-state":p(x,m),"data-value":x??void 0,"data-max":m,...u,ref:t})})});m.displayName=l;var x="ProgressIndicator",f=r.forwardRef((e,t)=>{let{__scopeProgress:s,...r}=e,a=u(x,s);return(0,i.jsx)(n.sG.div,{"data-state":p(a.value,a.max),"data-value":a.value??void 0,"data-max":a.max,...r,ref:t})});function h(e,t){return`${Math.round(e/t*100)}%`}function p(e,t){return null==e?"indeterminate":e===t?"complete":"loading"}function g(e){return"number"==typeof e}function v(e){return g(e)&&!isNaN(e)&&e>0}function j(e,t){return g(e)&&!isNaN(e)&&e<=t&&e>=0}f.displayName=x;var b=m,y=f},25334:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},25541:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29523:(e,t,s)=>{"use strict";s.d(t,{$:()=>c,r:()=>l});var r=s(60687);s(43210);var a=s(8730),n=s(24224),i=s(4780);let l=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function c({className:e,variant:t,size:s,asChild:n=!1,...c}){let o=n?a.DX:"button";return(0,r.jsx)(o,{"data-slot":"button",className:(0,i.cn)(l({variant:t,size:s,className:e})),...c})}},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},40228:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},41312:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},44493:(e,t,s)=>{"use strict";s.d(t,{BT:()=>o,Wu:()=>d,ZB:()=>c,Zp:()=>i,aR:()=>l,wL:()=>u});var r=s(60687),a=s(43210),n=s(4780);let i=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));i.displayName="Card";let l=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",e),...t}));l.displayName="CardHeader";let c=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));c.displayName="CardTitle";let o=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,n.cn)("text-sm text-muted-foreground",e),...t}));o.displayName="CardDescription";let d=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,n.cn)("p-6 pt-0",e),...t}));d.displayName="CardContent";let u=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,n.cn)("flex items-center p-6 pt-0",e),...t}));u.displayName="CardFooter"},45583:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},53332:(e,t,s)=>{"use strict";var r=s(43210),a="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},n=r.useState,i=r.useEffect,l=r.useLayoutEffect,c=r.useDebugValue;function o(e){var t=e.getSnapshot;e=e.value;try{var s=t();return!a(e,s)}catch(e){return!0}}var d="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var s=t(),r=n({inst:{value:s,getSnapshot:t}}),a=r[0].inst,d=r[1];return l(function(){a.value=s,a.getSnapshot=t,o(a)&&d({inst:a})},[e,s,t]),i(function(){return o(a)&&d({inst:a}),e(function(){o(a)&&d({inst:a})})},[e]),c(s),s};t.useSyncExternalStore=void 0!==r.useSyncExternalStore?r.useSyncExternalStore:d},53411:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},55146:(e,t,s)=>{"use strict";s.d(t,{B8:()=>E,UC:()=>$,bL:()=>S,l9:()=>P});var r=s(43210),a=s(70569),n=s(11273),i=s(72942),l=s(46059),c=s(14163),o=s(43),d=s(65551),u=s(96963),m=s(60687),x="Tabs",[f,h]=(0,n.A)(x,[i.RG]),p=(0,i.RG)(),[g,v]=f(x),j=r.forwardRef((e,t)=>{let{__scopeTabs:s,value:r,onValueChange:a,defaultValue:n,orientation:i="horizontal",dir:l,activationMode:f="automatic",...h}=e,p=(0,o.jH)(l),[v,j]=(0,d.i)({prop:r,onChange:a,defaultProp:n??"",caller:x});return(0,m.jsx)(g,{scope:s,baseId:(0,u.B)(),value:v,onValueChange:j,orientation:i,dir:p,activationMode:f,children:(0,m.jsx)(c.sG.div,{dir:p,"data-orientation":i,...h,ref:t})})});j.displayName=x;var b="TabsList",y=r.forwardRef((e,t)=>{let{__scopeTabs:s,loop:r=!0,...a}=e,n=v(b,s),l=p(s);return(0,m.jsx)(i.bL,{asChild:!0,...l,orientation:n.orientation,dir:n.dir,loop:r,children:(0,m.jsx)(c.sG.div,{role:"tablist","aria-orientation":n.orientation,...a,ref:t})})});y.displayName=b;var N="TabsTrigger",w=r.forwardRef((e,t)=>{let{__scopeTabs:s,value:r,disabled:n=!1,...l}=e,o=v(N,s),d=p(s),u=C(o.baseId,r),x=R(o.baseId,r),f=r===o.value;return(0,m.jsx)(i.q7,{asChild:!0,...d,focusable:!n,active:f,children:(0,m.jsx)(c.sG.button,{type:"button",role:"tab","aria-selected":f,"aria-controls":x,"data-state":f?"active":"inactive","data-disabled":n?"":void 0,disabled:n,id:u,...l,ref:t,onMouseDown:(0,a.m)(e.onMouseDown,e=>{n||0!==e.button||!1!==e.ctrlKey?e.preventDefault():o.onValueChange(r)}),onKeyDown:(0,a.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&o.onValueChange(r)}),onFocus:(0,a.m)(e.onFocus,()=>{let e="manual"!==o.activationMode;f||n||!e||o.onValueChange(r)})})})});w.displayName=N;var A="TabsContent",k=r.forwardRef((e,t)=>{let{__scopeTabs:s,value:a,forceMount:n,children:i,...o}=e,d=v(A,s),u=C(d.baseId,a),x=R(d.baseId,a),f=a===d.value,h=r.useRef(f);return r.useEffect(()=>{let e=requestAnimationFrame(()=>h.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,m.jsx)(l.C,{present:n||f,children:({present:s})=>(0,m.jsx)(c.sG.div,{"data-state":f?"active":"inactive","data-orientation":d.orientation,role:"tabpanel","aria-labelledby":u,hidden:!s,id:x,tabIndex:0,...o,ref:t,style:{...e.style,animationDuration:h.current?"0s":void 0},children:s&&i})})});function C(e,t){return`${e}-trigger-${t}`}function R(e,t){return`${e}-content-${t}`}k.displayName=A;var S=j,E=y,P=w,$=k},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57379:(e,t,s)=>{"use strict";e.exports=s(53332)},58559:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66232:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("instagram",[["rect",{width:"20",height:"20",x:"2",y:"2",rx:"5",ry:"5",key:"2e1cvw"}],["path",{d:"M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z",key:"9exkf1"}],["line",{x1:"17.5",x2:"17.51",y1:"6.5",y2:"6.5",key:"r4j83e"}]])},70334:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(31658);let a=async e=>[{type:"image/x-icon",sizes:"48x48",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},71771:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ewasl\\\\eWasl.com_Cursor_Digital_Ocean\\\\ewasl-app\\\\src\\\\app\\\\account-selection\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Ewasl\\eWasl.com_Cursor_Digital_Ocean\\ewasl-app\\src\\app\\account-selection\\page.tsx","default")},72565:(e,t,s)=>{Promise.resolve().then(s.bind(s,98792))},72575:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]])},72942:(e,t,s)=>{"use strict";s.d(t,{RG:()=>y,bL:()=>P,q7:()=>$});var r=s(43210),a=s(70569),n=s(9510),i=s(98599),l=s(11273),c=s(96963),o=s(14163),d=s(13495),u=s(65551),m=s(43),x=s(60687),f="rovingFocusGroup.onEntryFocus",h={bubbles:!1,cancelable:!0},p="RovingFocusGroup",[g,v,j]=(0,n.N)(p),[b,y]=(0,l.A)(p,[j]),[N,w]=b(p),A=r.forwardRef((e,t)=>(0,x.jsx)(g.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,x.jsx)(g.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,x.jsx)(k,{...e,ref:t})})}));A.displayName=p;var k=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:s,orientation:n,loop:l=!1,dir:c,currentTabStopId:g,defaultCurrentTabStopId:j,onCurrentTabStopIdChange:b,onEntryFocus:y,preventScrollOnEntryFocus:w=!1,...A}=e,k=r.useRef(null),C=(0,i.s)(t,k),R=(0,m.jH)(c),[S,P]=(0,u.i)({prop:g,defaultProp:j??null,onChange:b,caller:p}),[$,M]=r.useState(!1),q=(0,d.c)(y),T=v(s),I=r.useRef(!1),[F,D]=r.useState(0);return r.useEffect(()=>{let e=k.current;if(e)return e.addEventListener(f,q),()=>e.removeEventListener(f,q)},[q]),(0,x.jsx)(N,{scope:s,orientation:n,dir:R,loop:l,currentTabStopId:S,onItemFocus:r.useCallback(e=>P(e),[P]),onItemShiftTab:r.useCallback(()=>M(!0),[]),onFocusableItemAdd:r.useCallback(()=>D(e=>e+1),[]),onFocusableItemRemove:r.useCallback(()=>D(e=>e-1),[]),children:(0,x.jsx)(o.sG.div,{tabIndex:$||0===F?-1:0,"data-orientation":n,...A,ref:C,style:{outline:"none",...e.style},onMouseDown:(0,a.m)(e.onMouseDown,()=>{I.current=!0}),onFocus:(0,a.m)(e.onFocus,e=>{let t=!I.current;if(e.target===e.currentTarget&&t&&!$){let t=new CustomEvent(f,h);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=T().filter(e=>e.focusable);E([e.find(e=>e.active),e.find(e=>e.id===S),...e].filter(Boolean).map(e=>e.ref.current),w)}}I.current=!1}),onBlur:(0,a.m)(e.onBlur,()=>M(!1))})})}),C="RovingFocusGroupItem",R=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:s,focusable:n=!0,active:i=!1,tabStopId:l,children:d,...u}=e,m=(0,c.B)(),f=l||m,h=w(C,s),p=h.currentTabStopId===f,j=v(s),{onFocusableItemAdd:b,onFocusableItemRemove:y,currentTabStopId:N}=h;return r.useEffect(()=>{if(n)return b(),()=>y()},[n,b,y]),(0,x.jsx)(g.ItemSlot,{scope:s,id:f,focusable:n,active:i,children:(0,x.jsx)(o.sG.span,{tabIndex:p?0:-1,"data-orientation":h.orientation,...u,ref:t,onMouseDown:(0,a.m)(e.onMouseDown,e=>{n?h.onItemFocus(f):e.preventDefault()}),onFocus:(0,a.m)(e.onFocus,()=>h.onItemFocus(f)),onKeyDown:(0,a.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void h.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,s){var r;let a=(r=e.key,"rtl"!==s?r:"ArrowLeft"===r?"ArrowRight":"ArrowRight"===r?"ArrowLeft":r);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(a))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(a)))return S[a]}(e,h.orientation,h.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let s=j().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)s.reverse();else if("prev"===t||"next"===t){"prev"===t&&s.reverse();let r=s.indexOf(e.currentTarget);s=h.loop?function(e,t){return e.map((s,r)=>e[(t+r)%e.length])}(s,r+1):s.slice(r+1)}setTimeout(()=>E(s))}}),children:"function"==typeof d?d({isCurrentTabStop:p,hasTabStop:null!=N}):d})})});R.displayName=C;var S={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function E(e,t=!1){let s=document.activeElement;for(let r of e)if(r===s||(r.focus({preventScroll:t}),document.activeElement!==s))return}var P=A,$=R},74075:e=>{"use strict";e.exports=require("zlib")},77062:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>o});var r=s(65239),a=s(48088),n=s(88170),i=s.n(n),l=s(30893),c={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);s.d(t,c);let o={children:["",{children:["account-selection",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,71771)),"C:\\Users\\<USER>\\Desktop\\Ewasl\\eWasl.com_Cursor_Digital_Ocean\\ewasl-app\\src\\app\\account-selection\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,45399)),"C:\\Users\\<USER>\\Desktop\\Ewasl\\eWasl.com_Cursor_Digital_Ocean\\ewasl-app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Desktop\\Ewasl\\eWasl.com_Cursor_Digital_Ocean\\ewasl-app\\src\\app\\account-selection\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/account-selection/page",pathname:"/account-selection",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},78122:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},82293:(e,t,s)=>{Promise.resolve().then(s.bind(s,71771))},84027:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},85763:(e,t,s)=>{"use strict";s.d(t,{Xi:()=>o,av:()=>d,j7:()=>c,tU:()=>l});var r=s(60687),a=s(43210),n=s(55146),i=s(4780);let l=n.bL,c=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)(n.B8,{ref:s,className:(0,i.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...t}));c.displayName=n.B8.displayName;let o=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)(n.l9,{ref:s,className:(0,i.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...t}));o.displayName=n.l9.displayName;let d=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)(n.UC,{ref:s,className:(0,i.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...t}));d.displayName=n.UC.displayName},91645:e=>{"use strict";e.exports=require("net")},91821:(e,t,s)=>{"use strict";s.d(t,{Fc:()=>c,TN:()=>d,XL:()=>o});var r=s(60687),a=s(43210),n=s(24224),i=s(4780);let l=(0,n.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),c=a.forwardRef(({className:e,variant:t,...s},a)=>(0,r.jsx)("div",{ref:a,role:"alert",className:(0,i.cn)(l({variant:t}),e),...s}));c.displayName="Alert";let o=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("h5",{ref:s,className:(0,i.cn)("mb-1 font-medium leading-none tracking-tight",e),...t}));o.displayName="AlertTitle";let d=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,i.cn)("text-sm [&_p]:leading-relaxed",e),...t}));d.displayName="AlertDescription"},93613:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},94735:e=>{"use strict";e.exports=require("events")},96834:(e,t,s)=>{"use strict";s.d(t,{E:()=>l});var r=s(60687);s(43210);var a=s(24224),n=s(4780);let i=(0,a.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,...s}){return(0,r.jsx)("div",{className:(0,n.cn)(i({variant:t}),e),...s})}},96963:(e,t,s)=>{"use strict";s.d(t,{B:()=>c});var r,a=s(43210),n=s(66156),i=(r||(r=s.t(a,2)))[" useId ".trim().toString()]||(()=>void 0),l=0;function c(e){let[t,s]=a.useState(i());return(0,n.N)(()=>{e||s(e=>e??String(l++))},[e]),e||(t?`radix-${t}`:"")}},98792:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>_});var r=s(60687),a=s(43210),n=s(44493),i=s(29523),l=s(96834),c=s(91821),o=s(78122),d=s(84027),u=s(5336),m=s(70334),x=s(45583),f=s(93613),h=s(11437),p=s(62688);let g=(0,p.A)("building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]]);var v=s(41312),j=s(52581),b=s(16189),y=s(23562),N=s(58559),w=s(25541),A=s(40228),k=s(53411),C=s(85763),R=s(19526),S=s(98876),E=s(66232),P=s(72575),$=s(25334),M=s(12720);let q=(0,p.A)("badge-check",[["path",{d:"M3.85 8.62a4 4 0 0 1 4.78-4.77 4 4 0 0 1 6.74 0 4 4 0 0 1 4.78 4.78 4 4 0 0 1 0 6.74 4 4 0 0 1-4.77 4.78 4 4 0 0 1-6.75 0 4 4 0 0 1-4.78-4.77 4 4 0 0 1 0-6.76Z",key:"3c2336"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]]),T=(0,p.A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]]);function I({account:e,isSelected:t,onSelect:s,className:a=""}){let n=e=>e>=1e6?`${(e/1e6).toFixed(1)}M`:e>=1e3?`${(e/1e3).toFixed(1)}K`:e.toString(),i=e=>{let t=e.metadata?.headquarters;if(!t)return null;let s=[];return t.city&&s.push(t.city),t.country&&s.push(t.country),s.length>0?s.join(", "):null};return(0,r.jsx)("div",{className:`p-4 border rounded-lg cursor-pointer transition-all hover:shadow-md ${t?"border-blue-500 bg-blue-50 dark:bg-blue-950":"border-gray-200 hover:border-gray-300"} ${a}`,onClick:()=>s(e.id),children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:`w-2 h-12 rounded-full ${(e=>{switch(e.toUpperCase()){case"FACEBOOK":return"bg-blue-500";case"LINKEDIN":return"bg-blue-600";case"INSTAGRAM":return"bg-pink-500";case"TWITTER":return"bg-blue-400";default:return"bg-gray-500"}})(e.platform)}`}),(0,r.jsxs)(M.eu,{className:"w-12 h-12",children:[(0,r.jsx)(M.BK,{src:e.profilePictureUrl,alt:e.name}),(0,r.jsx)(M.q5,{children:"COMPANY"===e.type?(0,r.jsx)(g,{className:"w-6 h-6"}):e.name.substring(0,2).toUpperCase()})]}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("h3",{className:"font-semibold text-sm",children:e.name}),e.metadata?.isVerified&&(0,r.jsx)(q,{className:"w-4 h-4 text-blue-500"}),t&&(0,r.jsx)(u.A,{className:"w-4 h-4 text-green-500"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-4 mt-1 flex-wrap",children:[(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(e=>{switch(e.toUpperCase()){case"FACEBOOK":return(0,r.jsx)(R.A,{className:"w-4 h-4 text-blue-600"});case"LINKEDIN":return(0,r.jsx)(S.A,{className:"w-4 h-4 text-blue-700"});case"INSTAGRAM":return(0,r.jsx)(E.A,{className:"w-4 h-4 text-pink-600"});case"TWITTER":return(0,r.jsx)(P.A,{className:"w-4 h-4 text-blue-400"});default:return(0,r.jsx)(g,{className:"w-4 h-4"})}})(e.platform),(0,r.jsx)("span",{className:"text-xs text-muted-foreground",children:((e,t)=>{switch(e){case"PAGE":return"صفحة";case"COMPANY":return"شركة";case"BUSINESS":return"حساب تجاري";case"PERSONAL":return"حساب شخصي";default:return e}})(e.type,e.platform)})]}),e.followerCount&&e.followerCount>0&&(0,r.jsxs)("div",{className:"flex items-center gap-1 text-xs text-muted-foreground",children:[(0,r.jsx)(v.A,{className:"w-3 h-3"}),n(e.followerCount)]}),e.category&&(0,r.jsx)(l.E,{variant:"secondary",className:"text-xs",children:e.category}),e.metadata?.employeeCount&&(0,r.jsxs)("div",{className:"flex items-center gap-1 text-xs text-muted-foreground",children:[(0,r.jsx)(g,{className:"w-3 h-3"}),e.metadata.employeeCount," موظف"]}),e.metadata?.fanCount&&(0,r.jsxs)("div",{className:"flex items-center gap-1 text-xs text-muted-foreground",children:[(0,r.jsx)(v.A,{className:"w-3 h-3"}),n(e.metadata.fanCount)," معجب"]}),i(e)&&(0,r.jsxs)("div",{className:"flex items-center gap-1 text-xs text-muted-foreground",children:[(0,r.jsx)(T,{className:"w-3 h-3"}),i(e)]}),e.website&&(0,r.jsxs)("div",{className:"flex items-center gap-1 text-xs text-muted-foreground",children:[(0,r.jsx)(h.A,{className:"w-3 h-3"}),"موقع إلكتروني"]})]}),e.metadata?.description&&(0,r.jsx)("p",{className:"text-xs text-muted-foreground mt-2 line-clamp-2",children:e.metadata.description}),e.metadata?.about&&(0,r.jsx)("p",{className:"text-xs text-muted-foreground mt-2 line-clamp-2",children:e.metadata.about})]})]}),t&&(0,r.jsx)(l.E,{variant:"default",className:"bg-green-500",children:"مختارة"})]})})}function F({userId:e,onAccountSelected:t,onConfigurationChange:s,className:c}){let[m,x]=(0,a.useState)("facebook"),[h,p]=(0,a.useState)({}),[v,b]=(0,a.useState)(!0),[y,N]=(0,a.useState)(!1),w={facebook:{name:"فيسبوك",icon:R.A,color:"text-blue-600",bgColor:"bg-blue-50",borderColor:"border-blue-200",description:"إدارة صفحات فيسبوك التجارية"},linkedin:{name:"لينكد إن",icon:S.A,color:"text-blue-700",bgColor:"bg-blue-50",borderColor:"border-blue-300",description:"إدارة شركات لينكد إن"},instagram:{name:"إنستغرام",icon:E.A,color:"text-pink-600",bgColor:"bg-pink-50",borderColor:"border-pink-200",description:"إدارة حسابات إنستغرام التجارية"},twitter:{name:"تويتر",icon:P.A,color:"text-blue-400",bgColor:"bg-blue-50",borderColor:"border-blue-100",description:"إدارة حسابات تويتر"}},A=async(t,r=!0)=>{p(e=>({...e,[t]:{...e[t],platform:t,isLoading:!0,error:void 0}}));try{let a=await fetch(`/api/social/business-accounts?platform=${t}&userId=${e}`),n=await a.json();if(!a.ok)throw Error(n.error||`Failed to load ${t} configuration`);let i={platform:t,isConnected:!n.requiresReconnection,isConfigured:n.isConfigured,hasBusinessAccounts:n.hasBusinessAccounts,selectedAccount:n.selectedAccount,businessAccounts:n.businessAccounts||[],requiresReconnection:n.requiresReconnection,missingPermissions:n.missingPermissions||[],isLoading:!1};if(p(e=>({...e,[t]:i})),s&&s(t,n),r&&n.isConfigured){let e=w[t];j.o.success(`تم تحميل إعدادات ${e?.name||t} بنجاح`)}}catch(e){if(console.error(`Error loading ${t} config:`,e),p(s=>({...s,[t]:{...s[t],platform:t,isConnected:!1,isConfigured:!1,hasBusinessAccounts:!1,businessAccounts:[],requiresReconnection:!0,missingPermissions:[],isLoading:!1,error:e.message}})),r){let e=w[t];j.o.error(`فشل في تحميل إعدادات ${e?.name||t}`)}}},k=async t=>{try{N(!0);let s=await fetch("/api/social/business-accounts/refresh",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({platform:t,userId:e})}),r=await s.json();if(!s.ok)throw Error(r.error||`Failed to refresh ${t}`);await A(t,!1);let a=w[t];j.o.success(`تم تحديث ${a?.name||t} بنجاح`)}catch(s){console.error(`Error refreshing ${t}:`,s);let e=w[t];j.o.error(`فشل في تحديث ${e?.name||t}`)}finally{N(!1)}},M=async(s,r)=>{try{let a=await fetch("/api/social/business-accounts/select",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({platform:s,userId:e,businessAccountId:r})}),n=await a.json();if(!a.ok)throw Error(n.error||`Failed to select ${s} account`);p(e=>({...e,[s]:{...e[s],selectedAccount:e[s].businessAccounts.find(e=>e.id===r),businessAccounts:e[s].businessAccounts.map(e=>({...e,isSelected:e.id===r}))}})),t?.(s,r);let i=w[s];j.o.success(`تم اختيار حساب ${i?.name||s} بنجاح`)}catch(t){console.error(`Error selecting ${s} account:`,t);let e=w[s];j.o.error(`فشل في اختيار حساب ${e?.name||s}`)}},q=async e=>{try{let t=await fetch("/api/social/connect",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({platform:e})}),s=await t.json();if(!t.ok)throw Error(s.error||`Failed to connect ${e}`);s.authUrl&&(window.location.href=s.authUrl)}catch(s){console.error(`Error connecting ${e}:`,s);let t=w[e];j.o.error(`فشل في ربط ${t?.name||e}`)}},T=e=>{let t=h[e];return t?t.isLoading?(0,r.jsx)(l.E,{variant:"secondary",children:"جاري التحميل..."}):t.error?(0,r.jsx)(l.E,{variant:"destructive",children:"خطأ"}):t.isConnected?t.hasBusinessAccounts?t.selectedAccount?(0,r.jsx)(l.E,{className:"bg-green-500",children:"مكون"}):(0,r.jsx)(l.E,{className:"bg-yellow-500",children:"لم يتم الاختيار"}):(0,r.jsx)(l.E,{variant:"secondary",children:"لا توجد حسابات"}):(0,r.jsx)(l.E,{variant:"destructive",children:"غير متصل"}):(0,r.jsx)(l.E,{variant:"secondary",children:"غير محمل"})},F=e=>{let t=h[e];return t?t.isLoading?(0,r.jsx)(o.A,{className:"w-4 h-4 text-blue-500 animate-spin"}):t.error||!t.isConnected?(0,r.jsx)(f.A,{className:"w-4 h-4 text-red-500"}):t.selectedAccount?(0,r.jsx)(u.A,{className:"w-4 h-4 text-green-500"}):(0,r.jsx)(f.A,{className:"w-4 h-4 text-yellow-500"}):(0,r.jsx)(f.A,{className:"w-4 h-4 text-gray-400"})},D=e=>{let t=h[e],s=w[e];return t?t.error?(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)(f.A,{className:"w-12 h-12 text-red-500 mx-auto mb-4"}),(0,r.jsxs)("h3",{className:"text-lg font-semibold mb-2",children:["خطأ في تحميل ",s.name]}),(0,r.jsx)("p",{className:"text-muted-foreground mb-4",children:t.error}),(0,r.jsx)(i.$,{onClick:()=>A(e),variant:"outline",children:"إعادة المحاولة"})]}):t.isConnected?t.hasBusinessAccounts?(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold",children:[s.name," (",t.businessAccounts.length,")"]}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:s.description})]}),(0,r.jsx)(i.$,{onClick:()=>k(e),variant:"outline",size:"sm",disabled:y,children:y?(0,r.jsx)(o.A,{className:"w-4 h-4 animate-spin"}):(0,r.jsx)(o.A,{className:"w-4 h-4"})})]}),(0,r.jsx)("div",{className:"space-y-3",children:t.businessAccounts.map(s=>(0,r.jsx)(I,{account:s,isSelected:s.isSelected||t.selectedAccount?.id===s.id,onSelect:t=>M(e,t)},s.id))}),t.selectedAccount&&(0,r.jsx)("div",{className:"mt-4 p-3 bg-green-50 dark:bg-green-950 border border-green-200 rounded-lg",children:(0,r.jsxs)("p",{className:"text-sm text-green-700 dark:text-green-300",children:["✅ سيتم النشر على: ",t.selectedAccount.name]})})]}):(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)(g,{className:"w-12 h-12 text-yellow-500 mx-auto mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"لا توجد حسابات تجارية"}),(0,r.jsxs)("p",{className:"text-muted-foreground mb-4",children:["لم يتم العثور على حسابات تجارية قابلة للإدارة في ",s.name]}),(0,r.jsxs)("div",{className:"flex gap-2 justify-center",children:[(0,r.jsx)(i.$,{onClick:()=>k(e),variant:"outline",disabled:y,children:y?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(o.A,{className:"w-4 h-4 ml-2 animate-spin"}),"جاري التحديث..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(o.A,{className:"w-4 h-4 ml-2"}),"تحديث"]})}),(0,r.jsxs)(i.$,{onClick:()=>q(e),variant:"outline",children:[(0,r.jsx)($.A,{className:"w-4 h-4 ml-2"}),"إعادة الربط"]})]})]}):(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)(s.icon,{className:`w-12 h-12 ${s.color} mx-auto mb-4`}),(0,r.jsxs)("h3",{className:"text-lg font-semibold mb-2",children:["ربط حساب ",s.name]}),(0,r.jsx)("p",{className:"text-muted-foreground mb-4",children:s.description}),(0,r.jsxs)(i.$,{onClick:()=>q(e),className:"flex items-center gap-2",children:[(0,r.jsx)($.A,{className:"w-4 h-4"}),"ربط ",s.name]})]}):(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)(o.A,{className:"w-8 h-8 animate-spin text-muted-foreground mx-auto mb-4"}),(0,r.jsxs)("p",{className:"text-muted-foreground",children:["جاري تحميل إعدادات ",s.name,"..."]})]})};return v?(0,r.jsxs)(n.Zp,{className:c,children:[(0,r.jsx)(n.aR,{children:(0,r.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(d.A,{className:"w-6 h-6"}),"إعداد الحسابات التجارية"]})}),(0,r.jsx)(n.Wu,{children:(0,r.jsxs)("div",{className:"flex items-center justify-center py-8",children:[(0,r.jsx)(o.A,{className:"w-6 h-6 animate-spin text-muted-foreground"}),(0,r.jsx)("span",{className:"mr-2 text-muted-foreground",children:"جاري تحميل الإعدادات..."})]})})]}):(0,r.jsxs)(n.Zp,{className:c,children:[(0,r.jsx)(n.aR,{children:(0,r.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(d.A,{className:"w-6 h-6"}),"إعداد الحسابات التجارية"]})}),(0,r.jsxs)(n.Wu,{children:[(0,r.jsxs)(C.tU,{value:m,onValueChange:x,className:"w-full",children:[(0,r.jsx)(C.j7,{className:"grid w-full grid-cols-4",children:Object.entries(w).map(([e,t])=>(0,r.jsxs)(C.Xi,{value:e,className:"flex items-center gap-2",children:[F(e),(0,r.jsx)(t.icon,{className:`w-4 h-4 ${t.color}`}),(0,r.jsx)("span",{className:"hidden sm:inline",children:t.name})]},e))}),Object.keys(w).map(e=>(0,r.jsx)(C.av,{value:e,className:"space-y-4",children:D(e)},e))]}),(0,r.jsx)("div",{className:"mt-6 pt-4 border-t",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"text-sm text-muted-foreground",children:[Object.values(h).filter(e=>e.selectedAccount).length," من"," ",Object.keys(w).length," منصات مكونة"]}),(0,r.jsx)("div",{className:"flex gap-2",children:Object.entries(w).map(([e,t])=>(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)(t.icon,{className:`w-4 h-4 ${t.color}`}),T(e)]},e))})]})})]})]})}function D({userId:e,className:t}){let[s,c]=(0,a.useState)({totalPlatforms:4,connectedPlatforms:0,configuredPlatforms:0,totalBusinessAccounts:0,selectedAccounts:0,lastUpdated:new Date().toISOString()}),[m,x]=(0,a.useState)([]),[f,p]=(0,a.useState)(!0),[b,C]=(0,a.useState)(!1),R=async()=>{p(!0);try{let t=["facebook","linkedin","instagram","twitter"],s=t.map(async t=>{try{let s=await fetch(`/api/social/business-accounts?platform=${t}&userId=${e}`),r=await s.json();return{platform:t,name:E(t),isConnected:!r.requiresReconnection,isConfigured:r.isConfigured,businessAccountsCount:r.businessAccounts?.length||0,selectedAccount:r.selectedAccount?.name,followerCount:r.selectedAccount?.followerCount,lastActivity:r.lastUpdated}}catch(e){return{platform:t,name:E(t),isConnected:!1,isConfigured:!1,businessAccountsCount:0}}}),r=await Promise.all(s);x(r);let a={totalPlatforms:t.length,connectedPlatforms:r.filter(e=>e.isConnected).length,configuredPlatforms:r.filter(e=>e.isConfigured).length,totalBusinessAccounts:r.reduce((e,t)=>e+t.businessAccountsCount,0),selectedAccounts:r.filter(e=>e.selectedAccount).length,lastUpdated:new Date().toISOString()};c(a)}catch(e){console.error("Error loading dashboard data:",e),j.o.error("فشل في تحميل بيانات لوحة التحكم")}finally{p(!1)}},S=async()=>{C(!0);try{let t=["facebook","linkedin"].map(async t=>{try{if((await fetch("/api/social/business-accounts/refresh",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({platform:t,userId:e})})).ok)return{platform:t,success:!0};return{platform:t,success:!1}}catch(e){return{platform:t,success:!1}}}),s=(await Promise.all(t)).filter(e=>e.success).length;s>0?(j.o.success(`تم تحديث ${s} منصة بنجاح`),await R()):j.o.error("فشل في تحديث المنصات")}catch(e){console.error("Error refreshing platforms:",e),j.o.error("فشل في تحديث المنصات")}finally{C(!1)}},E=e=>({facebook:"فيسبوك",linkedin:"لينكد إن",instagram:"إنستغرام",twitter:"تويتر"})[e]||e;return f?(0,r.jsx)("div",{className:`space-y-6 ${t}`,children:(0,r.jsxs)(n.Zp,{children:[(0,r.jsx)(n.aR,{children:(0,r.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(d.A,{className:"w-6 h-6"}),"لوحة تحكم الحسابات التجارية"]})}),(0,r.jsx)(n.Wu,{children:(0,r.jsxs)("div",{className:"flex items-center justify-center py-8",children:[(0,r.jsx)(o.A,{className:"w-6 h-6 animate-spin text-muted-foreground"}),(0,r.jsx)("span",{className:"mr-2 text-muted-foreground",children:"جاري تحميل البيانات..."})]})})]})}):(0,r.jsxs)("div",{className:`space-y-6 ${t}`,children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold",children:"إدارة الحسابات التجارية"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"إدارة وتكوين حساباتك التجارية عبر جميع منصات التواصل الاجتماعي"})]}),(0,r.jsx)(i.$,{onClick:S,disabled:b,className:"flex items-center gap-2",children:b?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(o.A,{className:"w-4 h-4 animate-spin"}),"جاري التحديث..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(o.A,{className:"w-4 h-4"}),"تحديث الكل"]})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,r.jsx)(n.Zp,{children:(0,r.jsxs)(n.Wu,{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"المنصات المتصلة"}),(0,r.jsxs)("p",{className:"text-2xl font-bold",children:[s.connectedPlatforms,"/",s.totalPlatforms]})]}),(0,r.jsx)(h.A,{className:"w-8 h-8 text-blue-500"})]}),(0,r.jsx)(y.k,{value:s.connectedPlatforms/s.totalPlatforms*100,className:"mt-2"})]})}),(0,r.jsx)(n.Zp,{children:(0,r.jsxs)(n.Wu,{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"المنصات المكونة"}),(0,r.jsxs)("p",{className:"text-2xl font-bold",children:[s.configuredPlatforms,"/",s.totalPlatforms]})]}),(0,r.jsx)(d.A,{className:"w-8 h-8 text-green-500"})]}),(0,r.jsx)(y.k,{value:s.configuredPlatforms/s.totalPlatforms*100,className:"mt-2"})]})}),(0,r.jsx)(n.Zp,{children:(0,r.jsx)(n.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"الحسابات التجارية"}),(0,r.jsx)("p",{className:"text-2xl font-bold",children:s.totalBusinessAccounts})]}),(0,r.jsx)(g,{className:"w-8 h-8 text-purple-500"})]})})}),(0,r.jsx)(n.Zp,{children:(0,r.jsx)(n.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"الحسابات المختارة"}),(0,r.jsx)("p",{className:"text-2xl font-bold",children:s.selectedAccounts})]}),(0,r.jsx)(u.A,{className:"w-8 h-8 text-orange-500"})]})})})]}),(0,r.jsxs)(n.Zp,{children:[(0,r.jsx)(n.aR,{children:(0,r.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(N.A,{className:"w-5 h-5"}),"ملخص المنصات"]})}),(0,r.jsx)(n.Wu,{children:(0,r.jsx)("div",{className:"space-y-4",children:m.map(e=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:`w-3 h-3 rounded-full ${e.isConnected?"bg-green-500":"bg-red-500"}`}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-medium",children:e.name}),(0,r.jsxs)("p",{className:"text-sm text-muted-foreground",children:[e.businessAccountsCount," حساب تجاري",e.selectedAccount&&` • ${e.selectedAccount}`]})]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[e.followerCount&&(0,r.jsxs)(l.E,{variant:"outline",className:"flex items-center gap-1",children:[(0,r.jsx)(v.A,{className:"w-3 h-3"}),e.followerCount.toLocaleString()]}),(0,r.jsx)(l.E,{variant:e.isConfigured?"default":"secondary",children:e.isConfigured?"مكون":"غير مكون"})]})]},e.platform))})})]}),(0,r.jsx)(F,{userId:e,onConfigurationChange:(e,t)=>{x(s=>s.map(s=>s.platform===e?{...s,isConnected:!t.requiresReconnection,isConfigured:t.isConfigured,businessAccountsCount:t.businessAccounts.length,selectedAccount:t.selectedAccount?.name,followerCount:t.selectedAccount?.followerCount,lastActivity:t.lastUpdated}:s));let s=m.map(s=>s.platform===e?{...s,isConnected:!t.requiresReconnection,isConfigured:t.isConfigured,businessAccountsCount:t.businessAccounts.length,selectedAccount:t.selectedAccount?.name}:s);c({totalPlatforms:4,connectedPlatforms:s.filter(e=>e.isConnected).length,configuredPlatforms:s.filter(e=>e.isConfigured).length,totalBusinessAccounts:s.reduce((e,t)=>e+t.businessAccountsCount,0),selectedAccounts:s.filter(e=>e.selectedAccount).length,lastUpdated:new Date().toISOString()})},onAccountSelected:(e,t)=>{j.o.success(`تم اختيار حساب ${E(e)} بنجاح`)}}),(0,r.jsxs)(n.Zp,{children:[(0,r.jsx)(n.aR,{children:(0,r.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(w.A,{className:"w-5 h-5"}),"إجراءات سريعة"]})}),(0,r.jsx)(n.Wu,{children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)(i.$,{variant:"outline",className:"flex items-center gap-2 h-auto p-4",children:[(0,r.jsx)(A.A,{className:"w-5 h-5"}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsx)("div",{className:"font-medium",children:"جدولة منشور"}),(0,r.jsx)("div",{className:"text-sm text-muted-foreground",children:"إنشاء منشور جديد"})]})]}),(0,r.jsxs)(i.$,{variant:"outline",className:"flex items-center gap-2 h-auto p-4",children:[(0,r.jsx)(k.A,{className:"w-5 h-5"}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsx)("div",{className:"font-medium",children:"عرض التحليلات"}),(0,r.jsx)("div",{className:"text-sm text-muted-foreground",children:"إحصائيات الأداء"})]})]}),(0,r.jsxs)(i.$,{variant:"outline",className:"flex items-center gap-2 h-auto p-4",children:[(0,r.jsx)(d.A,{className:"w-5 h-5"}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsx)("div",{className:"font-medium",children:"إعدادات متقدمة"}),(0,r.jsx)("div",{className:"text-sm text-muted-foreground",children:"تخصيص الإعدادات"})]})]})]})})]}),(0,r.jsxs)("div",{className:"text-center text-sm text-muted-foreground",children:["آخر تحديث: ",new Date(s.lastUpdated).toLocaleString("ar-SA")]})]})}var L=s(79481);function _(){let[e,t]=(0,a.useState)(null),[s,p]=(0,a.useState)(!0),[y,N]=(0,a.useState)([]),[w,A]=(0,a.useState)(0),k=(0,b.useRouter)();(0,L.U)();let C=async e=>{try{let t=["facebook","linkedin","instagram","twitter"].map(async t=>{try{let s=await fetch(`/api/social/business-accounts?platform=${t}&userId=${e}`),r=await s.json();return{platform:t,isConnected:!r.requiresReconnection,isConfigured:r.isConfigured,hasBusinessAccounts:r.hasBusinessAccounts,selectedAccount:r.selectedAccount}}catch(e){return{platform:t,isConnected:!1,isConfigured:!1,hasBusinessAccounts:!1,selectedAccount:null}}}),s=await Promise.all(t),r=[{id:"connect-facebook",title:"ربط حساب فيسبوك",description:"اربط حساب فيسبوك الخاص بك للوصول إلى صفحاتك التجارية",isCompleted:s.find(e=>"facebook"===e.platform)?.isConnected||!1,isRequired:!0,action:()=>R("facebook")},{id:"select-facebook-page",title:"اختيار صفحة فيسبوك",description:"اختر الصفحة التجارية التي تريد النشر عليها",isCompleted:s.find(e=>"facebook"===e.platform)?.selectedAccount!=null,isRequired:!0},{id:"connect-linkedin",title:"ربط حساب لينكد إن",description:"اربط حساب لينكد إن للوصول إلى شركاتك",isCompleted:s.find(e=>"linkedin"===e.platform)?.isConnected||!1,isRequired:!1,action:()=>R("linkedin")},{id:"select-linkedin-company",title:"اختيار شركة لينكد إن",description:"اختر الشركة التي تريد النشر باسمها",isCompleted:s.find(e=>"linkedin"===e.platform)?.selectedAccount!=null,isRequired:!1},{id:"connect-instagram",title:"ربط حساب إنستغرام",description:"اربط حساب إنستغرام التجاري الخاص بك",isCompleted:s.find(e=>"instagram"===e.platform)?.isConnected||!1,isRequired:!1,action:()=>R("instagram")},{id:"connect-twitter",title:"ربط حساب تويتر",description:"اربط حساب تويتر الخاص بك",isCompleted:s.find(e=>"twitter"===e.platform)?.isConnected||!1,isRequired:!1,action:()=>R("twitter")}];N(r);let a=r.filter(e=>e.isCompleted).length/r.length*100;A(a)}catch(e){console.error("Error loading setup progress:",e),j.o.error("فشل في تحميل حالة الإعداد")}},R=async e=>{try{let t=await fetch("/api/social/connect",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({platform:e})}),s=await t.json();if(!t.ok)throw Error(s.error||`Failed to connect ${e}`);s.authUrl&&(window.location.href=s.authUrl)}catch(t){console.error(`Error connecting ${e}:`,t),j.o.error(`فشل في ربط ${e}`)}},S=async()=>{e&&(await C(e.id),j.o.success("تم تحديث حالة الإعداد"))};if(s)return(0,r.jsx)("div",{className:"container mx-auto p-6 max-w-4xl",children:(0,r.jsxs)("div",{className:"flex items-center justify-center py-12",children:[(0,r.jsx)(o.A,{className:"w-8 h-8 animate-spin text-muted-foreground"}),(0,r.jsx)("span",{className:"mr-3 text-lg",children:"جاري التحميل..."})]})});let E=y.filter(e=>e.isRequired&&e.isCompleted).length,P=y.filter(e=>e.isRequired).length,$=E===P;return(0,r.jsxs)("div",{className:"container mx-auto p-6 max-w-6xl",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold",children:"إعداد الحسابات التجارية"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"اربط وأعد حساباتك التجارية للبدء في إدارة منصات التواصل الاجتماعي"})]}),(0,r.jsxs)(i.$,{onClick:S,variant:"outline",className:"flex items-center gap-2",children:[(0,r.jsx)(o.A,{className:"w-4 h-4"}),"تحديث"]})]}),(0,r.jsx)(n.Zp,{children:(0,r.jsxs)(n.Wu,{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold",children:"تقدم الإعداد"}),(0,r.jsxs)("p",{className:"text-sm text-muted-foreground",children:[y.filter(e=>e.isCompleted).length," من ",y.length," خطوات مكتملة"]})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsxs)("div",{className:"text-2xl font-bold",children:[Math.round(w),"%"]}),(0,r.jsx)("div",{className:"text-sm text-muted-foreground",children:"مكتمل"})]})]}),(0,r.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,r.jsx)("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:`${w}%`}})})]})})]}),(0,r.jsx)("div",{className:"grid gap-6 mb-8",children:(0,r.jsxs)(n.Zp,{children:[(0,r.jsx)(n.aR,{children:(0,r.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(d.A,{className:"w-5 h-5"}),"خطوات الإعداد"]})}),(0,r.jsx)(n.Wu,{children:(0,r.jsx)("div",{className:"space-y-4",children:y.map((e,t)=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[e.isCompleted?(0,r.jsx)(u.A,{className:"w-5 h-5 text-green-500"}):(0,r.jsx)("div",{className:"w-5 h-5 rounded-full border-2 border-gray-300"}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("h3",{className:"font-medium flex items-center gap-2",children:[e.title,e.isRequired&&(0,r.jsx)(l.E,{variant:"destructive",className:"text-xs",children:"مطلوب"})]}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:e.description})]})]}),(0,r.jsx)("div",{className:"flex items-center gap-2",children:e.isCompleted?(0,r.jsx)(l.E,{className:"bg-green-500",children:"مكتمل"}):e.action?(0,r.jsx)(i.$,{onClick:e.action,size:"sm",children:"ربط الآن"}):(0,r.jsx)(l.E,{variant:"secondary",children:"في الانتظار"})})]},e.id))})})]})}),e&&(0,r.jsx)("div",{className:"mb-8",children:(0,r.jsx)(D,{userId:e.id})}),(0,r.jsx)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:$?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(i.$,{onClick:()=>{k.push("/dashboard")},className:"flex items-center gap-2",size:"lg",children:[(0,r.jsx)(m.A,{className:"w-5 h-5"}),"الانتقال إلى لوحة التحكم"]}),(0,r.jsxs)(i.$,{onClick:()=>{k.push("/posts")},variant:"outline",className:"flex items-center gap-2",size:"lg",children:[(0,r.jsx)(x.A,{className:"w-5 h-5"}),"إنشاء منشور جديد"]})]}):(0,r.jsxs)(c.Fc,{children:[(0,r.jsx)(f.A,{className:"h-4 w-4"}),(0,r.jsxs)(c.TN,{children:["يجب إكمال الخطوات المطلوبة (",E,"/",P,") قبل المتابعة. اربط حساب فيسبوك واختر صفحة تجارية للبدء."]})]})}),(0,r.jsxs)("div",{className:"mt-8 grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsx)(n.Zp,{children:(0,r.jsxs)(n.Wu,{className:"p-4 text-center",children:[(0,r.jsx)(h.A,{className:"w-8 h-8 text-blue-500 mx-auto mb-2"}),(0,r.jsx)("div",{className:"text-2xl font-bold",children:y.filter(e=>e.isCompleted).length}),(0,r.jsx)("div",{className:"text-sm text-muted-foreground",children:"منصات متصلة"})]})}),(0,r.jsx)(n.Zp,{children:(0,r.jsxs)(n.Wu,{className:"p-4 text-center",children:[(0,r.jsx)(g,{className:"w-8 h-8 text-green-500 mx-auto mb-2"}),(0,r.jsx)("div",{className:"text-2xl font-bold",children:y.filter(e=>e.isCompleted&&!e.isRequired).length}),(0,r.jsx)("div",{className:"text-sm text-muted-foreground",children:"منصات اختيارية"})]})}),(0,r.jsx)(n.Zp,{children:(0,r.jsxs)(n.Wu,{className:"p-4 text-center",children:[(0,r.jsx)(v.A,{className:"w-8 h-8 text-purple-500 mx-auto mb-2"}),(0,r.jsxs)("div",{className:"text-2xl font-bold",children:[Math.round(w),"%"]}),(0,r.jsx)("div",{className:"text-sm text-muted-foreground",children:"نسبة الإكمال"})]})})]})]})}},98876:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("linkedin",[["path",{d:"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z",key:"c2jq9f"}],["rect",{width:"4",height:"12",x:"2",y:"9",key:"mk3on5"}],["circle",{cx:"4",cy:"4",r:"2",key:"bt5ra8"}]])}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4243,6167,2215,1658,9038,9908],()=>s(77062));module.exports=r})();