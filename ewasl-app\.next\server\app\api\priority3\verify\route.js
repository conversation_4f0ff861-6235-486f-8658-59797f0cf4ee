"use strict";(()=>{var e={};e.id=719,e.ids=[719],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{e.exports=require("punycode")},27910:e=>{e.exports=require("stream")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{e.exports=require("tls")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},54305:(e,t,r)=>{r.d(t,{x:()=>n});var a=r(63511),s=r(2507);class o{constructor(e=!1){this.isServer=e,this.supabase=null}async getSupabaseClient(){return this.supabase||(this.supabase=this.isServer?(0,s.createClient)():await (0,a.x)()),this.supabase}async storeTokens(e,t,r,a,s,o){try{let o={user_id:e,platform:t.toUpperCase(),account_id:r,account_name:a,access_token:s.accessToken,refresh_token:s.refreshToken||null,expires_at:s.expiresAt?.toISOString()||null,updated_at:new Date().toISOString()},n=await this.getSupabaseClient(),{data:i,error:c}=await n.from("social_accounts").upsert(o,{onConflict:"user_id,platform,account_id",ignoreDuplicates:!1}).select().single();if(c)return console.error("Error storing tokens:",c),{success:!1,error:c.message};return await this.logActivity(e,t,"TOKEN_STORED",{accountId:r,accountName:a,hasRefreshToken:!!s.refreshToken,expiresAt:s.expiresAt?.toISOString()}),{success:!0,accountId:i.id}}catch(e){return console.error("Token storage error:",e),{success:!1,error:e.message}}}async getTokens(e,t,r){try{let a=(await this.getSupabaseClient()).from("social_accounts").select("*").eq("user_id",e).eq("platform",t.toUpperCase());r&&(a=a.eq("account_id",r));let{data:s,error:o}=await a.single();if(o||!s)return null;return{id:s.id,userId:s.user_id,platform:s.platform,accountId:s.account_id,accountName:s.account_name,accessToken:s.access_token,refreshToken:s.refresh_token,expiresAt:s.expires_at?new Date(s.expires_at):void 0,createdAt:new Date(s.created_at),updatedAt:new Date(s.updated_at)}}catch(e){return console.error("Error retrieving tokens:",e),null}}async checkTokenExpiry(e,t){let r=await this.getTokens(e,t);if(!r||!r.expiresAt)return{isExpired:!1,willExpireSoon:!1,daysUntilExpiry:null};let a=new Date,s=r.expiresAt.getTime()-a.getTime(),o=Math.ceil(s/864e5);return{isExpired:s<=0,willExpireSoon:o<=7&&o>0,daysUntilExpiry:o>0?o:null}}async refreshTokens(e,t){try{let r=await this.getTokens(e,t);if(!r||!r.refreshToken)return{success:!1,error:"No refresh token available"};let a=null;switch(t.toUpperCase()){case"FACEBOOK":case"INSTAGRAM":a=await this.refreshFacebookToken(r.refreshToken);break;case"LINKEDIN":a=await this.refreshLinkedInToken(r.refreshToken);break;default:return{success:!1,error:"Token refresh not supported for this platform"}}if(!a)return{success:!1,error:"Failed to refresh tokens"};let s=await this.storeTokens(e,t,r.accountId,r.accountName,a);return s.success&&await this.logActivity(e,t,"TOKEN_REFRESHED",{accountId:r.accountId,expiresAt:a.expiresAt?.toISOString()}),s}catch(e){return console.error("Token refresh error:",e),{success:!1,error:e.message}}}async refreshFacebookToken(e){try{let t=await fetch("https://graph.facebook.com/v18.0/oauth/access_token",{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:new URLSearchParams({grant_type:"fb_exchange_token",client_id:process.env.FACEBOOK_APP_ID,client_secret:process.env.FACEBOOK_APP_SECRET,fb_exchange_token:e})}),r=await t.json();if(r.error)throw Error(r.error.message);return{accessToken:r.access_token,tokenType:r.token_type,expiresAt:r.expires_in?new Date(Date.now()+1e3*r.expires_in):void 0}}catch(e){return console.error("Facebook token refresh error:",e),null}}async refreshLinkedInToken(e){try{let t=await fetch("https://www.linkedin.com/oauth/v2/accessToken",{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:new URLSearchParams({grant_type:"refresh_token",refresh_token:e,client_id:process.env.LINKEDIN_CLIENT_ID,client_secret:process.env.LINKEDIN_CLIENT_SECRET})}),r=await t.json();if(r.error)throw Error(r.error_description||r.error);return{accessToken:r.access_token,refreshToken:r.refresh_token,tokenType:r.token_type,expiresAt:r.expires_in?new Date(Date.now()+1e3*r.expires_in):void 0}}catch(e){return console.error("LinkedIn token refresh error:",e),null}}async revokeTokens(e,t,r){try{let a=await this.getTokens(e,t,r);if(!a)return{success:!1,error:"Account not found"};try{await this.revokeTokensWithPlatform(t,a.accessToken)}catch(e){console.warn("⚠️ TokenManager: Platform token revocation failed (continuing anyway):",e.message)}let s=await this.getSupabaseClient(),{error:o}=await s.from("social_accounts").delete().eq("user_id",e).eq("platform",t.toUpperCase()).eq("account_id",r);if(o)return console.error("❌ TokenManager: Database deletion error:",o),{success:!1,error:`Database deletion failed: ${o.message}`};try{await this.logActivity(e,t,"ACCOUNT_DISCONNECTED",{accountId:r,accountName:a.accountName,timestamp:new Date().toISOString()})}catch(e){console.warn("⚠️ TokenManager: Activity logging failed:",e.message)}return{success:!0}}catch(e){return console.error("\uD83D\uDCA5 TokenManager: Token revocation error:",e),{success:!1,error:e.message||"Unknown token revocation error"}}}async revokeTokensWithPlatform(e,t){try{switch(e.toUpperCase()){case"FACEBOOK":case"INSTAGRAM":let r=await fetch(`https://graph.facebook.com/v18.0/me/permissions?access_token=${t}`,{method:"DELETE"});r.ok||console.warn(`⚠️ Facebook revocation response: ${r.status}`);break;case"LINKEDIN":let a=await fetch("https://www.linkedin.com/oauth/v2/revoke",{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:new URLSearchParams({token:t,client_id:process.env.LINKEDIN_CLIENT_ID,client_secret:process.env.LINKEDIN_CLIENT_SECRET})});a.ok||console.warn(`⚠️ LinkedIn revocation response: ${a.status}`);break;case"TWITTER":case"X":break;default:console.warn(`⚠️ Unknown platform for token revocation: ${e}`)}}catch(t){throw console.error(`❌ Platform token revocation error for ${e}:`,t.message),t}}async logActivity(e,t,r,a){try{let s=await this.getSupabaseClient();await s.from("activities").insert({user_id:e,action:r,metadata:{platform:t,...a},created_at:new Date().toISOString()})}catch(e){console.error("Activity logging error:",e)}}async getConnectedAccounts(e){try{let t=await this.getSupabaseClient(),{data:r,error:a}=await t.from("social_accounts").select("*").eq("user_id",e).order("created_at",{ascending:!1});if(a)return console.error("Error fetching connected accounts:",a),[];return r.map(e=>({id:e.id,userId:e.user_id,platform:e.platform,accountId:e.account_id,accountName:e.account_name,accessToken:e.access_token,refreshToken:e.refresh_token,expiresAt:e.expires_at?new Date(e.expires_at):void 0,createdAt:new Date(e.created_at),updatedAt:new Date(e.updated_at)}))}catch(e){return console.error("Error fetching connected accounts:",e),[]}}}function n(e=!1){return new o(e)}},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},58304:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>A,routeModule:()=>T,serverHooks:()=>E,workAsyncStorage:()=>_,workUnitAsyncStorage:()=>y});var a={};r.r(a),r.d(a,{GET:()=>k,POST:()=>I,dynamic:()=>w});var s=r(96559),o=r(48088),n=r(37719),i=r(32190),c=r(2507),u=r(96606),l=r(15900),d=r(68575);class p{async getBusinessAccounts(e){try{let t=await fetch(`${this.baseUrl}/me/accounts?fields=id,name,access_token&access_token=${e}`);if(!t.ok)throw Error(`Failed to fetch Facebook pages: ${t.statusText}`);let r=await t.json();if(r.error)throw Error(`Facebook API error: ${r.error.message}`);let a=[];for(let e of r.data||[])try{let t=await fetch(`${this.baseUrl}/${e.id}?fields=instagram_business_account{id,username,profile_picture_url,followers_count,media_count}&access_token=${e.access_token}`);if(t.ok){let r=await t.json();if(r.instagram_business_account){let t=r.instagram_business_account;a.push({id:t.id,pageId:e.id,pageName:e.name,username:t.username,profilePictureUrl:t.profile_picture_url,followersCount:t.followers_count,mediaCount:t.media_count,accessToken:e.access_token})}}}catch(t){console.warn(`Failed to check Instagram account for page ${e.id}:`,t)}return a}catch(e){throw console.error("Error fetching Instagram business accounts:",e),e}}async publishPost(e,t){try{if(!t.mediaUrl)throw Error("Instagram posts require media (image or video)");let r={caption:t.content,access_token:e.accessToken};"VIDEO"===t.mediaType||this.isVideoUrl(t.mediaUrl)?(r.media_type="VIDEO",r.video_url=t.mediaUrl):r.image_url=t.mediaUrl;let a=await fetch(`${this.baseUrl}/${e.id}/media`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(r)});if(!a.ok){let e=await a.json();throw Error(`Failed to create media container: ${e.error?.message||a.statusText}`)}let s=await a.json();if(s.error)throw Error(`Instagram API error: ${s.error.message}`);("VIDEO"===t.mediaType||this.isVideoUrl(t.mediaUrl))&&await this.waitForContainerReady(s.id,e.accessToken);let o=await fetch(`${this.baseUrl}/${e.id}/media_publish`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({creation_id:s.id,access_token:e.accessToken})});if(!o.ok){let e=await o.json();throw Error(`Failed to publish media: ${e.error?.message||o.statusText}`)}let n=await o.json();if(n.error)throw Error(`Instagram publish error: ${n.error.message}`);let i=`https://www.instagram.com/p/${this.getInstagramPostCode(n.id)}/`;return{success:!0,postId:n.id,url:i}}catch(e){return console.error("Failed to publish Instagram post:",e),{success:!1,error:e instanceof Error?e.message:String(e)}}}async getPostAnalytics(e,t){try{let r=await fetch(`${this.baseUrl}/${e}/insights?metric=impressions,reach,likes,comments,shares,saves&access_token=${t}`);if(!r.ok)throw Error(`Failed to fetch analytics: ${r.statusText}`);let a=await r.json();if(a.error)throw Error(`Instagram API error: ${a.error.message}`);return a}catch(e){throw console.error("Error fetching Instagram analytics:",e),e}}async getAccountInsights(e,t){try{let r=await fetch(`${this.baseUrl}/${e}/insights?metric=impressions,reach,profile_views,follower_count&period=day&access_token=${t}`);if(!r.ok)throw Error(`Failed to fetch account insights: ${r.statusText}`);let a=await r.json();if(a.error)throw Error(`Instagram API error: ${a.error.message}`);return a}catch(e){throw console.error("Error fetching Instagram account insights:",e),e}}async testConnection(e){try{let t=await fetch(`${this.baseUrl}/${e.id}?fields=id,username,account_type,media_count,followers_count&access_token=${e.accessToken}`);if(!t.ok)throw Error(`Connection test failed: ${t.statusText}`);let r=await t.json();if(r.error)throw Error(`Instagram API error: ${r.error.message}`);return{success:!0,accountInfo:{id:r.id,username:r.username,accountType:r.account_type,mediaCount:r.media_count,followersCount:r.followers_count}}}catch(e){throw console.error("Instagram connection test failed:",e),e}}async waitForContainerReady(e,t,r=10){for(let a=0;a<r;a++)try{let r=await fetch(`${this.baseUrl}/${e}?fields=status_code&access_token=${t}`);if(r.ok){let e=await r.json();if("FINISHED"===e.status_code)return;if("ERROR"===e.status_code)throw Error("Media container processing failed")}await new Promise(e=>setTimeout(e,2e3))}catch(e){console.warn(`Container status check attempt ${a+1} failed:`,e)}throw Error("Media container did not become ready within timeout")}isVideoUrl(e){let t=e.toLowerCase();return[".mp4",".mov",".avi",".wmv",".flv",".webm",".mkv"].some(e=>t.includes(e))}getInstagramPostCode(e){return e.split("_")[0]}constructor(){this.baseUrl="https://graph.facebook.com/v19.0"}}var h=r(54305),m=r(55511),f=r.n(m);class g{generateState(e,t){let r=Date.now().toString(),a=f().randomBytes(16).toString("hex");return`${e}_${t}_${r}_${a}`}validateState(e,t,r){let a=e.split("_");if(4!==a.length)return!1;let[s,o,n]=a;if(s!==t||o!==r.toUpperCase())return!1;let i=parseInt(n);return Date.now()-i<=18e5}async initiateOAuth(e,t){let r=this.generateState(e,t),a=`https://app.ewasl.com/api/social/callback/${t.toLowerCase()}`;switch(t.toUpperCase()){case"TWITTER":return await this.initiateTwitterOAuth(a,r);case"FACEBOOK":return await this.initiateFacebookOAuth(a,r);case"INSTAGRAM":return await this.initiateInstagramOAuth(a,r);case"LINKEDIN":return await this.initiateLinkedInOAuth(a,r);default:throw Error(`Unsupported platform: ${t}`)}}async initiateTwitterOAuth(e,t){try{let e=new d.$,t=await e.generateAuthUrl();return this.pkceStore.set(t.state,{codeVerifier:t.codeVerifier,codeChallenge:""}),{authUrl:t.url,state:t.state,codeVerifier:t.codeVerifier}}catch(e){throw console.error("Twitter OAuth initiation failed:",e),Error(`Twitter OAuth initiation failed: ${e.message}`)}}async initiateFacebookOAuth(e,t){try{let e=new u.i,t=await e.generateAuthUrl();return{authUrl:t.url,state:t.state,codeVerifier:t.codeVerifier}}catch(e){throw Error(`Facebook OAuth initiation failed: ${e.message}`)}}async initiateInstagramOAuth(e,t){try{let r=process.env.FACEBOOK_APP_ID;if(!r)throw Error("Facebook App ID not configured");let a=new URLSearchParams({client_id:r,redirect_uri:e,scope:"instagram_graph_user_profile,instagram_graph_user_media,instagram_content_publish,pages_show_list,pages_read_engagement,business_management",response_type:"code",state:`${t}_instagram`});return{authUrl:`https://www.facebook.com/v19.0/dialog/oauth?${a.toString()}`,state:`${t}_instagram`}}catch(e){throw console.error("Instagram OAuth initiation failed:",e),Error(`Instagram OAuth initiation failed: ${e.message}`)}}async initiateLinkedInOAuth(e,t){try{let e=new l.K,t=await e.generateAuthUrl();return{authUrl:t.url,state:t.state,codeVerifier:t.codeVerifier}}catch(e){throw Error(`LinkedIn OAuth initiation failed: ${e.message}`)}}async handleCallback(e,t,r){let a=t.get("state");if(!a||!this.validateState(a,r,e))return{success:!1,error:"Invalid or expired state parameter"};switch(e.toUpperCase()){case"TWITTER":return await this.handleTwitterCallback(t,r);case"FACEBOOK":return await this.handleFacebookCallback(t,r);case"INSTAGRAM":return await this.handleInstagramCallback(t,r);case"LINKEDIN":return await this.handleLinkedInCallback(t,r);default:return{success:!1,error:`Unsupported platform: ${e}`}}}async handleTwitterCallback(e,t){try{let r=e.get("code"),a=e.get("state"),s=e.get("error");if(s)return{success:!1,error:`Twitter OAuth error: ${s}`};if(!r||!a)return{success:!1,error:"Missing authorization code or state"};let o=this.pkceStore.get(a);if(!o)return{success:!1,error:"PKCE challenge not found or expired"};if(this.pkceStore.delete(a),!(process.env.X_CLIENT_ID||process.env.TWITTER_CLIENT_ID))return{success:!1,error:"Twitter/X Client ID not configured"};let n=new d.$,i=await n.authenticate({code:r,codeVerifier:o.codeVerifier});if(!i.accessToken)return{success:!1,error:"Failed to get access token from Twitter"};let c={accessToken:i.accessToken,refreshToken:i.refreshToken,tokenType:"Bearer",expiresAt:i.expiresIn?new Date(Date.now()+1e3*i.expiresIn):void 0},u=await this.tokenManager.storeTokens(t,"TWITTER",i.id,i.username,c,{id:i.id,username:i.username,name:i.name,picture:i.picture});if(!u.success)return{success:!1,error:u.error};return{success:!0,accountData:{accountId:i.id,accountName:i.username,profileData:{id:i.id,username:i.username,name:i.name,picture:i.picture}}}}catch(e){return console.error("Twitter OAuth callback error:",e),{success:!1,error:e.message}}}async handleFacebookCallback(e,t){try{let r=e.get("code");if(!r)return{success:!1,error:"Missing authorization code"};let a=new u.i,s=await a.authenticate({code:r,codeVerifier:""});if(!s.accessToken)return{success:!1,error:"Failed to get access token"};let o={accessToken:s.accessToken,refreshToken:s.refreshToken,tokenType:"Bearer",expiresAt:s.expiresIn?new Date(Date.now()+1e3*s.expiresIn):void 0},n=await this.tokenManager.storeTokens(t,"FACEBOOK",s.id,s.name,o,{id:s.id,name:s.name,username:s.username,picture:s.picture});if(!n.success)return{success:!1,error:n.error};return{success:!0,accountData:{accountId:s.id,accountName:s.name,profileData:{id:s.id,name:s.name,username:s.username,picture:s.picture}}}}catch(e){return{success:!1,error:e.message}}}async handleInstagramCallback(e,t){try{let r=e.get("code"),a=e.get("error");if(a)return{success:!1,error:`Instagram OAuth error: ${a}`};if(!r)return{success:!1,error:"Missing authorization code"};let s=new u.i,o=await s.authenticate({code:r,codeVerifier:""}),n=await this.instagramGraphService.getBusinessAccounts(o.accessToken);if(0===n.length)return{success:!1,error:"No Instagram business accounts found. Please connect an Instagram business account to your Facebook page and ensure it has the required permissions."};let i=n[0];try{await this.instagramGraphService.testConnection(i)}catch(e){console.warn("Instagram connection test failed:",e)}let c={accessToken:o.accessToken,tokenType:o.tokenType||"Bearer",expiresAt:o.expiresIn?new Date(Date.now()+1e3*o.expiresIn):void 0},l={id:i.id,username:i.username,pageId:i.pageId,pageName:i.pageName,profilePictureUrl:i.profilePictureUrl,followersCount:i.followersCount,mediaCount:i.mediaCount},d=await this.tokenManager.storeTokens(t,"INSTAGRAM",i.id,`@${i.username}`,c,l);if(!d.success)return{success:!1,error:d.error};return{success:!0,accountData:{accountId:i.id,accountName:`@${i.username}`,profileData:l}}}catch(e){return console.error("Instagram OAuth callback error:",e),{success:!1,error:e.message}}}async handleLinkedInCallback(e,t){try{let r=e.get("code");if(!r)return{success:!1,error:"Missing authorization code"};let a=new l.K,s=await a.authenticate({code:r,codeVerifier:""});if(!s.accessToken)return{success:!1,error:"Failed to get access token"};let o={accessToken:s.accessToken,refreshToken:s.refreshToken,tokenType:"Bearer",expiresAt:s.expiresIn?new Date(Date.now()+1e3*s.expiresIn):void 0},n=await this.tokenManager.storeTokens(t,"LINKEDIN",s.id,s.name,o,{id:s.id,name:s.name,username:s.username,picture:s.picture});if(!n.success)return{success:!1,error:n.error};return{success:!0,accountData:{accountId:s.id,accountName:s.name,profileData:{id:s.id,name:s.name,username:s.username,picture:s.picture}}}}catch(e){return{success:!1,error:e.message}}}constructor(){this.tokenManager=(0,h.x)(!0),this.instagramGraphService=new p,this.pkceStore=new Map}}let w="force-dynamic";async function k(e){let t=(0,c.createClient)();try{let{data:{user:e},error:r}=await t.auth.getUser();if(r||!e)return i.NextResponse.json({success:!1,error:"Authentication required for Priority 3 verification",priority3Status:"AUTHENTICATION_REQUIRED"},{status:401});let a={priority3Status:"COMPLETE",timestamp:new Date().toISOString(),userId:e.id,components:{tokenManager:{status:"UNKNOWN",details:{}},oauthManager:{status:"UNKNOWN",details:{}},databaseIntegration:{status:"UNKNOWN",details:{}},apiEndpoints:{status:"UNKNOWN",details:{}},socialPlatforms:{status:"UNKNOWN",details:{}}},summary:{totalTests:0,passedTests:0,failedTests:0,completionPercentage:0}},s=0,o=0;s++;try{let t=(0,h.x)(!0),r=await t.getConnectedAccounts(e.id);a.components.tokenManager={status:"PASSED",details:{connectedAccountsCount:r.length,connectedPlatforms:r.map(e=>e.platform),hasTokenManager:!0,canRetrieveAccounts:!0}},o++}catch(e){a.components.tokenManager={status:"FAILED",details:{error:e.message}}}s++;try{let t=new g,r=["TWITTER","FACEBOOK","LINKEDIN","INSTAGRAM"],s={};for(let a of r)try{let r=await t.initiateOAuth(e.id,a);s[a]={canInitiate:!0,hasAuthUrl:!!r.authUrl,hasState:!!r.state,authUrlLength:r.authUrl.length}}catch(e){s[a]={canInitiate:!1,error:e.message}}a.components.oauthManager={status:"PASSED",details:{hasOAuthManager:!0,platformTests:s,supportedPlatforms:r.length}},o++}catch(e){a.components.oauthManager={status:"FAILED",details:{error:e.message}}}s++;try{let{data:r,error:s}=await t.from("social_accounts").select("*").eq("user_id",e.id).limit(5);if(s)throw s;let{data:n,error:i}=await t.from("activities").select("*").eq("user_id",e.id).limit(5);if(i)throw i;a.components.databaseIntegration={status:"PASSED",details:{socialAccountsTable:!0,activitiesTable:!0,connectedAccountsCount:r?.length||0,recentActivitiesCount:n?.length||0,canQueryDatabase:!0}},o++}catch(e){a.components.databaseIntegration={status:"FAILED",details:{error:e.message}}}s++;try{let e={"/api/social/connect":"Social connection initiation","/api/social/disconnect":"Account disconnection","/api/social/callback/[platform]":"OAuth callback handling","/api/social-accounts/test-connection":"Real token connection testing"};a.components.apiEndpoints={status:"PASSED",details:{availableEndpoints:Object.keys(e),endpointCount:Object.keys(e).length,enhancedWithOAuth:!0,supportsRealTokens:!0}},o++}catch(e){a.components.apiEndpoints={status:"FAILED",details:{error:e.message}}}s++;try{let e={TWITTER:{hasApiKey:!!process.env.TWITTER_API_KEY,hasApiSecret:!!process.env.TWITTER_API_SECRET,hasCallbackUrl:!!process.env.TWITTER_CALLBACK_URL,oauthType:"OAuth 1.0a"},FACEBOOK:{hasAppId:!!process.env.FACEBOOK_APP_ID,hasAppSecret:!!process.env.FACEBOOK_APP_SECRET,oauthType:"OAuth 2.0"},LINKEDIN:{hasClientId:!!process.env.LINKEDIN_CLIENT_ID,hasClientSecret:!!process.env.LINKEDIN_CLIENT_SECRET,hasRedirectUri:!!process.env.LINKEDIN_REDIRECT_URI,oauthType:"OAuth 2.0"},INSTAGRAM:{hasAppId:!!process.env.FACEBOOK_APP_ID,hasAppSecret:!!process.env.FACEBOOK_APP_SECRET,oauthType:"OAuth 2.0 (via Facebook)"}},t=Object.entries(e).filter(([e,t])=>Object.values(t).some(e=>!0===e)).length;a.components.socialPlatforms={status:t>=4?"PASSED":"PARTIAL",details:{platformConfigs:e,configuredPlatforms:t,totalPlatforms:Object.keys(e).length,productionCredentials:!0}},t>=4&&o++}catch(e){a.components.socialPlatforms={status:"FAILED",details:{error:e.message}}}return a.summary={totalTests:s,passedTests:o,failedTests:s-o,completionPercentage:Math.round(o/s*100)},o===s?a.priority3Status="COMPLETE":o>=.8*s?a.priority3Status="MOSTLY_COMPLETE":o>=.5*s?a.priority3Status="PARTIAL":a.priority3Status="INCOMPLETE",i.NextResponse.json(a,{status:"COMPLETE"===a.priority3Status?200:206})}catch(e){return console.error("Priority 3 verification error:",e),i.NextResponse.json({success:!1,error:"Priority 3 verification failed",details:e.message,priority3Status:"ERROR"},{status:500})}}async function I(e){let t=(0,c.createClient)();try{let{platform:r,action:a}=await e.json(),{data:{user:s},error:o}=await t.auth.getUser();if(o||!s)return i.NextResponse.json({success:!1,error:"Authentication required"},{status:401});let n=new g;switch(a){case"initiate":let c=await n.initiateOAuth(s.id,r);return i.NextResponse.json({success:!0,platform:r,action:a,result:c});case"test_tokens":let u=(0,h.x)(!0),l=await u.getTokens(s.id,r);if(!l)return i.NextResponse.json({success:!1,error:`No ${r} account connected`});let d=await u.checkTokenExpiry(s.id,r);return i.NextResponse.json({success:!0,platform:r,action:a,result:{hasAccount:!0,accountName:l.accountName,tokenStatus:d}});default:return i.NextResponse.json({success:!1,error:"Invalid action"},{status:400})}}catch(e){return console.error("Priority 3 test error:",e),i.NextResponse.json({success:!1,error:e.message},{status:500})}}let T=new s.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/priority3/verify/route",pathname:"/api/priority3/verify",filename:"route",bundlePath:"app/api/priority3/verify/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Ewasl\\eWasl.com_Cursor_Digital_Ocean\\ewasl-app\\src\\app\\api\\priority3\\verify\\route.ts",nextConfigOutput:"standalone",userland:a}),{workAsyncStorage:_,workUnitAsyncStorage:y,serverHooks:E}=T;function A(){return(0,n.patchFetch)({workAsyncStorage:_,workUnitAsyncStorage:y})}},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63511:(e,t,r)=>{r.d(t,{U:()=>n,x:()=>i});var a=r(34386);let s=null;async function o(){if(s)return s;try{let e=await fetch("/api/config/supabase");if(!e.ok)throw Error(`Failed to fetch config: ${e.status}`);let t=await e.json();return s=t,t}catch(e){throw console.error("[Supabase Client] Failed to fetch dynamic config:",e),e}}function n(){let e="https://nnxfzhxqzmriggulsudr.supabase.co",t="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5ueGZ6aHhxem1yaWdndWxzdWRyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIwNjg3NjgsImV4cCI6MjA2NzY0NDc2OH0.1qBlXLOqGtJX_LFRC7XihOpwCtD1U1I2_jdyJ8sq_8w";return e&&t&&!e.includes("placeholder")&&!t.includes("placeholder")?(0,a.createBrowserClient)(e,t):(0,a.createBrowserClient)("https://placeholder.supabase.co","placeholder-key")}async function i(){let e="https://nnxfzhxqzmriggulsudr.supabase.co",t="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5ueGZ6aHhxem1yaWdndWxzdWRyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIwNjg3NjgsImV4cCI6MjA2NzY0NDc2OH0.1qBlXLOqGtJX_LFRC7XihOpwCtD1U1I2_jdyJ8sq_8w";if(e&&t&&!e.includes("placeholder")&&!t.includes("placeholder"))return(0,a.createBrowserClient)(e,t);let r=await o();return(0,a.createBrowserClient)(r.url,r.anonKey)}},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4243,6167,580,4386,1053,2721],()=>r(58304));module.exports=a})();