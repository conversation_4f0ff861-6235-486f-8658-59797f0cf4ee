{"version": 3, "file": "index.umd.js", "sources": ["../src/character-count.ts"], "sourcesContent": ["import { Extension } from '@tiptap/core'\nimport { Node as ProseMirrorNode } from '@tiptap/pm/model'\nimport { <PERSON>lug<PERSON>, PluginKey } from '@tiptap/pm/state'\n\nexport interface CharacterCountOptions {\n  /**\n   * The maximum number of characters that should be allowed. Defaults to `0`.\n   * @default null\n   * @example 180\n   */\n  limit: number | null | undefined\n  /**\n   * The mode by which the size is calculated. If set to `textSize`, the textContent of the document is used.\n   * If set to `nodeSize`, the nodeSize of the document is used.\n   * @default 'textSize'\n   * @example 'textSize'\n   */\n  mode: 'textSize' | 'nodeSize'\n  /**\n * The text counter function to use. Defaults to a simple character count.\n * @default (text) => text.length\n * @example (text) => [...new Intl.Segmenter().segment(text)].length\n */\n  textCounter: (text: string) => number\n  /**\n   * The word counter function to use. Defaults to a simple word count.\n   * @default (text) => text.split(' ').filter(word => word !== '').length\n   * @example (text) => text.split(/\\s+/).filter(word => word !== '').length\n   */\n  wordCounter: (text: string) => number\n}\n\nexport interface CharacterCountStorage {\n  /**\n   * Get the number of characters for the current document.\n   * @param options The options for the character count. (optional)\n   * @param options.node The node to get the characters from. Defaults to the current document.\n   * @param options.mode The mode by which the size is calculated. If set to `textSize`, the textContent of the document is used.\n   */\n  characters: (options?: { node?: ProseMirrorNode; mode?: 'textSize' | 'nodeSize' }) => number\n\n  /**\n   * Get the number of words for the current document.\n   * @param options The options for the character count. (optional)\n   * @param options.node The node to get the words from. Defaults to the current document.\n   */\n  words: (options?: { node?: ProseMirrorNode }) => number\n}\n\n/**\n * This extension allows you to count the characters and words of your document.\n * @see https://tiptap.dev/api/extensions/character-count\n */\nexport const CharacterCount = Extension.create<CharacterCountOptions, CharacterCountStorage>({\n  name: 'characterCount',\n\n  addOptions() {\n    return {\n      limit: null,\n      mode: 'textSize',\n      textCounter: text => text.length,\n      wordCounter: text => text.split(' ').filter(word => word !== '').length,\n    }\n  },\n\n  addStorage() {\n    return {\n      characters: () => 0,\n      words: () => 0,\n    }\n  },\n\n  onBeforeCreate() {\n    this.storage.characters = options => {\n      const node = options?.node || this.editor.state.doc\n      const mode = options?.mode || this.options.mode\n\n      if (mode === 'textSize') {\n        const text = node.textBetween(0, node.content.size, undefined, ' ')\n\n        return this.options.textCounter(text)\n      }\n\n      return node.nodeSize\n    }\n\n    this.storage.words = options => {\n      const node = options?.node || this.editor.state.doc\n      const text = node.textBetween(0, node.content.size, ' ', ' ')\n\n      return this.options.wordCounter(text)\n    }\n  },\n\n  addProseMirrorPlugins() {\n    let initialEvaluationDone = false\n\n    return [\n      new Plugin({\n        key: new PluginKey('characterCount'),\n        appendTransaction: (transactions, oldState, newState) => {\n          if (initialEvaluationDone) {\n            return\n          }\n\n          const limit = this.options.limit\n\n          if (limit === null || limit === undefined || limit === 0) {\n            initialEvaluationDone = true\n            return\n          }\n\n          const initialContentSize = this.storage.characters({ node: newState.doc })\n\n          if (initialContentSize > limit) {\n            const over = initialContentSize - limit\n            const from = 0\n            const to = over\n\n            console.warn(`[CharacterCount] Initial content exceeded limit of ${limit} characters. Content was automatically trimmed.`)\n            const tr = newState.tr.deleteRange(from, to)\n\n            initialEvaluationDone = true\n            return tr\n          }\n\n          initialEvaluationDone = true\n        },\n        filterTransaction: (transaction, state) => {\n          const limit = this.options.limit\n\n          // Nothing has changed or no limit is defined. Ignore it.\n          if (!transaction.docChanged || limit === 0 || limit === null || limit === undefined) {\n            return true\n          }\n\n          const oldSize = this.storage.characters({ node: state.doc })\n          const newSize = this.storage.characters({ node: transaction.doc })\n\n          // Everything is in the limit. Good.\n          if (newSize <= limit) {\n            return true\n          }\n\n          // The limit has already been exceeded but will be reduced.\n          if (oldSize > limit && newSize > limit && newSize <= oldSize) {\n            return true\n          }\n\n          // The limit has already been exceeded and will be increased further.\n          if (oldSize > limit && newSize > limit && newSize > oldSize) {\n            return false\n          }\n\n          const isPaste = transaction.getMeta('paste')\n\n          // Block all exceeding transactions that were not pasted.\n          if (!isPaste) {\n            return false\n          }\n\n          // For pasted content, we try to remove the exceeding content.\n          const pos = transaction.selection.$head.pos\n          const over = newSize - limit\n          const from = pos - over\n          const to = pos\n\n          // It’s probably a bad idea to mutate transactions within `filterTransaction`\n          // but for now this is working fine.\n          transaction.deleteRange(from, to)\n\n          // In some situations, the limit will continue to be exceeded after trimming.\n          // This happens e.g. when truncating within a complex node (e.g. table)\n          // and ProseMirror has to close this node again.\n          // If this is the case, we prevent the transaction completely.\n          const updatedSize = this.storage.characters({ node: transaction.doc })\n\n          if (updatedSize > limit) {\n            return false\n          }\n\n          return true\n        },\n      }),\n    ]\n  },\n})\n"], "names": ["Extension", "Plugin", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;EAiDA;;;EAGG;AACU,QAAA,cAAc,GAAGA,cAAS,CAAC,MAAM,CAA+C;EAC3F,IAAA,IAAI,EAAE,gBAAgB;MAEtB,UAAU,GAAA;UACR,OAAO;EACL,YAAA,KAAK,EAAE,IAAI;EACX,YAAA,IAAI,EAAE,UAAU;EAChB,YAAA,WAAW,EAAE,IAAI,IAAI,IAAI,CAAC,MAAM;cAChC,WAAW,EAAE,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,IAAI,IAAI,IAAI,KAAK,EAAE,CAAC,CAAC,MAAM;WACxE;OACF;MAED,UAAU,GAAA;UACR,OAAO;EACL,YAAA,UAAU,EAAE,MAAM,CAAC;EACnB,YAAA,KAAK,EAAE,MAAM,CAAC;WACf;OACF;MAED,cAAc,GAAA;EACZ,QAAA,IAAI,CAAC,OAAO,CAAC,UAAU,GAAG,OAAO,IAAG;EAClC,YAAA,MAAM,IAAI,GAAG,CAAA,OAAO,KAAP,IAAA,IAAA,OAAO,uBAAP,OAAO,CAAE,IAAI,KAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG;EACnD,YAAA,MAAM,IAAI,GAAG,CAAA,OAAO,KAAA,IAAA,IAAP,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,IAAI,KAAI,IAAI,CAAC,OAAO,CAAC,IAAI;EAE/C,YAAA,IAAI,IAAI,KAAK,UAAU,EAAE;EACvB,gBAAA,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,SAAS,EAAE,GAAG,CAAC;kBAEnE,OAAO,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC;;cAGvC,OAAO,IAAI,CAAC,QAAQ;EACtB,SAAC;EAED,QAAA,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,OAAO,IAAG;EAC7B,YAAA,MAAM,IAAI,GAAG,CAAA,OAAO,KAAP,IAAA,IAAA,OAAO,uBAAP,OAAO,CAAE,IAAI,KAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG;EACnD,YAAA,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC;cAE7D,OAAO,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC;EACvC,SAAC;OACF;MAED,qBAAqB,GAAA;UACnB,IAAI,qBAAqB,GAAG,KAAK;UAEjC,OAAO;EACL,YAAA,IAAIC,YAAM,CAAC;EACT,gBAAA,GAAG,EAAE,IAAIC,eAAS,CAAC,gBAAgB,CAAC;kBACpC,iBAAiB,EAAE,CAAC,YAAY,EAAE,QAAQ,EAAE,QAAQ,KAAI;sBACtD,IAAI,qBAAqB,EAAE;0BACzB;;EAGF,oBAAA,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK;EAEhC,oBAAA,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,CAAC,EAAE;0BACxD,qBAAqB,GAAG,IAAI;0BAC5B;;EAGF,oBAAA,MAAM,kBAAkB,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,GAAG,EAAE,CAAC;EAE1E,oBAAA,IAAI,kBAAkB,GAAG,KAAK,EAAE;EAC9B,wBAAA,MAAM,IAAI,GAAG,kBAAkB,GAAG,KAAK;0BACvC,MAAM,IAAI,GAAG,CAAC;0BACd,MAAM,EAAE,GAAG,IAAI;EAEf,wBAAA,OAAO,CAAC,IAAI,CAAC,sDAAsD,KAAK,CAAA,+CAAA,CAAiD,CAAC;EAC1H,wBAAA,MAAM,EAAE,GAAG,QAAQ,CAAC,EAAE,CAAC,WAAW,CAAC,IAAI,EAAE,EAAE,CAAC;0BAE5C,qBAAqB,GAAG,IAAI;EAC5B,wBAAA,OAAO,EAAE;;sBAGX,qBAAqB,GAAG,IAAI;mBAC7B;EACD,gBAAA,iBAAiB,EAAE,CAAC,WAAW,EAAE,KAAK,KAAI;EACxC,oBAAA,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK;;EAGhC,oBAAA,IAAI,CAAC,WAAW,CAAC,UAAU,IAAI,KAAK,KAAK,CAAC,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS,EAAE;EACnF,wBAAA,OAAO,IAAI;;EAGb,oBAAA,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,IAAI,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC;EAC5D,oBAAA,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,IAAI,EAAE,WAAW,CAAC,GAAG,EAAE,CAAC;;EAGlE,oBAAA,IAAI,OAAO,IAAI,KAAK,EAAE;EACpB,wBAAA,OAAO,IAAI;;;EAIb,oBAAA,IAAI,OAAO,GAAG,KAAK,IAAI,OAAO,GAAG,KAAK,IAAI,OAAO,IAAI,OAAO,EAAE;EAC5D,wBAAA,OAAO,IAAI;;;EAIb,oBAAA,IAAI,OAAO,GAAG,KAAK,IAAI,OAAO,GAAG,KAAK,IAAI,OAAO,GAAG,OAAO,EAAE;EAC3D,wBAAA,OAAO,KAAK;;sBAGd,MAAM,OAAO,GAAG,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC;;sBAG5C,IAAI,CAAC,OAAO,EAAE;EACZ,wBAAA,OAAO,KAAK;;;sBAId,MAAM,GAAG,GAAG,WAAW,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG;EAC3C,oBAAA,MAAM,IAAI,GAAG,OAAO,GAAG,KAAK;EAC5B,oBAAA,MAAM,IAAI,GAAG,GAAG,GAAG,IAAI;sBACvB,MAAM,EAAE,GAAG,GAAG;;;EAId,oBAAA,WAAW,CAAC,WAAW,CAAC,IAAI,EAAE,EAAE,CAAC;;;;;EAMjC,oBAAA,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,IAAI,EAAE,WAAW,CAAC,GAAG,EAAE,CAAC;EAEtE,oBAAA,IAAI,WAAW,GAAG,KAAK,EAAE;EACvB,wBAAA,OAAO,KAAK;;EAGd,oBAAA,OAAO,IAAI;mBACZ;eACF,CAAC;WACH;OACF;EACF,CAAA;;;;;;;;;;;"}