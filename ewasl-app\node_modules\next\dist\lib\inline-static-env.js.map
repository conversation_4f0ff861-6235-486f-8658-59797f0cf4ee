{"version": 3, "sources": ["../../src/lib/inline-static-env.ts"], "sourcesContent": ["import fs from 'fs'\nimport path from 'path'\nimport crypto from 'crypto'\nimport { promisify } from 'util'\nimport globOriginal from 'next/dist/compiled/glob'\nimport { Sema } from 'next/dist/compiled/async-sema'\nimport type { NextConfigComplete } from '../server/config-shared'\nimport { getNextConfigEnv, getStaticEnv } from './static-env'\n\nconst glob = promisify(globOriginal)\n\nexport async function inlineStaticEnv({\n  distDir,\n  config,\n}: {\n  distDir: string\n  config: NextConfigComplete\n}) {\n  const nextConfigEnv = getNextConfigEnv(config)\n  const staticEnv = getStaticEnv(config)\n\n  const serverDir = path.join(distDir, 'server')\n  const serverChunks = await glob('**/*.{js,json,js.map}', {\n    cwd: serverDir,\n  })\n  const clientDir = path.join(distDir, 'static')\n  const clientChunks = await glob('**/*.{js,json,js.map}', {\n    cwd: clientDir,\n  })\n  const manifestChunks = await glob('*.{js,json,js.map}', {\n    cwd: distDir,\n  })\n\n  const inlineSema = new Sema(8)\n  const nextConfigEnvKeys = Object.keys(nextConfigEnv).map((item) =>\n    item.split('process.env.').pop()\n  )\n\n  const builtRegEx = new RegExp(\n    `[\\\\w]{1,}(\\\\.env)?\\\\.(?:NEXT_PUBLIC_[\\\\w]{1,}${nextConfigEnvKeys.length ? '|' + nextConfigEnvKeys.join('|') : ''})`,\n    'g'\n  )\n  const changedClientFiles: Array<{ file: string; content: string }> = []\n  const filesToCheck = new Set<string>(\n    manifestChunks.map((f) => path.join(distDir, f))\n  )\n\n  for (const [parentDir, files] of [\n    [serverDir, serverChunks],\n    [clientDir, clientChunks],\n  ] as const) {\n    await Promise.all(\n      files.map(async (file) => {\n        await inlineSema.acquire()\n        const filepath = path.join(parentDir, file)\n        const content = await fs.promises.readFile(filepath, 'utf8')\n        const newContent = content.replace(builtRegEx, (match) => {\n          let normalizedMatch = `process.env.${match.split('.').pop()}`\n\n          if (staticEnv[normalizedMatch]) {\n            return JSON.stringify(staticEnv[normalizedMatch])\n          }\n          return match\n        })\n\n        await fs.promises.writeFile(filepath, newContent)\n\n        if (content !== newContent && parentDir === clientDir) {\n          changedClientFiles.push({ file, content: newContent })\n        }\n        filesToCheck.add(filepath)\n        inlineSema.release()\n      })\n    )\n  }\n  const hashChanges: Array<{\n    originalHash: string\n    newHash: string\n  }> = []\n\n  // hashes need updating for any changed client files\n  for (const { file, content } of changedClientFiles) {\n    // hash is 16 chars currently for all client chunks\n    const originalHash = file.match(/([a-z0-9]{16})\\./)?.[1] || ''\n\n    if (!originalHash) {\n      throw new Error(\n        `Invariant: client chunk changed but failed to detect hash ${file}`\n      )\n    }\n    const newHash = crypto\n      .createHash('sha256')\n      .update(content)\n      .digest('hex')\n      .substring(0, 16)\n\n    hashChanges.push({ originalHash, newHash })\n\n    const filepath = path.join(clientDir, file)\n    const newFilepath = filepath.replace(originalHash, newHash)\n\n    filesToCheck.delete(filepath)\n    filesToCheck.add(newFilepath)\n\n    await fs.promises.rename(filepath, newFilepath)\n  }\n\n  // update build-manifest and webpack-runtime with new hashes\n  for (let file of filesToCheck) {\n    const content = await fs.promises.readFile(file, 'utf-8')\n    let newContent = content\n\n    for (const { originalHash, newHash } of hashChanges) {\n      newContent = newContent.replaceAll(originalHash, newHash)\n    }\n    if (content !== newContent) {\n      await fs.promises.writeFile(file, newContent)\n    }\n  }\n}\n"], "names": ["inlineStaticEnv", "glob", "promisify", "globOriginal", "distDir", "config", "nextConfigEnv", "getNextConfigEnv", "staticEnv", "getStaticEnv", "serverDir", "path", "join", "serverChunks", "cwd", "clientDir", "clientChunks", "manifestChunks", "inlineSema", "<PERSON><PERSON>", "nextConfigEnvKeys", "Object", "keys", "map", "item", "split", "pop", "builtRegEx", "RegExp", "length", "changedClientFiles", "filesToCheck", "Set", "f", "parentDir", "files", "Promise", "all", "file", "acquire", "filepath", "content", "fs", "promises", "readFile", "newContent", "replace", "match", "normalizedMatch", "JSON", "stringify", "writeFile", "push", "add", "release", "hashChanges", "originalHash", "Error", "newHash", "crypto", "createHash", "update", "digest", "substring", "newFilepath", "delete", "rename", "replaceAll"], "mappings": ";;;;+BA<PERSON><PERSON><PERSON>;;;eAAAA;;;2DAXP;6DACE;+DAC<PERSON>;sBACO;6DACD;2BACJ;2BAE0B;;;;;;AAE/C,MAAMC,OAAOC,IAAAA,eAAS,EAACC,aAAY;AAE5B,eAAeH,gBAAgB,EACpCI,OAAO,EACPC,MAAM,EAIP;IACC,MAAMC,gBAAgBC,IAAAA,2BAAgB,EAACF;IACvC,MAAMG,YAAYC,IAAAA,uBAAY,EAACJ;IAE/B,MAAMK,YAAYC,aAAI,CAACC,IAAI,CAACR,SAAS;IACrC,MAAMS,eAAe,MAAMZ,KAAK,yBAAyB;QACvDa,KAAKJ;IACP;IACA,MAAMK,YAAYJ,aAAI,CAACC,IAAI,CAACR,SAAS;IACrC,MAAMY,eAAe,MAAMf,KAAK,yBAAyB;QACvDa,KAAKC;IACP;IACA,MAAME,iBAAiB,MAAMhB,KAAK,sBAAsB;QACtDa,KAAKV;IACP;IAEA,MAAMc,aAAa,IAAIC,eAAI,CAAC;IAC5B,MAAMC,oBAAoBC,OAAOC,IAAI,CAAChB,eAAeiB,GAAG,CAAC,CAACC,OACxDA,KAAKC,KAAK,CAAC,gBAAgBC,GAAG;IAGhC,MAAMC,aAAa,IAAIC,OACrB,CAAC,6CAA6C,EAAER,kBAAkBS,MAAM,GAAG,MAAMT,kBAAkBR,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC,EACpH;IAEF,MAAMkB,qBAA+D,EAAE;IACvE,MAAMC,eAAe,IAAIC,IACvBf,eAAeM,GAAG,CAAC,CAACU,IAAMtB,aAAI,CAACC,IAAI,CAACR,SAAS6B;IAG/C,KAAK,MAAM,CAACC,WAAWC,MAAM,IAAI;QAC/B;YAACzB;YAAWG;SAAa;QACzB;YAACE;YAAWC;SAAa;KAC1B,CAAW;QACV,MAAMoB,QAAQC,GAAG,CACfF,MAAMZ,GAAG,CAAC,OAAOe;YACf,MAAMpB,WAAWqB,OAAO;YACxB,MAAMC,WAAW7B,aAAI,CAACC,IAAI,CAACsB,WAAWI;YACtC,MAAMG,UAAU,MAAMC,WAAE,CAACC,QAAQ,CAACC,QAAQ,CAACJ,UAAU;YACrD,MAAMK,aAAaJ,QAAQK,OAAO,CAACnB,YAAY,CAACoB;gBAC9C,IAAIC,kBAAkB,CAAC,YAAY,EAAED,MAAMtB,KAAK,CAAC,KAAKC,GAAG,IAAI;gBAE7D,IAAIlB,SAAS,CAACwC,gBAAgB,EAAE;oBAC9B,OAAOC,KAAKC,SAAS,CAAC1C,SAAS,CAACwC,gBAAgB;gBAClD;gBACA,OAAOD;YACT;YAEA,MAAML,WAAE,CAACC,QAAQ,CAACQ,SAAS,CAACX,UAAUK;YAEtC,IAAIJ,YAAYI,cAAcX,cAAcnB,WAAW;gBACrDe,mBAAmBsB,IAAI,CAAC;oBAAEd;oBAAMG,SAASI;gBAAW;YACtD;YACAd,aAAasB,GAAG,CAACb;YACjBtB,WAAWoC,OAAO;QACpB;IAEJ;IACA,MAAMC,cAGD,EAAE;IAEP,oDAAoD;IACpD,KAAK,MAAM,EAAEjB,IAAI,EAAEG,OAAO,EAAE,IAAIX,mBAAoB;YAE7BQ;QADrB,mDAAmD;QACnD,MAAMkB,eAAelB,EAAAA,cAAAA,KAAKS,KAAK,CAAC,wCAAXT,WAAgC,CAAC,EAAE,KAAI;QAE5D,IAAI,CAACkB,cAAc;YACjB,MAAM,qBAEL,CAFK,IAAIC,MACR,CAAC,0DAA0D,EAAEnB,MAAM,GAD/D,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QACA,MAAMoB,UAAUC,eAAM,CACnBC,UAAU,CAAC,UACXC,MAAM,CAACpB,SACPqB,MAAM,CAAC,OACPC,SAAS,CAAC,GAAG;QAEhBR,YAAYH,IAAI,CAAC;YAAEI;YAAcE;QAAQ;QAEzC,MAAMlB,WAAW7B,aAAI,CAACC,IAAI,CAACG,WAAWuB;QACtC,MAAM0B,cAAcxB,SAASM,OAAO,CAACU,cAAcE;QAEnD3B,aAAakC,MAAM,CAACzB;QACpBT,aAAasB,GAAG,CAACW;QAEjB,MAAMtB,WAAE,CAACC,QAAQ,CAACuB,MAAM,CAAC1B,UAAUwB;IACrC;IAEA,4DAA4D;IAC5D,KAAK,IAAI1B,QAAQP,aAAc;QAC7B,MAAMU,UAAU,MAAMC,WAAE,CAACC,QAAQ,CAACC,QAAQ,CAACN,MAAM;QACjD,IAAIO,aAAaJ;QAEjB,KAAK,MAAM,EAAEe,YAAY,EAAEE,OAAO,EAAE,IAAIH,YAAa;YACnDV,aAAaA,WAAWsB,UAAU,CAACX,cAAcE;QACnD;QACA,IAAIjB,YAAYI,YAAY;YAC1B,MAAMH,WAAE,CAACC,QAAQ,CAACQ,SAAS,CAACb,MAAMO;QACpC;IACF;AACF"}