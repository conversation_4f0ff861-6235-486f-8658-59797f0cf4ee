"use strict";exports.id=3715,exports.ids=[3715],exports.modules={3843:(t,e,n)=>{n.d(e,{w:()=>a});var r=n(39819);function a(t,e){return"function"==typeof t?t(e):t&&"object"==typeof t&&r._P in t?t[r._P](e):t instanceof Date?new t.constructor(e):new Date(e)}},39819:(t,e,n)=>{n.d(e,{Cg:()=>i,_P:()=>u,my:()=>r,s0:()=>o,w4:()=>a});let r=6048e5,a=864e5,i=6e4,o=36e5,u=Symbol.for("constructDateFrom")},57241:(t,e,n)=>{n.d(e,{a:()=>a});var r=n(3843);function a(t,e){return(0,r.w)(e||t,t)}},72080:(t,e,n)=>{n.d(e,{H:()=>o});var r=n(39819),a=n(3843),i=n(57241);function o(t,e){let n,o,g=()=>(0,a.w)(e?.in,NaN),w=e?.additionalDigits??2,b=function(t){let e,n={},r=t.split(u.dateTimeDelimiter);if(r.length>2)return n;if(/:/.test(r[0])?e=r[0]:(n.date=r[0],e=r[1],u.timeZoneDelimiter.test(n.date)&&(n.date=t.split(u.timeZoneDelimiter)[0],e=t.substr(n.date.length,t.length))),e){let t=u.timezone.exec(e);t?(n.time=e.replace(t[1],""),n.timezone=t[1]):n.time=e}return n}(t);if(b.date){let t=function(t,e){let n=RegExp("^(?:(\\d{4}|[+-]\\d{"+(4+e)+"})|(\\d{2}|[+-]\\d{"+(2+e)+"})$)"),r=t.match(n);if(!r)return{year:NaN,restDateString:""};let a=r[1]?parseInt(r[1]):null,i=r[2]?parseInt(r[2]):null;return{year:null===i?a:100*i,restDateString:t.slice((r[1]||r[2]).length)}}(b.date,w);n=function(t,e){var n,r,a,i,o,u,l,d;if(null===e)return new Date(NaN);let h=t.match(s);if(!h)return new Date(NaN);let g=!!h[4],w=c(h[1]),b=c(h[2])-1,y=c(h[3]),p=c(h[4]),v=c(h[5])-1;if(g){return(n=0,r=p,a=v,r>=1&&r<=53&&a>=0&&a<=6)?function(t,e,n){let r=new Date(0);r.setUTCFullYear(t,0,4);let a=r.getUTCDay()||7;return r.setUTCDate(r.getUTCDate()+((e-1)*7+n+1-a)),r}(e,p,v):new Date(NaN)}{let t=new Date(0);return(i=e,o=b,u=y,o>=0&&o<=11&&u>=1&&u<=(m[o]||(f(i)?29:28))&&(l=e,(d=w)>=1&&d<=(f(l)?366:365)))?(t.setUTCFullYear(e,b,Math.max(w,y)),t):new Date(NaN)}}(t.restDateString,t.year)}if(!n||isNaN(+n))return g();let y=+n,p=0;if(b.time&&isNaN(p=function(t){var e,n,a;let i=t.match(l);if(!i)return NaN;let o=h(i[1]),u=h(i[2]),s=h(i[3]);return(e=o,n=u,a=s,24===e?0===n&&0===a:a>=0&&a<60&&n>=0&&n<60&&e>=0&&e<25)?o*r.s0+u*r.Cg+1e3*s:NaN}(b.time)))return g();if(b.timezone){if(isNaN(o=function(t){var e,n;if("Z"===t)return 0;let a=t.match(d);if(!a)return 0;let i="+"===a[1]?-1:1,o=parseInt(a[2]),u=a[3]&&parseInt(a[3])||0;return(e=0,(n=u)>=0&&n<=59)?i*(o*r.s0+u*r.Cg):NaN}(b.timezone)))return g()}else{let t=new Date(y+p),n=(0,i.a)(0,e?.in);return n.setFullYear(t.getUTCFullYear(),t.getUTCMonth(),t.getUTCDate()),n.setHours(t.getUTCHours(),t.getUTCMinutes(),t.getUTCSeconds(),t.getUTCMilliseconds()),n}return(0,i.a)(y+p+o,e?.in)}let u={dateTimeDelimiter:/[T ]/,timeZoneDelimiter:/[Z ]/i,timezone:/([Z+-].*)$/},s=/^-?(?:(\d{3})|(\d{2})(?:-?(\d{2}))?|W(\d{2})(?:-?(\d{1}))?|)$/,l=/^(\d{2}(?:[.,]\d*)?)(?::?(\d{2}(?:[.,]\d*)?))?(?::?(\d{2}(?:[.,]\d*)?))?$/,d=/^([+-])(\d{2})(?::?(\d{2}))?$/;function c(t){return t?parseInt(t):1}function h(t){return t&&parseFloat(t.replace(",","."))||0}let m=[31,null,31,30,31,30,31,31,30,31,30,31];function f(t){return t%400==0||t%4==0&&t%100!=0}},77606:(t,e,n)=>{n.d(e,{e:()=>i});var r=n(3843),a=n(57241);function i(t,e,n){var i=-e;let o=(0,a.a)(t,n?.in);return isNaN(i)?(0,r.w)(n?.in||t,NaN):(i&&o.setDate(o.getDate()+i),o)}},99845:(t,e,n)=>{n.d(e,{GP:()=>j});let r={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}};function a(t){return (e={})=>{let n=e.width?String(e.width):t.defaultWidth;return t.formats[n]||t.formats[t.defaultWidth]}}let i={date:a({formats:{full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},defaultWidth:"full"}),time:a({formats:{full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},defaultWidth:"full"}),dateTime:a({formats:{full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})},o={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"};function u(t){return(e,n)=>{let r;if("formatting"===(n?.context?String(n.context):"standalone")&&t.formattingValues){let e=t.defaultFormattingWidth||t.defaultWidth,a=n?.width?String(n.width):e;r=t.formattingValues[a]||t.formattingValues[e]}else{let e=t.defaultWidth,a=n?.width?String(n.width):t.defaultWidth;r=t.values[a]||t.values[e]}return r[t.argumentCallback?t.argumentCallback(e):e]}}function s(t){return(e,n={})=>{let r,a=n.width,i=a&&t.matchPatterns[a]||t.matchPatterns[t.defaultMatchWidth],o=e.match(i);if(!o)return null;let u=o[0],s=a&&t.parsePatterns[a]||t.parsePatterns[t.defaultParseWidth],l=Array.isArray(s)?function(t,e){for(let n=0;n<t.length;n++)if(e(t[n]))return n}(s,t=>t.test(u)):function(t,e){for(let n in t)if(Object.prototype.hasOwnProperty.call(t,n)&&e(t[n]))return n}(s,t=>t.test(u));return r=t.valueCallback?t.valueCallback(l):l,{value:r=n.valueCallback?n.valueCallback(r):r,rest:e.slice(u.length)}}}let l={code:"en-US",formatDistance:(t,e,n)=>{let a,i=r[t];if(a="string"==typeof i?i:1===e?i.one:i.other.replace("{{count}}",e.toString()),n?.addSuffix)if(n.comparison&&n.comparison>0)return"in "+a;else return a+" ago";return a},formatLong:i,formatRelative:(t,e,n,r)=>o[t],localize:{ordinalNumber:(t,e)=>{let n=Number(t),r=n%100;if(r>20||r<10)switch(r%10){case 1:return n+"st";case 2:return n+"nd";case 3:return n+"rd"}return n+"th"},era:u({values:{narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},defaultWidth:"wide"}),quarter:u({values:{narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},defaultWidth:"wide",argumentCallback:t=>t-1}),month:u({values:{narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},defaultWidth:"wide"}),day:u({values:{narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},defaultWidth:"wide"}),dayPeriod:u({values:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},defaultWidth:"wide",formattingValues:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},defaultFormattingWidth:"wide"})},match:{ordinalNumber:function(t){return(e,n={})=>{let r=e.match(t.matchPattern);if(!r)return null;let a=r[0],i=e.match(t.parsePattern);if(!i)return null;let o=t.valueCallback?t.valueCallback(i[0]):i[0];return{value:o=n.valueCallback?n.valueCallback(o):o,rest:e.slice(a.length)}}}({matchPattern:/^(\d+)(th|st|nd|rd)?/i,parsePattern:/\d+/i,valueCallback:t=>parseInt(t,10)}),era:s({matchPatterns:{narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^b/i,/^(a|c)/i]},defaultParseWidth:"any"}),quarter:s({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:t=>t+1}),month:s({matchPatterns:{narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},defaultParseWidth:"any"}),day:s({matchPatterns:{narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},defaultParseWidth:"any"}),dayPeriod:s({matchPatterns:{narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},defaultParseWidth:"any"})},options:{weekStartsOn:0,firstWeekContainsDate:1}},d={};var c=n(57241);function h(t){let e=(0,c.a)(t),n=new Date(Date.UTC(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours(),e.getMinutes(),e.getSeconds(),e.getMilliseconds()));return n.setUTCFullYear(e.getFullYear()),t-n}var m=n(3843),f=n(39819);function g(t,e){let n=(0,c.a)(t,e?.in);return n.setHours(0,0,0,0),n}function w(t,e){let n=e?.weekStartsOn??e?.locale?.options?.weekStartsOn??d.weekStartsOn??d.locale?.options?.weekStartsOn??0,r=(0,c.a)(t,e?.in),a=r.getDay();return r.setDate(r.getDate()-(7*(a<n)+a-n)),r.setHours(0,0,0,0),r}function b(t,e){return w(t,{...e,weekStartsOn:1})}function y(t,e){let n=(0,c.a)(t,e?.in),r=n.getFullYear(),a=(0,m.w)(n,0);a.setFullYear(r+1,0,4),a.setHours(0,0,0,0);let i=b(a),o=(0,m.w)(n,0);o.setFullYear(r,0,4),o.setHours(0,0,0,0);let u=b(o);return n.getTime()>=i.getTime()?r+1:n.getTime()>=u.getTime()?r:r-1}function p(t,e){let n=(0,c.a)(t,e?.in),r=n.getFullYear(),a=e?.firstWeekContainsDate??e?.locale?.options?.firstWeekContainsDate??d.firstWeekContainsDate??d.locale?.options?.firstWeekContainsDate??1,i=(0,m.w)(e?.in||t,0);i.setFullYear(r+1,0,a),i.setHours(0,0,0,0);let o=w(i,e),u=(0,m.w)(e?.in||t,0);u.setFullYear(r,0,a),u.setHours(0,0,0,0);let s=w(u,e);return+n>=+o?r+1:+n>=+s?r:r-1}function v(t,e){let n=Math.abs(t).toString().padStart(e,"0");return(t<0?"-":"")+n}let M={y(t,e){let n=t.getFullYear(),r=n>0?n:1-n;return v("yy"===e?r%100:r,e.length)},M(t,e){let n=t.getMonth();return"M"===e?String(n+1):v(n+1,2)},d:(t,e)=>v(t.getDate(),e.length),a(t,e){let n=t.getHours()/12>=1?"pm":"am";switch(e){case"a":case"aa":return n.toUpperCase();case"aaa":return n;case"aaaaa":return n[0];default:return"am"===n?"a.m.":"p.m."}},h:(t,e)=>v(t.getHours()%12||12,e.length),H:(t,e)=>v(t.getHours(),e.length),m:(t,e)=>v(t.getMinutes(),e.length),s:(t,e)=>v(t.getSeconds(),e.length),S(t,e){let n=e.length;return v(Math.trunc(t.getMilliseconds()*Math.pow(10,n-3)),e.length)}},D={midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},x={G:function(t,e,n){let r=+(t.getFullYear()>0);switch(e){case"G":case"GG":case"GGG":return n.era(r,{width:"abbreviated"});case"GGGGG":return n.era(r,{width:"narrow"});default:return n.era(r,{width:"wide"})}},y:function(t,e,n){if("yo"===e){let e=t.getFullYear();return n.ordinalNumber(e>0?e:1-e,{unit:"year"})}return M.y(t,e)},Y:function(t,e,n,r){let a=p(t,r),i=a>0?a:1-a;return"YY"===e?v(i%100,2):"Yo"===e?n.ordinalNumber(i,{unit:"year"}):v(i,e.length)},R:function(t,e){return v(y(t),e.length)},u:function(t,e){return v(t.getFullYear(),e.length)},Q:function(t,e,n){let r=Math.ceil((t.getMonth()+1)/3);switch(e){case"Q":return String(r);case"QQ":return v(r,2);case"Qo":return n.ordinalNumber(r,{unit:"quarter"});case"QQQ":return n.quarter(r,{width:"abbreviated",context:"formatting"});case"QQQQQ":return n.quarter(r,{width:"narrow",context:"formatting"});default:return n.quarter(r,{width:"wide",context:"formatting"})}},q:function(t,e,n){let r=Math.ceil((t.getMonth()+1)/3);switch(e){case"q":return String(r);case"qq":return v(r,2);case"qo":return n.ordinalNumber(r,{unit:"quarter"});case"qqq":return n.quarter(r,{width:"abbreviated",context:"standalone"});case"qqqqq":return n.quarter(r,{width:"narrow",context:"standalone"});default:return n.quarter(r,{width:"wide",context:"standalone"})}},M:function(t,e,n){let r=t.getMonth();switch(e){case"M":case"MM":return M.M(t,e);case"Mo":return n.ordinalNumber(r+1,{unit:"month"});case"MMM":return n.month(r,{width:"abbreviated",context:"formatting"});case"MMMMM":return n.month(r,{width:"narrow",context:"formatting"});default:return n.month(r,{width:"wide",context:"formatting"})}},L:function(t,e,n){let r=t.getMonth();switch(e){case"L":return String(r+1);case"LL":return v(r+1,2);case"Lo":return n.ordinalNumber(r+1,{unit:"month"});case"LLL":return n.month(r,{width:"abbreviated",context:"standalone"});case"LLLLL":return n.month(r,{width:"narrow",context:"standalone"});default:return n.month(r,{width:"wide",context:"standalone"})}},w:function(t,e,n,r){let a=function(t,e){let n=(0,c.a)(t,e?.in);return Math.round((w(n,e)-function(t,e){let n=e?.firstWeekContainsDate??e?.locale?.options?.firstWeekContainsDate??d.firstWeekContainsDate??d.locale?.options?.firstWeekContainsDate??1,r=p(t,e),a=(0,m.w)(e?.in||t,0);return a.setFullYear(r,0,n),a.setHours(0,0,0,0),w(a,e)}(n,e))/f.my)+1}(t,r);return"wo"===e?n.ordinalNumber(a,{unit:"week"}):v(a,e.length)},I:function(t,e,n){let r=function(t,e){let n=(0,c.a)(t,void 0);return Math.round((b(n)-function(t,e){let n=y(t,void 0),r=(0,m.w)(t,0);return r.setFullYear(n,0,4),r.setHours(0,0,0,0),b(r)}(n))/f.my)+1}(t);return"Io"===e?n.ordinalNumber(r,{unit:"week"}):v(r,e.length)},d:function(t,e,n){return"do"===e?n.ordinalNumber(t.getDate(),{unit:"date"}):M.d(t,e)},D:function(t,e,n){let r=function(t,e){let n=(0,c.a)(t,void 0);return function(t,e,n){let[r,a]=function(t,...e){let n=m.w.bind(null,t||e.find(t=>"object"==typeof t));return e.map(n)}(void 0,t,e),i=g(r),o=g(a);return Math.round((i-h(i)-(o-h(o)))/f.w4)}(n,function(t,e){let n=(0,c.a)(t,void 0);return n.setFullYear(n.getFullYear(),0,1),n.setHours(0,0,0,0),n}(n))+1}(t);return"Do"===e?n.ordinalNumber(r,{unit:"dayOfYear"}):v(r,e.length)},E:function(t,e,n){let r=t.getDay();switch(e){case"E":case"EE":case"EEE":return n.day(r,{width:"abbreviated",context:"formatting"});case"EEEEE":return n.day(r,{width:"narrow",context:"formatting"});case"EEEEEE":return n.day(r,{width:"short",context:"formatting"});default:return n.day(r,{width:"wide",context:"formatting"})}},e:function(t,e,n,r){let a=t.getDay(),i=(a-r.weekStartsOn+8)%7||7;switch(e){case"e":return String(i);case"ee":return v(i,2);case"eo":return n.ordinalNumber(i,{unit:"day"});case"eee":return n.day(a,{width:"abbreviated",context:"formatting"});case"eeeee":return n.day(a,{width:"narrow",context:"formatting"});case"eeeeee":return n.day(a,{width:"short",context:"formatting"});default:return n.day(a,{width:"wide",context:"formatting"})}},c:function(t,e,n,r){let a=t.getDay(),i=(a-r.weekStartsOn+8)%7||7;switch(e){case"c":return String(i);case"cc":return v(i,e.length);case"co":return n.ordinalNumber(i,{unit:"day"});case"ccc":return n.day(a,{width:"abbreviated",context:"standalone"});case"ccccc":return n.day(a,{width:"narrow",context:"standalone"});case"cccccc":return n.day(a,{width:"short",context:"standalone"});default:return n.day(a,{width:"wide",context:"standalone"})}},i:function(t,e,n){let r=t.getDay(),a=0===r?7:r;switch(e){case"i":return String(a);case"ii":return v(a,e.length);case"io":return n.ordinalNumber(a,{unit:"day"});case"iii":return n.day(r,{width:"abbreviated",context:"formatting"});case"iiiii":return n.day(r,{width:"narrow",context:"formatting"});case"iiiiii":return n.day(r,{width:"short",context:"formatting"});default:return n.day(r,{width:"wide",context:"formatting"})}},a:function(t,e,n){let r=t.getHours()/12>=1?"pm":"am";switch(e){case"a":case"aa":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"});case"aaa":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return n.dayPeriod(r,{width:"narrow",context:"formatting"});default:return n.dayPeriod(r,{width:"wide",context:"formatting"})}},b:function(t,e,n){let r,a=t.getHours();switch(r=12===a?D.noon:0===a?D.midnight:a/12>=1?"pm":"am",e){case"b":case"bb":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"});case"bbb":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return n.dayPeriod(r,{width:"narrow",context:"formatting"});default:return n.dayPeriod(r,{width:"wide",context:"formatting"})}},B:function(t,e,n){let r,a=t.getHours();switch(r=a>=17?D.evening:a>=12?D.afternoon:a>=4?D.morning:D.night,e){case"B":case"BB":case"BBB":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"});case"BBBBB":return n.dayPeriod(r,{width:"narrow",context:"formatting"});default:return n.dayPeriod(r,{width:"wide",context:"formatting"})}},h:function(t,e,n){if("ho"===e){let e=t.getHours()%12;return 0===e&&(e=12),n.ordinalNumber(e,{unit:"hour"})}return M.h(t,e)},H:function(t,e,n){return"Ho"===e?n.ordinalNumber(t.getHours(),{unit:"hour"}):M.H(t,e)},K:function(t,e,n){let r=t.getHours()%12;return"Ko"===e?n.ordinalNumber(r,{unit:"hour"}):v(r,e.length)},k:function(t,e,n){let r=t.getHours();return(0===r&&(r=24),"ko"===e)?n.ordinalNumber(r,{unit:"hour"}):v(r,e.length)},m:function(t,e,n){return"mo"===e?n.ordinalNumber(t.getMinutes(),{unit:"minute"}):M.m(t,e)},s:function(t,e,n){return"so"===e?n.ordinalNumber(t.getSeconds(),{unit:"second"}):M.s(t,e)},S:function(t,e){return M.S(t,e)},X:function(t,e,n){let r=t.getTimezoneOffset();if(0===r)return"Z";switch(e){case"X":return P(r);case"XXXX":case"XX":return S(r);default:return S(r,":")}},x:function(t,e,n){let r=t.getTimezoneOffset();switch(e){case"x":return P(r);case"xxxx":case"xx":return S(r);default:return S(r,":")}},O:function(t,e,n){let r=t.getTimezoneOffset();switch(e){case"O":case"OO":case"OOO":return"GMT"+k(r,":");default:return"GMT"+S(r,":")}},z:function(t,e,n){let r=t.getTimezoneOffset();switch(e){case"z":case"zz":case"zzz":return"GMT"+k(r,":");default:return"GMT"+S(r,":")}},t:function(t,e,n){return v(Math.trunc(t/1e3),e.length)},T:function(t,e,n){return v(+t,e.length)}};function k(t,e=""){let n=t>0?"-":"+",r=Math.abs(t),a=Math.trunc(r/60),i=r%60;return 0===i?n+String(a):n+String(a)+e+v(i,2)}function P(t,e){return t%60==0?(t>0?"-":"+")+v(Math.abs(t)/60,2):S(t,e)}function S(t,e=""){let n=Math.abs(t);return(t>0?"-":"+")+v(Math.trunc(n/60),2)+e+v(n%60,2)}let N=(t,e)=>{switch(t){case"P":return e.date({width:"short"});case"PP":return e.date({width:"medium"});case"PPP":return e.date({width:"long"});default:return e.date({width:"full"})}},T=(t,e)=>{switch(t){case"p":return e.time({width:"short"});case"pp":return e.time({width:"medium"});case"ppp":return e.time({width:"long"});default:return e.time({width:"full"})}},W={p:T,P:(t,e)=>{let n,r=t.match(/(P+)(p+)?/)||[],a=r[1],i=r[2];if(!i)return N(t,e);switch(a){case"P":n=e.dateTime({width:"short"});break;case"PP":n=e.dateTime({width:"medium"});break;case"PPP":n=e.dateTime({width:"long"});break;default:n=e.dateTime({width:"full"})}return n.replace("{{date}}",N(a,e)).replace("{{time}}",T(i,e))}},C=/^D+$/,Y=/^Y+$/,F=["D","DD","YY","YYYY"],O=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,q=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,H=/^'([^]*?)'?$/,z=/''/g,E=/[a-zA-Z]/;function j(t,e,n){let r=n?.locale??d.locale??l,a=n?.firstWeekContainsDate??n?.locale?.options?.firstWeekContainsDate??d.firstWeekContainsDate??d.locale?.options?.firstWeekContainsDate??1,i=n?.weekStartsOn??n?.locale?.options?.weekStartsOn??d.weekStartsOn??d.locale?.options?.weekStartsOn??0,o=(0,c.a)(t,n?.in);if(!(o instanceof Date||"object"==typeof o&&"[object Date]"===Object.prototype.toString.call(o))&&"number"!=typeof o||isNaN(+(0,c.a)(o)))throw RangeError("Invalid time value");let u=e.match(q).map(t=>{let e=t[0];return"p"===e||"P"===e?(0,W[e])(t,r.formatLong):t}).join("").match(O).map(t=>{if("''"===t)return{isToken:!1,value:"'"};let e=t[0];if("'"===e)return{isToken:!1,value:function(t){let e=t.match(H);return e?e[1].replace(z,"'"):t}(t)};if(x[e])return{isToken:!0,value:t};if(e.match(E))throw RangeError("Format string contains an unescaped latin alphabet character `"+e+"`");return{isToken:!1,value:t}});r.localize.preprocessor&&(u=r.localize.preprocessor(o,u));let s={firstWeekContainsDate:a,weekStartsOn:i,locale:r};return u.map(a=>{if(!a.isToken)return a.value;let i=a.value;return(!n?.useAdditionalWeekYearTokens&&Y.test(i)||!n?.useAdditionalDayOfYearTokens&&C.test(i))&&function(t,e,n){let r=function(t,e,n){let r="Y"===t[0]?"years":"days of the month";return`Use \`${t.toLowerCase()}\` instead of \`${t}\` (in \`${e}\`) for formatting ${r} to the input \`${n}\`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md`}(t,e,n);if(console.warn(r),F.includes(t))throw RangeError(r)}(i,e,String(t)),(0,x[i[0]])(o,i,r.localize,s)}).join("")}}};