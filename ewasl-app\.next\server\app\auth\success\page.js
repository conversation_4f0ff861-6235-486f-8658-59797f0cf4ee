(()=>{var e={};e.id=8033,e.ids=[8033],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},16189:(e,s,t)=>{"use strict";var r=t(65773);t.o(r,"useParams")&&t.d(s,{useParams:function(){return r.useParams}}),t.o(r,"usePathname")&&t.d(s,{usePathname:function(){return r.usePathname}}),t.o(r,"useRouter")&&t.d(s,{useRouter:function(){return r.useRouter}}),t.o(r,"useSearchParams")&&t.d(s,{useSearchParams:function(){return r.useSearchParams}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34504:(e,s,t)=>{Promise.resolve().then(t.bind(t,67297))},34631:e=>{"use strict";e.exports=require("tls")},47051:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i});var r=t(60687),a=t(16189),n=t(43210);function o(){(0,a.useSearchParams)();let[e,s]=(0,n.useState)(""),[t,o]=(0,n.useState)("");return(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8",children:[(0,r.jsx)("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:(0,r.jsx)("div",{className:"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100",children:(0,r.jsx)("svg",{className:"h-6 w-6 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),(0,r.jsx)("h2",{className:"mt-6 text-3xl font-extrabold text-gray-900",children:"Account Connected!"}),(0,r.jsxs)("p",{className:"mt-2 text-sm text-gray-600",children:["Your ",e?e.charAt(0).toUpperCase()+e.slice(1):"social media"," account has been successfully connected to eWasl."]}),t&&(0,r.jsx)("div",{className:"mt-4 p-3 bg-green-50 rounded-md",children:(0,r.jsxs)("p",{className:"text-sm text-green-800",children:[(0,r.jsx)("strong",{children:"Connected Account:"})," ",t]})}),(0,r.jsxs)("div",{className:"mt-6 space-y-3",children:[(0,r.jsx)("button",{onClick:()=>window.close(),className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500",children:"Close Window"}),(0,r.jsx)("a",{href:"/social",className:"w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500",children:"Go to Social Accounts"})]}),(0,r.jsxs)("div",{className:"mt-6 text-xs text-gray-500",children:[(0,r.jsxs)("p",{children:["You can now schedule posts to your ",e," account."]}),(0,r.jsx)("p",{className:"mt-1",children:"This window will close automatically in 10 seconds."})]})]})})}),(0,r.jsx)("script",{dangerouslySetInnerHTML:{__html:`
            // Auto-close window after 10 seconds
            setTimeout(() => {
              window.close();
            }, 10000);
          `}})]})}function i(){return(0,r.jsx)(n.Suspense,{fallback:(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8",children:(0,r.jsx)("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:(0,r.jsx)("div",{className:"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto"}),(0,r.jsx)("p",{className:"mt-4 text-sm text-gray-600",children:"Loading..."})]})})})}),children:(0,r.jsx)(o,{})})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67297:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ewasl\\\\eWasl.com_Cursor_Digital_Ocean\\\\ewasl-app\\\\src\\\\app\\\\auth\\\\success\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Ewasl\\eWasl.com_Cursor_Digital_Ocean\\ewasl-app\\src\\app\\auth\\success\\page.tsx","default")},70440:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});var r=t(31658);let a=async e=>[{type:"image/x-icon",sizes:"48x48",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},85010:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>o.a,__next_app__:()=>d,pages:()=>u,routeModule:()=>p,tree:()=>l});var r=t(65239),a=t(48088),n=t(88170),o=t.n(n),i=t(30893),c={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);t.d(s,c);let l={children:["",{children:["auth",{children:["success",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,67297)),"C:\\Users\\<USER>\\Desktop\\Ewasl\\eWasl.com_Cursor_Digital_Ocean\\ewasl-app\\src\\app\\auth\\success\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,45399)),"C:\\Users\\<USER>\\Desktop\\Ewasl\\eWasl.com_Cursor_Digital_Ocean\\ewasl-app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,u=["C:\\Users\\<USER>\\Desktop\\Ewasl\\eWasl.com_Cursor_Digital_Ocean\\ewasl-app\\src\\app\\auth\\success\\page.tsx"],d={require:t,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/auth/success/page",pathname:"/auth/success",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},98056:(e,s,t)=>{Promise.resolve().then(t.bind(t,47051))}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[4243,6167,2215,1658,9038,9908],()=>t(85010));module.exports=r})();