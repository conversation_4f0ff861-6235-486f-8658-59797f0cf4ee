(()=>{var e={};e.id=7491,e.ids=[7491],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},16189:(e,r,s)=>{"use strict";var t=s(65773);s.o(t,"useParams")&&s.d(r,{useParams:function(){return t.useParams}}),s.o(t,"usePathname")&&s.d(r,{usePathname:function(){return t.usePathname}}),s.o(t,"useRouter")&&s.d(r,{useRouter:function(){return t.useRouter}}),s.o(t,"useSearchParams")&&s.d(r,{useSearchParams:function(){return t.useSearchParams}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},36901:(e,r,s)=>{Promise.resolve().then(s.bind(s,52361))},42981:(e,r,s)=>{Promise.resolve().then(s.bind(s,73067))},52361:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>i});var t=s(60687);s(43210);var a=s(16189);function i(){return(0,a.useRouter)(),(0,t.jsx)("div",{className:"flex min-h-screen items-center justify-center bg-gray-100",children:(0,t.jsxs)("div",{className:"bg-white p-8 rounded-lg shadow-md max-w-md w-full text-center",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold mb-4",children:"جاري التوجيه..."}),(0,t.jsx)("p",{className:"mb-4",children:"سيتم توجيهك إلى لوحة التحكم خلال لحظات."}),(0,t.jsx)("div",{className:"animate-pulse bg-blue-100 h-2 w-full rounded-full",children:(0,t.jsx)("div",{className:"bg-blue-500 h-2 w-1/3 rounded-full"})})]})})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>a});var t=s(31658);let a=async e=>[{type:"image/x-icon",sizes:"48x48",url:(0,t.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},73067:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ewasl\\\\eWasl.com_Cursor_Digital_Ocean\\\\ewasl-app\\\\src\\\\app\\\\bypass\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Ewasl\\eWasl.com_Cursor_Digital_Ocean\\ewasl-app\\src\\app\\bypass\\page.tsx","default")},73490:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>n.a,__next_app__:()=>d,pages:()=>p,routeModule:()=>c,tree:()=>l});var t=s(65239),a=s(48088),i=s(88170),n=s.n(i),o=s(30893),u={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(u[e]=()=>o[e]);s.d(r,u);let l={children:["",{children:["bypass",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,73067)),"C:\\Users\\<USER>\\Desktop\\Ewasl\\eWasl.com_Cursor_Digital_Ocean\\ewasl-app\\src\\app\\bypass\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,45399)),"C:\\Users\\<USER>\\Desktop\\Ewasl\\eWasl.com_Cursor_Digital_Ocean\\ewasl-app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,p=["C:\\Users\\<USER>\\Desktop\\Ewasl\\eWasl.com_Cursor_Digital_Ocean\\ewasl-app\\src\\app\\bypass\\page.tsx"],d={require:s,loadChunk:()=>Promise.resolve()},c=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/bypass/page",pathname:"/bypass",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[4243,6167,2215,1658,9038,9908],()=>s(73490));module.exports=t})();