(()=>{var e={};e.id=4680,e.ids=[4680],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},16189:(e,r,t)=>{"use strict";var s=t(65773);t.o(s,"useParams")&&t.d(r,{useParams:function(){return s.useParams}}),t.o(s,"usePathname")&&t.d(r,{usePathname:function(){return s.usePathname}}),t.o(s,"useRouter")&&t.d(r,{useRouter:function(){return s.useRouter}}),t.o(s,"useSearchParams")&&t.d(r,{useSearchParams:function(){return s.useSearchParams}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29523:(e,r,t)=>{"use strict";t.d(r,{$:()=>o,r:()=>l});var s=t(60687);t(43210);var a=t(8730),i=t(24224),n=t(4780);let l=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o({className:e,variant:r,size:t,asChild:i=!1,...o}){let c=i?a.DX:"button";return(0,s.jsx)(c,{"data-slot":"button",className:(0,n.cn)(l({variant:r,size:t,className:e})),...o})}},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},44419:(e,r,t)=>{Promise.resolve().then(t.bind(t,92482))},44493:(e,r,t)=>{"use strict";t.d(r,{BT:()=>c,Wu:()=>d,ZB:()=>o,Zp:()=>n,aR:()=>l,wL:()=>u});var s=t(60687),a=t(43210),i=t(4780);let n=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...r}));n.displayName="Card";let l=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...r}));l.displayName="CardHeader";let o=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",e),...r}));o.displayName="CardTitle";let c=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",e),...r}));c.displayName="CardDescription";let d=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("p-6 pt-0",e),...r}));d.displayName="CardContent";let u=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("flex items-center p-6 pt-0",e),...r}));u.displayName="CardFooter"},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var s=t(31658);let a=async e=>[{type:"image/x-icon",sizes:"48x48",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},80013:(e,r,t)=>{"use strict";t.d(r,{J:()=>c});var s=t(60687),a=t(43210),i=t(78148),n=t(24224),l=t(4780);let o=(0,n.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)(i.b,{ref:t,className:(0,l.cn)(o(),e),...r}));c.displayName=i.b.displayName},81630:e=>{"use strict";e.exports=require("http")},84747:(e,r,t)=>{Promise.resolve().then(t.bind(t,87578))},87578:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ewasl\\\\eWasl.com_Cursor_Digital_Ocean\\\\ewasl-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Ewasl\\eWasl.com_Cursor_Digital_Ocean\\ewasl-app\\src\\app\\auth\\signin\\page.tsx","default")},87979:(e,r,t)=>{"use strict";t.d(r,{As:()=>l,Nu:()=>o,OH:()=>c});var s=t(43210),a=t(16189),i=t(35622);function n(){let e="https://nnxfzhxqzmriggulsudr.supabase.co",r="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.1qBlXLOqGtJX_LFRC7XihOpwCtD1U1I2_jdyJ8sq_8w";if(!e||!r)throw Error("Missing Supabase environment variables");return(0,i.createBrowserClient)(e,r)}function l(){let[e,r]=(0,s.useState)({user:null,loading:!0,error:null}),t=(0,a.useRouter)(),i=async()=>{try{r(e=>({...e,loading:!0}));let e=n(),{error:s}=await e.auth.signOut();s?(console.error("Sign out error:",s),r(e=>({...e,loading:!1,error:s.message}))):(r({user:null,loading:!1,error:null}),t.push("/auth/signin"))}catch(e){console.error("Sign out failed:",e),r(r=>({...r,loading:!1,error:e.message||"Sign out failed"}))}},l=async()=>{try{r(e=>({...e,loading:!0}));let e=await n(),{data:{user:t},error:s}=await e.auth.getUser();s?r({user:null,loading:!1,error:s.message}):r({user:t,loading:!1,error:null})}catch(e){r({user:null,loading:!1,error:e.message||"Session refresh failed"})}};return{user:e.user,loading:e.loading,error:e.error,isAuthenticated:!!e.user,signOut:i,refreshSession:l}}function o(){let{user:e,loading:r,error:t}=l();return(0,a.useRouter)(),{user:e,loading:r,error:t}}function c(){let{user:e,loading:r}=l();return(0,a.useRouter)(),{user:e,loading:r}}},89667:(e,r,t)=>{"use strict";t.d(r,{p:()=>n});var s=t(60687),a=t(43210),i=t(4780);let n=a.forwardRef(({className:e,type:r,...t},a)=>(0,s.jsx)("input",{type:r,className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:a,...t}));n.displayName="Input"},91645:e=>{"use strict";e.exports=require("net")},91821:(e,r,t)=>{"use strict";t.d(r,{Fc:()=>o,TN:()=>d,XL:()=>c});var s=t(60687),a=t(43210),i=t(24224),n=t(4780);let l=(0,i.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),o=a.forwardRef(({className:e,variant:r,...t},a)=>(0,s.jsx)("div",{ref:a,role:"alert",className:(0,n.cn)(l({variant:r}),e),...t}));o.displayName="Alert";let c=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("h5",{ref:t,className:(0,n.cn)("mb-1 font-medium leading-none tracking-tight",e),...r}));c.displayName="AlertTitle";let d=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,n.cn)("text-sm [&_p]:leading-relaxed",e),...r}));d.displayName="AlertDescription"},92116:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c});var s=t(65239),a=t(48088),i=t(88170),n=t.n(i),l=t(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);t.d(r,o);let c={children:["",{children:["auth",{children:["signin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,87578)),"C:\\Users\\<USER>\\Desktop\\Ewasl\\eWasl.com_Cursor_Digital_Ocean\\ewasl-app\\src\\app\\auth\\signin\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,45399)),"C:\\Users\\<USER>\\Desktop\\Ewasl\\eWasl.com_Cursor_Digital_Ocean\\ewasl-app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Desktop\\Ewasl\\eWasl.com_Cursor_Digital_Ocean\\ewasl-app\\src\\app\\auth\\signin\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/auth/signin/page",pathname:"/auth/signin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},92482:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>q});var s=t(60687),a=t(43210),i=t(27605),n=t(63442),l=t(9275),o=t(16189),c=t(85814),d=t.n(c),u=t(35622),p=t(87979),m=t(29523),h=t(89667),g=t(80013),x=t(44493),f=t(91821),v={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},b=a.createContext&&a.createContext(v),y=["attr","size","title"];function w(){return(w=Object.assign?Object.assign.bind():function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var s in t)Object.prototype.hasOwnProperty.call(t,s)&&(e[s]=t[s])}return e}).apply(this,arguments)}function j(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);r&&(s=s.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,s)}return t}function N(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?j(Object(t),!0).forEach(function(r){var s,a,i;s=e,a=r,i=t[r],(a=function(e){var r=function(e,r){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var s=t.call(e,r||"default");if("object"!=typeof s)return s;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)}(e,"string");return"symbol"==typeof r?r:r+""}(a))in s?Object.defineProperty(s,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):s[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):j(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function I(e){return r=>a.createElement(C,w({attr:N({},e.attr)},r),function e(r){return r&&r.map((r,t)=>a.createElement(r.tag,N({key:t},r.attr),e(r.child)))}(e.child))}function C(e){var r=r=>{var t,{attr:s,size:i,title:n}=e,l=function(e,r){if(null==e)return{};var t,s,a=function(e,r){if(null==e)return{};var t={};for(var s in e)if(Object.prototype.hasOwnProperty.call(e,s)){if(r.indexOf(s)>=0)continue;t[s]=e[s]}return t}(e,r);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(s=0;s<i.length;s++)t=i[s],!(r.indexOf(t)>=0)&&Object.prototype.propertyIsEnumerable.call(e,t)&&(a[t]=e[t])}return a}(e,y),o=i||r.size||"1em";return r.className&&(t=r.className),e.className&&(t=(t?t+" ":"")+e.className),a.createElement("svg",w({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},r.attr,s,l,{className:t,style:N(N({color:e.color||r.color},r.style),e.style),height:o,width:o,xmlns:"http://www.w3.org/2000/svg"}),n&&a.createElement("title",null,n),e.children)};return void 0!==b?a.createElement(b.Consumer,null,e=>r(e)):r(v)}function O(e){return I({tag:"svg",attr:{version:"1.1",x:"0px",y:"0px",viewBox:"0 0 48 48",enableBackground:"new 0 0 48 48"},child:[{tag:"path",attr:{fill:"#FFC107",d:"M43.611,20.083H42V20H24v8h11.303c-1.649,4.657-6.08,8-11.303,8c-6.627,0-12-5.373-12-12\r\n	c0-6.627,5.373-12,12-12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657C34.046,6.053,29.268,4,24,4C12.955,4,4,12.955,4,24\r\n	c0,11.045,8.955,20,20,20c11.045,0,20-8.955,20-20C44,22.659,43.862,21.35,43.611,20.083z"},child:[]},{tag:"path",attr:{fill:"#FF3D00",d:"M6.306,14.691l6.571,4.819C14.655,15.108,18.961,12,24,12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657\r\n	C34.046,6.053,29.268,4,24,4C16.318,4,9.656,8.337,6.306,14.691z"},child:[]},{tag:"path",attr:{fill:"#4CAF50",d:"M24,44c5.166,0,9.86-1.977,13.409-5.192l-6.19-5.238C29.211,35.091,26.715,36,24,36\r\n	c-5.202,0-9.619-3.317-11.283-7.946l-6.522,5.025C9.505,39.556,16.227,44,24,44z"},child:[]},{tag:"path",attr:{fill:"#1976D2",d:"M43.611,20.083H42V20H24v8h11.303c-0.792,2.237-2.231,4.166-4.087,5.571\r\n	c0.001-0.001,0.002-0.001,0.003-0.002l6.19,5.238C36.971,39.205,44,34,44,24C44,22.659,43.862,21.35,43.611,20.083z"},child:[]}]})(e)}function _(e){return I({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M504 256C504 119 393 8 256 8S8 119 8 256c0 123.78 90.69 226.38 209.25 245V327.69h-63V256h63v-54.64c0-62.15 37-96.48 93.67-96.48 27.14 0 55.52 4.84 55.52 4.84v61h-31.28c-30.8 0-40.41 19.12-40.41 38.73V256h68.78l-11 71.69h-57.78V501C413.31 482.38 504 379.78 504 256z"},child:[]}]})(e)}function k(e){return I({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M459.37 151.716c.325 4.548.325 9.097.325 13.645 0 138.72-105.583 298.558-298.558 298.558-59.452 0-114.68-17.219-161.137-47.106 8.447.974 16.568 1.299 25.34 1.299 49.055 0 94.213-16.568 130.274-44.832-46.132-.975-84.792-31.188-98.112-72.772 6.498.974 12.995 1.624 19.818 1.624 9.421 0 18.843-1.3 27.614-3.573-48.081-9.747-84.143-51.98-84.143-102.985v-1.299c13.969 7.797 30.214 12.67 47.431 13.319-28.264-18.843-46.781-51.005-46.781-87.391 0-19.492 5.197-37.36 14.294-52.954 51.655 63.675 129.3 105.258 216.365 109.807-1.624-7.797-2.599-15.918-2.599-24.04 0-57.828 46.782-104.934 104.934-104.934 30.213 0 57.502 12.67 76.67 33.137 23.715-4.548 46.456-13.32 66.599-25.34-7.798 24.366-24.366 44.833-46.132 57.827 21.117-2.273 41.584-8.122 60.426-16.243-14.292 20.791-32.161 39.308-52.628 54.253z"},child:[]}]})(e)}let z=l.Ik({email:l.Yj().email({message:"البريد الإلكتروني غير صالح"}),password:l.Yj().min(6,{message:"كلمة المرور يجب أن تكون 6 أحرف على الأقل"})});function P(){let e=(0,o.useRouter)(),r=(0,o.useSearchParams)(),t=r?.get("redirect")||"/dashboard",l=r?.get("error"),[c,v]=(0,a.useState)(""),[b,y]=(0,a.useState)(""),[w,j]=(0,a.useState)(!1),[N,I]=(0,a.useState)(null);(0,p.OH)(),(0,i.mN)({resolver:(0,n.u)(z),defaultValues:{email:"",password:""}});let C=async r=>{if(r.preventDefault(),!c||!b)return void I("يرجى إدخال البريد الإلكتروني وكلمة المرور");j(!0),I(null);try{let r=(0,u.createBrowserClient)("https://nnxfzhxqzmriggulsudr.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.1qBlXLOqGtJX_LFRC7XihOpwCtD1U1I2_jdyJ8sq_8w"),{data:s,error:a}=await r.auth.signInWithPassword({email:c,password:b});if(a){console.error("Sign in error:",a),I("Invalid login credentials"===a.message?"بيانات تسجيل الدخول غير صحيحة":"حدث خطأ في تسجيل الدخول. يرجى المحاولة مرة أخرى.");return}s.user&&e.push(t)}catch(e){console.error("Unexpected sign in error:",e),I("حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.")}finally{j(!1)}},P=async()=>{j(!0),I(null);try{let e=(0,u.createBrowserClient)("https://nnxfzhxqzmriggulsudr.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.1qBlXLOqGtJX_LFRC7XihOpwCtD1U1I2_jdyJ8sq_8w"),{data:r,error:s}=await e.auth.signInWithOAuth({provider:"google",options:{redirectTo:`${window.location.origin}/auth/callback?redirect=${encodeURIComponent(t)}`,queryParams:{access_type:"offline",prompt:"consent"}}});if(s){console.error("Google sign in error:",s),I("حدث خطأ في تسجيل الدخول بواسطة Google. يرجى المحاولة مرة أخرى."),j(!1);return}r.url&&(window.location.href=r.url)}catch(e){console.error("Google sign in error:",e),I("حدث خطأ في تسجيل الدخول بواسطة Google. يرجى المحاولة مرة أخرى."),j(!1)}},q=async e=>{j(!0),I(null);try{let r=(0,u.createBrowserClient)("https://nnxfzhxqzmriggulsudr.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.1qBlXLOqGtJX_LFRC7XihOpwCtD1U1I2_jdyJ8sq_8w"),{data:s,error:a}=await r.auth.signInWithOAuth({provider:e,options:{redirectTo:`${window.location.origin}/auth/callback?redirect=${encodeURIComponent(t)}`,queryParams:{access_type:"offline",prompt:"consent"}}});if(a){console.error(`${e} sign in error:`,a),I(`حدث خطأ في تسجيل الدخول بواسطة ${e}. يرجى المحاولة مرة أخرى.`),j(!1);return}s.url&&(window.location.href=s.url)}catch(r){console.error(`${e} sign in error:`,r),I(`حدث خطأ في تسجيل الدخول بواسطة ${e}. يرجى المحاولة مرة أخرى.`),j(!1)}},J=(e=>{switch(e){case"oauth_error":return"حدث خطأ في تسجيل الدخول بواسطة Google. يرجى المحاولة مرة أخرى.";case"oauth_init_error":return"لم نتمكن من بدء عملية تسجيل الدخول بواسطة Google. يرجى التحقق من الإعدادات.";case"no_oauth_url":return"حدث خطأ في إعداد Google OAuth. يرجى الاتصال بالدعم الفني.";case"oauth_server_error":return"خطأ في الخادم أثناء تسجيل الدخول بواسطة Google.";case"auth_error":return"حدث خطأ في المصادقة. يرجى تسجيل الدخول مرة أخرى.";case"no_code":return"لم يتم تلقي رمز التفويض من Google.";default:return null}})(l);return(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)(x.Zp,{className:"w-full max-w-md",children:[(0,s.jsxs)(x.aR,{className:"text-center",children:[(0,s.jsx)(x.ZB,{className:"text-2xl font-bold text-gray-900",children:"مرحباً بك في eWasl"}),(0,s.jsx)(x.BT,{className:"text-gray-600",children:"سجل دخولك لإدارة حساباتك على وسائل التواصل الاجتماعي"})]}),(0,s.jsxs)(x.Wu,{className:"space-y-6",children:[(J||N)&&(0,s.jsx)(f.Fc,{variant:"destructive",children:(0,s.jsx)(f.TN,{children:J||N})}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)(m.$,{onClick:P,disabled:w,variant:"outline",className:"w-full flex items-center justify-center gap-3 py-3 border-gray-300 hover:bg-gray-50",children:[(0,s.jsx)(O,{className:"w-5 h-5"}),(0,s.jsx)("span",{className:"text-gray-700",children:"تسجيل الدخول بواسطة Google"})]}),(0,s.jsxs)(m.$,{onClick:()=>q("facebook"),disabled:w,variant:"outline",className:"w-full flex items-center justify-center gap-3 py-3 border-gray-300 hover:bg-blue-50 text-blue-600",children:[(0,s.jsx)(_,{className:"w-5 h-5"}),(0,s.jsx)("span",{children:"تسجيل الدخول بواسطة Facebook"})]}),(0,s.jsxs)(m.$,{onClick:()=>q("twitter"),disabled:w,variant:"outline",className:"w-full flex items-center justify-center gap-3 py-3 border-gray-300 hover:bg-blue-50 text-blue-400",children:[(0,s.jsx)(k,{className:"w-5 h-5"}),(0,s.jsx)("span",{children:"تسجيل الدخول بواسطة Twitter"})]})]}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,s.jsx)("div",{className:"w-full border-t border-gray-300"})}),(0,s.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,s.jsx)("span",{className:"px-2 bg-white text-gray-500",children:"أو"})})]}),(0,s.jsxs)("form",{onSubmit:C,className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(g.J,{htmlFor:"email",className:"text-right block text-sm font-medium text-gray-700",children:"البريد الإلكتروني"}),(0,s.jsx)(h.p,{id:"email",type:"email",value:c,onChange:e=>v(e.target.value),required:!0,className:"mt-1 text-right",placeholder:"<EMAIL>",disabled:w})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(g.J,{htmlFor:"password",className:"text-right block text-sm font-medium text-gray-700",children:"كلمة المرور"}),(0,s.jsx)(h.p,{id:"password",type:"password",value:b,onChange:e=>y(e.target.value),required:!0,className:"mt-1 text-right",placeholder:"••••••••",disabled:w})]}),(0,s.jsx)(m.$,{type:"submit",disabled:w,className:"w-full bg-blue-600 hover:bg-blue-700 text-white py-3",children:w?"جاري تسجيل الدخول...":"تسجيل الدخول"})]})]}),(0,s.jsxs)(x.wL,{className:"text-center space-y-2",children:[(0,s.jsxs)("p",{className:"text-sm text-gray-600",children:["ليس لديك حساب؟"," ",(0,s.jsx)(d(),{href:`/auth/signup${"/dashboard"!==t?`?redirect=${encodeURIComponent(t)}`:""}`,className:"text-blue-600 hover:text-blue-500 font-medium",children:"إنشاء حساب جديد"})]}),(0,s.jsx)("p",{className:"text-sm",children:(0,s.jsx)(d(),{href:"/auth/forgot-password",className:"text-blue-600 hover:text-blue-500",children:"نسيت كلمة المرور؟"})})]})]})})}function q(){return(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-purple-50 flex items-center justify-center p-4",children:(0,s.jsxs)("div",{className:"w-full max-w-lg",children:[(0,s.jsxs)("div",{className:"text-center mb-10",children:[(0,s.jsx)("div",{className:"w-20 h-20 bg-gradient-to-r from-blue-600 to-purple-600 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-2xl",children:(0,s.jsx)("span",{className:"text-3xl font-bold text-white",children:"eW"})}),(0,s.jsx)("h1",{className:"text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-3",children:"eWasl"}),(0,s.jsx)("p",{className:"text-gray-600 text-lg",children:"منصة إدارة وسائل التواصل الاجتماعي"}),(0,s.jsx)("p",{className:"text-sm text-gray-500 mt-2",children:"الإصدار 7.0 - تصميم حديث ومتطور"})]}),(0,s.jsxs)("div",{className:"bg-white/80 backdrop-blur-xl rounded-3xl shadow-2xl p-10 border border-white/50",children:[(0,s.jsxs)("div",{className:"text-center mb-8",children:[(0,s.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-3",children:"مرحباً بعودتك"}),(0,s.jsx)("p",{className:"text-gray-600 text-lg",children:"سجل دخولك إلى لوحة التحكم"})]}),(0,s.jsx)(a.Suspense,{fallback:(0,s.jsx)("div",{children:"جاري التحميل..."}),children:(0,s.jsx)(P,{})})]})]})})}},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4243,6167,2215,1658,5814,7825,9038,9908],()=>t(92116));module.exports=s})();