(()=>{var e={};e.id=9709,e.ids=[9709],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8073:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ewasl\\\\eWasl.com_Cursor_Digital_Ocean\\\\ewasl-app\\\\src\\\\app\\\\test-simple-post\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Ewasl\\eWasl.com_Cursor_Digital_Ocean\\ewasl-app\\src\\app\\test-simple-post\\page.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21986:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>o.a,__next_app__:()=>c,pages:()=>d,routeModule:()=>u,tree:()=>p});var r=s(65239),a=s(48088),i=s(88170),o=s.n(i),n=s(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);s.d(t,l);let p={children:["",{children:["test-simple-post",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,8073)),"C:\\Users\\<USER>\\Desktop\\Ewasl\\eWasl.com_Cursor_Digital_Ocean\\ewasl-app\\src\\app\\test-simple-post\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,45399)),"C:\\Users\\<USER>\\Desktop\\Ewasl\\eWasl.com_Cursor_Digital_Ocean\\ewasl-app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Desktop\\Ewasl\\eWasl.com_Cursor_Digital_Ocean\\ewasl-app\\src\\app\\test-simple-post\\page.tsx"],c={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/test-simple-post/page",pathname:"/test-simple-post",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30291:(e,t,s)=>{Promise.resolve().then(s.bind(s,69867))},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69867:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});var r=s(60687),a=s(43210);function i(){let[e,t]=(0,a.useState)(null),[s,i]=(0,a.useState)(!1),o=async()=>{i(!0),t(null);try{let e=await fetch("/api/test-post-simple",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({content:"\uD83E\uDDEA Simple API Test - Basic Post Creation\n\nTesting if basic post creation works without complex logic.\n\nTimestamp: "+new Date().toISOString()})}),s=await e.json();t({status:e.status,data:s})}catch(e){t({status:"ERROR",data:{error:e.message}})}finally{i(!1)}};return(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 p-8",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold mb-8",children:"\uD83E\uDDEA Simple Post API Test"}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 mb-6",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Test Simple Post Creation"}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"This test will call the simplified API endpoint to create a basic post without complex logic."}),(0,r.jsx)("button",{onClick:o,disabled:s,className:"bg-blue-500 hover:bg-blue-600 disabled:bg-gray-400 text-white px-6 py-2 rounded-lg font-medium",children:s?"Testing...":"Test Simple Post Creation"})]}),e&&(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Test Result"}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("span",{className:"font-medium",children:"Status: "}),(0,r.jsx)("span",{className:`px-2 py-1 rounded text-sm ${200===e.status?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:e.status})]}),(0,r.jsx)("div",{className:"bg-gray-100 rounded p-4 overflow-auto",children:(0,r.jsx)("pre",{className:"text-sm",children:JSON.stringify(e.data,null,2)})})]})]})})}},70203:(e,t,s)=>{Promise.resolve().then(s.bind(s,8073))},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(31658);let a=async e=>[{type:"image/x-icon",sizes:"48x48",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4243,6167,2215,1658,9038,9908],()=>s(21986));module.exports=r})();