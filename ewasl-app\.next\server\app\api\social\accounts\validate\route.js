"use strict";(()=>{var e={};e.id=1391,e.ids=[1391],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{e.exports=require("punycode")},27910:e=>{e.exports=require("stream")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{e.exports=require("tls")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},52102:(e,t,a)=>{a.r(t),a.d(t,{patchFetch:()=>k,routeModule:()=>g,serverHooks:()=>w,workAsyncStorage:()=>_,workUnitAsyncStorage:()=>m});var r={};a.r(r),a.d(r,{GET:()=>h,POST:()=>f});var s=a(96559),o=a(48088),n=a(37719),i=a(32190),c=a(82087),l=a(63511);class u{constructor(){try{this.facebookService=(0,c.p)()}catch(e){console.warn("Facebook OAuth service not available:",e)}}async validateAccount(e){try{switch(e.platform){case"FACEBOOK":case"INSTAGRAM":return await this.validateFacebookAccount(e);case"TWITTER":return await this.validateTwitterAccount(e);case"LINKEDIN":return await this.validateLinkedInAccount(e);case"TIKTOK":return await this.validateTikTokAccount(e);default:return{isValid:!1,status:ConnectionStatus.ERROR,error:`Unsupported platform: ${e.platform}`}}}catch(t){return console.error(`Error validating ${e.platform} account:`,t),{isValid:!1,status:ConnectionStatus.ERROR,error:t instanceof Error?t.message:"Unknown validation error"}}}async validateFacebookAccount(e){if(!this.facebookService)return{isValid:!1,status:ConnectionStatus.ERROR,error:"Facebook service not configured"};try{if(e.expiresAt&&e.expiresAt<new Date)return{isValid:!1,status:ConnectionStatus.EXPIRED,error:"Token has expired",needsRefresh:!0};let t=await this.facebookService.validateToken(e.accessToken);if(t&&t.id)return{isValid:!0,status:ConnectionStatus.CONNECTED};return{isValid:!1,status:ConnectionStatus.ERROR,error:"Invalid token response"}}catch(t){let e=t instanceof Error?t.message:"Unknown error";if(e.includes("expired")||e.includes("invalid"))return{isValid:!1,status:ConnectionStatus.EXPIRED,error:e,needsRefresh:!0};return{isValid:!1,status:ConnectionStatus.ERROR,error:e}}}async validateTwitterAccount(e){return e.expiresAt&&e.expiresAt<new Date?{isValid:!1,status:ConnectionStatus.EXPIRED,needsRefresh:!0}:{isValid:!0,status:ConnectionStatus.CONNECTED}}async validateLinkedInAccount(e){return e.expiresAt&&e.expiresAt<new Date?{isValid:!1,status:ConnectionStatus.EXPIRED,needsRefresh:!0}:{isValid:!0,status:ConnectionStatus.CONNECTED}}async validateTikTokAccount(e){return e.expiresAt&&e.expiresAt<new Date?{isValid:!1,status:ConnectionStatus.EXPIRED,needsRefresh:!0}:{isValid:!0,status:ConnectionStatus.CONNECTED}}async validateAccounts(e){let t=new Map;for(let a=0;a<e.length;a+=5){let r=e.slice(a,a+5),s=r.map(async e=>{let t=await this.validateAccount(e);return{accountId:e.id,result:t}}),o=await Promise.allSettled(s);o.forEach(e=>{if("fulfilled"===e.status)t.set(e.value.accountId,e.value.result);else{let a=r[o.indexOf(e)]?.id||"unknown";t.set(a,{isValid:!1,status:ConnectionStatus.ERROR,error:"Validation failed"})}})}return t}async updateAccountStatus(e,t){try{let a=(0,l.U)(),r={connection_status:t.status,last_validated_at:new Date().toISOString(),updated_at:new Date().toISOString()};t.newToken&&(r.access_token=t.newToken),t.error&&(r.metadata={lastError:t.error,lastErrorAt:new Date().toISOString()});let{error:s}=await a.from("social_accounts").update(r).eq("id",e);s&&console.error("Error updating account status:",s)}catch(e){console.error("Error updating account status in database:",e)}}async validateUserAccounts(e){try{let t=(0,l.U)(),{data:a,error:r}=await t.from("social_accounts").select("*").eq("user_id",e);if(r||!a)return void console.error("Error fetching user accounts:",r);let s=a.map(e=>({id:e.id,platform:e.platform,accessToken:e.access_token,refreshToken:e.refresh_token,expiresAt:e.expires_at?new Date(e.expires_at):void 0,lastValidatedAt:e.last_validated_at?new Date(e.last_validated_at):void 0})),o=await this.validateAccounts(s),n=Array.from(o.entries()).map(([e,t])=>this.updateAccountStatus(e,t));await Promise.allSettled(n)}catch(e){console.error("Error validating user accounts:",e)}}}let d=new u;var p=a(2507);async function f(e){try{let{accountIds:t,userId:a}=await e.json();if(!a)return i.NextResponse.json({error:"User ID is required"},{status:400});if(t&&Array.isArray(t)){let e=(0,p.createClient)(),{data:r,error:s}=await e.from("social_accounts").select("*").eq("user_id",a).in("id",t);if(s)return i.NextResponse.json({error:"Failed to fetch accounts"},{status:500});let o=r.map(e=>({id:e.id,platform:e.platform,accessToken:e.access_token,refreshToken:e.refresh_token,expiresAt:e.expires_at?new Date(e.expires_at):void 0,lastValidatedAt:e.last_validated_at?new Date(e.last_validated_at):void 0})),n=await d.validateAccounts(o),c=Array.from(n.entries()).map(([e,t])=>d.updateAccountStatus(e,t));await Promise.allSettled(c);let l=Array.from(n.entries()).map(([e,t])=>({accountId:e,isValid:t.isValid,status:t.status,error:t.error,needsRefresh:t.needsRefresh}));return i.NextResponse.json({success:!0,results:l,validatedCount:l.length})}return await d.validateUserAccounts(a),i.NextResponse.json({success:!0,message:"All accounts validated successfully"})}catch(e){return console.error("Account validation API error:",e),i.NextResponse.json({error:"Validation failed",message:e instanceof Error?e.message:"Unknown error"},{status:500})}}async function h(e){try{let{searchParams:t}=new URL(e.url),a=t.get("userId"),r=t.get("accountId");if(!a)return i.NextResponse.json({error:"User ID is required"},{status:400});let s=(0,p.createClient)();if(r){let{data:e,error:t}=await s.from("social_accounts").select("*").eq("user_id",a).eq("id",r).single();if(t||!e)return i.NextResponse.json({error:"Account not found"},{status:404});let o={id:e.id,platform:e.platform,accessToken:e.access_token,refreshToken:e.refresh_token,expiresAt:e.expires_at?new Date(e.expires_at):void 0,lastValidatedAt:e.last_validated_at?new Date(e.last_validated_at):void 0},n=await d.validateAccount(o);return await d.updateAccountStatus(r,n),i.NextResponse.json({accountId:r,isValid:n.isValid,status:n.status,error:n.error,needsRefresh:n.needsRefresh,lastValidated:new Date().toISOString()})}let{data:o,error:n}=await s.from("social_accounts").select("id, platform, connection_status, last_validated_at, expires_at").eq("user_id",a);if(n)return i.NextResponse.json({error:"Failed to fetch accounts"},{status:500});let c=o.map(e=>({accountId:e.id,platform:e.platform,status:e.connection_status,lastValidated:e.last_validated_at,expiresAt:e.expires_at,needsValidation:!e.last_validated_at||new Date(e.last_validated_at)<new Date(Date.now()-36e5)}));return i.NextResponse.json({accounts:c,totalAccounts:o.length,needsValidation:c.filter(e=>e.needsValidation).length})}catch(e){return console.error("Account validation status API error:",e),i.NextResponse.json({error:"Failed to get validation status",message:e instanceof Error?e.message:"Unknown error"},{status:500})}}let g=new s.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/social/accounts/validate/route",pathname:"/api/social/accounts/validate",filename:"route",bundlePath:"app/api/social/accounts/validate/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Ewasl\\eWasl.com_Cursor_Digital_Ocean\\ewasl-app\\src\\app\\api\\social\\accounts\\validate\\route.ts",nextConfigOutput:"standalone",userland:r}),{workAsyncStorage:_,workUnitAsyncStorage:m,serverHooks:w}=g;function k(){return(0,n.patchFetch)({workAsyncStorage:_,workUnitAsyncStorage:m})}},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63511:(e,t,a)=>{a.d(t,{U:()=>n,x:()=>i});var r=a(34386);let s=null;async function o(){if(s)return s;try{let e=await fetch("/api/config/supabase");if(!e.ok)throw Error(`Failed to fetch config: ${e.status}`);let t=await e.json();return s=t,t}catch(e){throw console.error("[Supabase Client] Failed to fetch dynamic config:",e),e}}function n(){let e="https://nnxfzhxqzmriggulsudr.supabase.co",t="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.1qBlXLOqGtJX_LFRC7XihOpwCtD1U1I2_jdyJ8sq_8w";return e&&t&&!e.includes("placeholder")&&!t.includes("placeholder")?(0,r.createBrowserClient)(e,t):(0,r.createBrowserClient)("https://placeholder.supabase.co","placeholder-key")}async function i(){let e="https://nnxfzhxqzmriggulsudr.supabase.co",t="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.1qBlXLOqGtJX_LFRC7XihOpwCtD1U1I2_jdyJ8sq_8w";if(e&&t&&!e.includes("placeholder")&&!t.includes("placeholder"))return(0,r.createBrowserClient)(e,t);let a=await o();return(0,r.createBrowserClient)(a.url,a.anonKey)}},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},82087:(e,t,a)=>{a.d(t,{p:()=>c});var r=a(55511),s=a.n(r);let o={CONNECTED:"connected"},n="https://graph.facebook.com/v18.0";class i{constructor(e){this.config=e}generateAppSecretProof(e){return s().createHmac("sha256",this.config.appSecret).update(e).digest("hex")}getAuthorizationUrl(e){let t=new URLSearchParams({client_id:this.config.appId,redirect_uri:this.config.redirectUri,scope:"pages_show_list,pages_read_engagement,pages_manage_posts,pages_read_user_content,instagram_basic,instagram_content_publish,business_management",response_type:"code",state:e||""});return`https://www.facebook.com/v18.0/dialog/oauth?${t.toString()}`}async exchangeCodeForToken(e){let t=new URLSearchParams({client_id:this.config.appId,client_secret:this.config.appSecret,redirect_uri:this.config.redirectUri,code:e}),a=await fetch(`${n}/oauth/access_token`,{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:t.toString()});if(!a.ok){let e=await a.json();throw Error(`Facebook OAuth error: ${e.error?.message||"Unknown error"}`)}return a.json()}async getLongLivedToken(e){let t=new URLSearchParams({grant_type:"fb_exchange_token",client_id:this.config.appId,client_secret:this.config.appSecret,fb_exchange_token:e}),a=await fetch(`${n}/oauth/access_token?${t.toString()}`);if(!a.ok){let e=await a.json();throw Error(`Facebook token exchange error: ${e.error?.message||"Unknown error"}`)}return a.json()}async validateToken(e){let t=this.generateAppSecretProof(e),a=await fetch(`${n}/me?fields=id,name,email,picture&access_token=${e}&appsecret_proof=${t}`);if(!a.ok){let e=await a.json();throw Error(`Facebook API error: ${e.error?.message||"Invalid token"}`)}return a.json()}async getUserAccounts(e){try{let t=this.generateAppSecretProof(e),a=await fetch(`${n}/me/accounts?fields=id,name,access_token,category,tasks,instagram_business_account&access_token=${e}&appsecret_proof=${t}`);if(!a.ok)throw Error("Failed to fetch Facebook pages");let r=await a.json(),s=[];for(let e of r.data||[]){let t={id:`fb_${e.id}`,platform:"FACEBOOK",accountName:e.name,accountHandle:`@${e.name.toLowerCase().replace(/\s+/g,"")}`,connectionStatus:o.CONNECTED,accessToken:e.access_token,refreshToken:null,expiresAt:null,lastValidatedAt:new Date,metadata:{pageId:e.id,category:e.category,tasks:e.tasks||[]},permissions:["read","write"],profileImageUrl:`https://graph.facebook.com/${e.id}/picture?type=large`,createdAt:new Date,updatedAt:new Date};if(s.push(t),e.instagram_business_account){let t={id:`ig_${e.instagram_business_account.id}`,platform:"INSTAGRAM",accountName:e.name,accountHandle:`@${e.name.toLowerCase().replace(/\s+/g,"")}`,connectionStatus:o.CONNECTED,accessToken:e.access_token,refreshToken:null,expiresAt:null,lastValidatedAt:new Date,metadata:{instagramAccountId:e.instagram_business_account.id,connectedFacebookPageId:e.id},permissions:["read","write"],profileImageUrl:`https://graph.facebook.com/${e.instagram_business_account.id}/picture?type=large`,createdAt:new Date,updatedAt:new Date};s.push(t)}}return s}catch(e){throw console.error("Error fetching user accounts:",e),e}}async getAccountMetrics(e,t,a){try{let r=this.generateAppSecretProof(t);if("FACEBOOK"===a){let a=await fetch(`${n}/${e}/insights?metric=page_fans,page_post_engagements&access_token=${t}&appsecret_proof=${r}`);if(a.ok){let e=(await a.json()).data||[],t=e.find(e=>"page_fans"===e.name)?.values?.[0]?.value||0,r=e.find(e=>"page_post_engagements"===e.name)?.values?.[0]?.value||0;return{followerCount:t,engagementRate:t>0?r/t*100:0,postCount:0}}}else if("INSTAGRAM"===a){let a=await fetch(`${n}/${e}?fields=followers_count,media_count&access_token=${t}&appsecret_proof=${r}`);if(a.ok){let e=await a.json();return{followerCount:e.followers_count||0,engagementRate:0,postCount:e.media_count||0}}}return{followerCount:0,engagementRate:0,postCount:0}}catch(e){return console.error("Error fetching account metrics:",e),{followerCount:0,engagementRate:0,postCount:0}}}async refreshAccessToken(e){throw Error("Facebook token refresh not implemented - use long-lived tokens")}async revokeToken(e){try{let t=this.generateAppSecretProof(e);return(await fetch(`${n}/me/permissions?access_token=${e}&appsecret_proof=${t}`,{method:"DELETE"})).ok}catch(e){return console.error("Error revoking Facebook token:",e),!1}}}function c(){let e={appId:process.env.FACEBOOK_APP_ID,appSecret:process.env.FACEBOOK_APP_SECRET,businessId:process.env.FACEBOOK_BUSINESS_ID,redirectUri:`${process.env.OAUTH_REDIRECT_URL}/facebook`};if(!e.appId||!e.appSecret)throw Error("Facebook OAuth configuration is missing");return new i(e)}},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")}};var t=require("../../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[4243,6167,580,4386,1053],()=>a(52102));module.exports=r})();