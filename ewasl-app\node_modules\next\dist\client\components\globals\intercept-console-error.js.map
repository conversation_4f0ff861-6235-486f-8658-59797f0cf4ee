{"version": 3, "sources": ["../../../../src/client/components/globals/intercept-console-error.ts"], "sourcesContent": ["import isError from '../../../lib/is-error'\nimport { isNextRouterError } from '../is-next-router-error'\nimport { handleConsoleError } from '../errors/use-error-handler'\nimport { parseConsoleArgs } from '../../lib/console'\n\nexport const originConsoleError = globalThis.console.error\n\n// Patch console.error to collect information about hydration errors\nexport function patchConsoleError() {\n  // Ensure it's only patched once\n  if (typeof window === 'undefined') {\n    return\n  }\n  window.console.error = function error(...args: any[]) {\n    let maybeError: unknown\n    if (process.env.NODE_ENV !== 'production') {\n      const { error: replayedError } = parseConsoleArgs(args)\n      if (replayedError) {\n        maybeError = replayedError\n      } else if (isError(args[0])) {\n        maybeError = args[0]\n      } else {\n        // See https://github.com/facebook/react/blob/d50323eb845c5fde0d720cae888bf35dedd05506/packages/react-reconciler/src/ReactFiberErrorLogger.js#L78\n        maybeError = args[1]\n      }\n    } else {\n      maybeError = args[0]\n    }\n\n    if (!isNextRouterError(maybeError)) {\n      if (process.env.NODE_ENV !== 'production') {\n        handleConsoleError(\n          // replayed errors have their own complex format string that should be used,\n          // but if we pass the error directly, `handleClientError` will ignore it\n          maybeError,\n          args\n        )\n      }\n\n      originConsoleError.apply(window.console, args)\n    }\n  }\n}\n"], "names": ["originConsoleError", "patchConsoleError", "globalThis", "console", "error", "window", "args", "maybeError", "process", "env", "NODE_ENV", "replayedError", "parseConsoleArgs", "isError", "isNextRouterError", "handleConsoleError", "apply"], "mappings": ";;;;;;;;;;;;;;;IAKaA,kBAAkB;eAAlBA;;IAGGC,iBAAiB;eAAjBA;;;;kEARI;mCACc;iCACC;yBACF;AAE1B,MAAMD,qBAAqBE,WAAWC,OAAO,CAACC,KAAK;AAGnD,SAASH;IACd,gCAAgC;IAChC,IAAI,OAAOI,WAAW,aAAa;QACjC;IACF;IACAA,OAAOF,OAAO,CAACC,KAAK,GAAG,SAASA;QAAM,IAAA,IAAA,OAAA,UAAA,QAAA,AAAGE,OAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;YAAGA,KAAH,QAAA,SAAA,CAAA,KAAc;;QAClD,IAAIC;QACJ,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;YACzC,MAAM,EAAEN,OAAOO,aAAa,EAAE,GAAGC,IAAAA,yBAAgB,EAACN;YAClD,IAAIK,eAAe;gBACjBJ,aAAaI;YACf,OAAO,IAAIE,IAAAA,gBAAO,EAACP,IAAI,CAAC,EAAE,GAAG;gBAC3BC,aAAaD,IAAI,CAAC,EAAE;YACtB,OAAO;gBACL,iJAAiJ;gBACjJC,aAAaD,IAAI,CAAC,EAAE;YACtB;QACF,OAAO;YACLC,aAAaD,IAAI,CAAC,EAAE;QACtB;QAEA,IAAI,CAACQ,IAAAA,oCAAiB,EAACP,aAAa;YAClC,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;gBACzCK,IAAAA,mCAAkB,EAChB,4EAA4E;gBAC5E,wEAAwE;gBACxER,YACAD;YAEJ;YAEAN,mBAAmBgB,KAAK,CAACX,OAAOF,OAAO,EAAEG;QAC3C;IACF;AACF"}