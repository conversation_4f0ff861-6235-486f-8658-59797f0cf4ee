exports.id=2721,exports.ids=[2721],exports.modules={15900:(e,t,r)=>{"use strict";r.d(t,{K:()=>s});var a=r(27002);class s extends a.g{async refreshToken(e){try{let t=await this.fetch("https://www.linkedin.com/oauth/v2/accessToken",{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:new URLSearchParams({grant_type:"refresh_token",refresh_token:e,client_id:process.env.LINKEDIN_CLIENT_ID,client_secret:process.env.LINKEDIN_CLIENT_SECRET})}),{access_token:r,refresh_token:a,expires_in:s}=await t.json(),n=await this.fetch("https://api.linkedin.com/v2/userinfo",{headers:{Authorization:`Bearer ${r}`}}),{name:i,sub:o,picture:c}=await n.json(),h=await this.fetch("https://api.linkedin.com/v2/me",{headers:{Authorization:`Bearer ${r}`}}),{vanityName:u}=await h.json();return{id:o,accessToken:r,refreshToken:a,expiresIn:s,name:i,picture:c,username:u}}catch(e){throw console.error("[LinkedInEnhanced] Refresh token error:",e),Error(`Failed to refresh LinkedIn token: ${e instanceof Error?e.message:e}`)}}async generateAuthUrl(){let e=this.makeId(6),t=this.makeId(30);return{url:`https://www.linkedin.com/oauth/v2/authorization?response_type=code&client_id=${process.env.LINKEDIN_CLIENT_ID}&prompt=none&redirect_uri=${encodeURIComponent("https://app.ewasl.com/api/linkedin/callback")}&state=${e}&scope=${encodeURIComponent(this.scopes.join(" "))}`,codeVerifier:t,state:e}}async authenticate(e){try{let t=new URLSearchParams;t.append("grant_type","authorization_code"),t.append("code",e.code),t.append("redirect_uri",`https://app.ewasl.com/api/linkedin/callback${e.refresh?`?refresh=${e.refresh}`:""}`),t.append("client_id",process.env.LINKEDIN_CLIENT_ID),t.append("client_secret",process.env.LINKEDIN_CLIENT_SECRET);let r=await this.fetch("https://www.linkedin.com/oauth/v2/accessToken",{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:t}),{access_token:a,expires_in:s,refresh_token:n,scope:i}=await r.json();this.checkScopes(this.scopes,i);let o=await this.fetch("https://api.linkedin.com/v2/userinfo",{headers:{Authorization:`Bearer ${a}`}}),{name:c,sub:h,picture:u}=await o.json(),d=await this.fetch("https://api.linkedin.com/v2/me",{headers:{Authorization:`Bearer ${a}`}}),{vanityName:l}=await d.json();return{id:h,accessToken:a,refreshToken:n,expiresIn:s,name:c,picture:u,username:l}}catch(e){throw console.error("[LinkedInEnhanced] Authentication error:",e),Error(`LinkedIn authentication failed: ${e instanceof Error?e.message:e}`)}}async getCompanyPages(e){try{let t=await this.fetch("https://api.linkedin.com/v2/organizationAcls?q=roleAssignee&role=ADMINISTRATOR&projection=(elements*(organization~(id,name,vanityName,logoV2(original~:playableStreams))))",{headers:{Authorization:`Bearer ${e}`,"X-Restli-Protocol-Version":"2.0.0","LinkedIn-Version":"202501"}}),r=await t.json();return r.elements?.map(e=>({id:e.organization.id,name:e.organization.name,vanityName:e.organization.vanityName,logoUrl:e.organization.logoV2?.original?.elements?.[0]?.identifiers?.[0]?.identifier}))||[]}catch(e){return console.error("[LinkedInEnhanced] Get company pages error:",e),[]}}fixText(e){let t=/@\[.+?\]\(urn:li:organization.+?\)/g,r=e.match(t)||[];return e.split(t).map(e=>e.replace(/\\/g,"\\\\").replace(/</g,"\\<").replace(/>/g,"\\>").replace(/#/g,"\\#").replace(/~/g,"\\~").replace(/_/g,"\\_").replace(/\|/g,"\\|").replace(/\[/g,"\\[").replace(/]/g,"\\]").replace(/\*/g,"\\*").replace(/\(/g,"\\(").replace(/\)/g,"\\)").replace(/\{/g,"\\{").replace(/}/g,"\\}").replace(/@/g,"\\@")).reduce((e,t)=>{let a=r.shift();return e.push(t),a&&e.push(a),e},[]).join("")}async post(e,t,r,a,s="personal"){try{let[a,...n]=r,i=[];if(a.media?.length)for(let r of a.media){let a=await this.uploadMedia(r,t,e,s);a&&i.push(a)}let o={author:"personal"===s?`urn:li:person:${e}`:`urn:li:organization:${e}`,commentary:this.fixText(a.message),visibility:"PUBLIC",distribution:{feedDistribution:"MAIN_FEED",targetEntities:[],thirdPartyDistributionChannels:[]},...i.length>0?{content:1===i.length?{media:{id:i[0]}}:{multiImage:{images:i.map(e=>({id:e}))}}}:{},lifecycleState:"PUBLISHED",isReshareDisabledByAuthor:!1},c=await this.fetch("https://api.linkedin.com/v2/posts",{method:"POST",headers:{"X-Restli-Protocol-Version":"2.0.0","Content-Type":"application/json",Authorization:`Bearer ${t}`},body:JSON.stringify(o)});if(201!==c.status&&200!==c.status)throw Error("Error posting to LinkedIn");let h=c.headers.get("x-restli-id"),u=[{status:"posted",postId:h,id:a.id,releaseURL:`https://www.linkedin.com/feed/update/${h}`}];for(let r of n)try{let a=await this.fetch(`https://api.linkedin.com/v2/socialActions/${decodeURIComponent(h)}/comments`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${t}`},body:JSON.stringify({actor:"personal"===s?`urn:li:person:${e}`:`urn:li:organization:${e}`,object:h,message:{text:this.fixText(r.message)}})}),{object:n}=await a.json();u.push({status:"posted",postId:n,id:r.id,releaseURL:`https://www.linkedin.com/embed/feed/update/${n}`})}catch(e){console.error("[LinkedInEnhanced] Comment posting error:",e),u.push({status:"failed",postId:"",id:r.id,releaseURL:""})}return u}catch(e){throw console.error("[LinkedInEnhanced] Post error:",e),Error(`LinkedIn posting failed: ${e instanceof Error?e.message:e}`)}}async uploadMedia(e,t,r,a="personal"){try{return null}catch(e){return console.error("[LinkedInEnhanced] Media upload error:",e),null}}constructor(...e){super(...e),this.identifier="linkedin",this.name="LinkedIn",this.oneTimeToken=!0,this.isBetweenSteps=!1,this.scopes=["openid","profile","w_member_social","email"],this.refreshWait=!0}}},27002:(e,t,r)=>{"use strict";r.d(t,{g:()=>s});var a=r(80726);class s{async fetch(e,t={},r){try{let a=await fetch(e,{...t,headers:{"User-Agent":"eWasl/1.0 (+https://app.ewasl.com)",...t.headers}});if(!a.ok){let t=await a.text();throw console.error(`[${this.constructor.name}] HTTP Error:`,{status:a.status,statusText:a.statusText,url:e,context:r,error:t}),Error(`HTTP ${a.status}: ${a.statusText} - ${t}`)}return a}catch(t){throw console.error(`[${this.constructor.name}] Fetch Error:`,{url:e,context:r,error:t instanceof Error?t.message:t}),t}}checkScopes(e,t){let r=Array.isArray(t)?t:t.split(" "),a=e.filter(e=>!r.includes(e));if(a.length>0)throw console.warn(`[${this.constructor.name}] Missing scopes:`,a),Error(`Missing required scopes: ${a.join(", ")}`)}makeId(e){let t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",r="";for(let a=0;a<e;a++)r+=t.charAt(Math.floor(Math.random()*t.length));return r}async logActivity(e,t,r,a){try{await this.supabase.from("activity_logs").insert({user_id:e,platform:t,action:r,details:a,created_at:new Date().toISOString()})}catch(e){console.error(`[${this.constructor.name}] Failed to log activity:`,e)}}async updateTokens(e,t,r,a){try{let s={access_token:t,updated_at:new Date().toISOString()};r&&(s.refresh_token=r),a&&(s.expires_at=new Date(Date.now()+1e3*a).toISOString());let{error:n}=await this.supabase.from("social_accounts").update(s).eq("id",e);if(n)throw n}catch(e){throw console.error(`[${this.constructor.name}] Failed to update tokens:`,e),e}}async getIntegration(e){try{let{data:t,error:r}=await this.supabase.from("social_accounts").select("*").eq("id",e).single();if(r)throw r;return t}catch(e){throw console.error(`[${this.constructor.name}] Failed to get integration:`,e),e}}async saveIntegration(e){try{let{data:t,error:r}=await this.supabase.from("social_accounts").upsert({...e,created_at:new Date().toISOString(),updated_at:new Date().toISOString(),is_active:!0},{onConflict:"user_id,platform,account_id"}).select().single();if(r)throw r;return t}catch(e){throw console.error(`[${this.constructor.name}] Failed to save integration:`,e),e}}async handleRateLimit(e,t=0,r=3){if(t>=r)return!1;if(429===e.status||e.message?.includes("rate limit")){let r=1e3*parseInt(e.headers?.get("retry-after")||Math.pow(2,t));return await new Promise(e=>setTimeout(e,r)),!0}return!1}fixText(e){return e.replace(/\\/g,"\\\\").replace(/"/g,'\\"').trim()}constructor(){this.supabase=a.sY}}},59406:function(e){e.exports=function(){"use strict";var e="millisecond",t="second",r="minute",a="hour",s="week",n="month",i="quarter",o="year",c="date",h="Invalid Date",u=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,d=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,l=function(e,t,r){var a=String(e);return!a||a.length>=t?e:""+Array(t+1-a.length).join(r)+e},p="en",f={};f[p]={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(e){var t=["th","st","nd","rd"],r=e%100;return"["+e+(t[(r-20)%10]||t[r]||t[0])+"]"}};var m="$isDayjsObject",g=function(e){return e instanceof $||!(!e||!e[m])},w=function e(t,r,a){var s;if(!t)return p;if("string"==typeof t){var n=t.toLowerCase();f[n]&&(s=n),r&&(f[n]=r,s=n);var i=t.split("-");if(!s&&i.length>1)return e(i[0])}else{var o=t.name;f[o]=t,s=o}return!a&&s&&(p=s),s||!a&&p},_=function(e,t){if(g(e))return e.clone();var r="object"==typeof t?t:{};return r.date=e,r.args=arguments,new $(r)},y={s:l,z:function(e){var t=-e.utcOffset(),r=Math.abs(t);return(t<=0?"+":"-")+l(Math.floor(r/60),2,"0")+":"+l(r%60,2,"0")},m:function e(t,r){if(t.date()<r.date())return-e(r,t);var a=12*(r.year()-t.year())+(r.month()-t.month()),s=t.clone().add(a,n),i=r-s<0,o=t.clone().add(a+(i?-1:1),n);return+(-(a+(r-s)/(i?s-o:o-s))||0)},a:function(e){return e<0?Math.ceil(e)||0:Math.floor(e)},p:function(h){return({M:n,y:o,w:s,d:"day",D:c,h:a,m:r,s:t,ms:e,Q:i})[h]||String(h||"").toLowerCase().replace(/s$/,"")},u:function(e){return void 0===e}};y.l=w,y.i=g,y.w=function(e,t){return _(e,{locale:t.$L,utc:t.$u,x:t.$x,$offset:t.$offset})};var $=function(){function l(e){this.$L=w(e.locale,null,!0),this.parse(e),this.$x=this.$x||e.x||{},this[m]=!0}var p=l.prototype;return p.parse=function(e){this.$d=function(e){var t=e.date,r=e.utc;if(null===t)return new Date(NaN);if(y.u(t))return new Date;if(t instanceof Date)return new Date(t);if("string"==typeof t&&!/Z$/i.test(t)){var a=t.match(u);if(a){var s=a[2]-1||0,n=(a[7]||"0").substring(0,3);return r?new Date(Date.UTC(a[1],s,a[3]||1,a[4]||0,a[5]||0,a[6]||0,n)):new Date(a[1],s,a[3]||1,a[4]||0,a[5]||0,a[6]||0,n)}}return new Date(t)}(e),this.init()},p.init=function(){var e=this.$d;this.$y=e.getFullYear(),this.$M=e.getMonth(),this.$D=e.getDate(),this.$W=e.getDay(),this.$H=e.getHours(),this.$m=e.getMinutes(),this.$s=e.getSeconds(),this.$ms=e.getMilliseconds()},p.$utils=function(){return y},p.isValid=function(){return this.$d.toString()!==h},p.isSame=function(e,t){var r=_(e);return this.startOf(t)<=r&&r<=this.endOf(t)},p.isAfter=function(e,t){return _(e)<this.startOf(t)},p.isBefore=function(e,t){return this.endOf(t)<_(e)},p.$g=function(e,t,r){return y.u(e)?this[t]:this.set(r,e)},p.unix=function(){return Math.floor(this.valueOf()/1e3)},p.valueOf=function(){return this.$d.getTime()},p.startOf=function(e,i){var h=this,u=!!y.u(i)||i,d=y.p(e),l=function(e,t){var r=y.w(h.$u?Date.UTC(h.$y,t,e):new Date(h.$y,t,e),h);return u?r:r.endOf("day")},p=function(e,t){return y.w(h.toDate()[e].apply(h.toDate("s"),(u?[0,0,0,0]:[23,59,59,999]).slice(t)),h)},f=this.$W,m=this.$M,g=this.$D,w="set"+(this.$u?"UTC":"");switch(d){case o:return u?l(1,0):l(31,11);case n:return u?l(1,m):l(0,m+1);case s:var _=this.$locale().weekStart||0,$=(f<_?f+7:f)-_;return l(u?g-$:g+(6-$),m);case"day":case c:return p(w+"Hours",0);case a:return p(w+"Minutes",1);case r:return p(w+"Seconds",2);case t:return p(w+"Milliseconds",3);default:return this.clone()}},p.endOf=function(e){return this.startOf(e,!1)},p.$set=function(s,i){var h,u=y.p(s),d="set"+(this.$u?"UTC":""),l=((h={}).day=d+"Date",h[c]=d+"Date",h[n]=d+"Month",h[o]=d+"FullYear",h[a]=d+"Hours",h[r]=d+"Minutes",h[t]=d+"Seconds",h[e]=d+"Milliseconds",h)[u],p="day"===u?this.$D+(i-this.$W):i;if(u===n||u===o){var f=this.clone().set(c,1);f.$d[l](p),f.init(),this.$d=f.set(c,Math.min(this.$D,f.daysInMonth())).$d}else l&&this.$d[l](p);return this.init(),this},p.set=function(e,t){return this.clone().$set(e,t)},p.get=function(e){return this[y.p(e)]()},p.add=function(e,i){var c,h=this;e=Number(e);var u=y.p(i),d=function(t){var r=_(h);return y.w(r.date(r.date()+Math.round(t*e)),h)};if(u===n)return this.set(n,this.$M+e);if(u===o)return this.set(o,this.$y+e);if("day"===u)return d(1);if(u===s)return d(7);var l=((c={})[r]=6e4,c[a]=36e5,c[t]=1e3,c)[u]||1,p=this.$d.getTime()+e*l;return y.w(p,this)},p.subtract=function(e,t){return this.add(-1*e,t)},p.format=function(e){var t=this,r=this.$locale();if(!this.isValid())return r.invalidDate||h;var a=e||"YYYY-MM-DDTHH:mm:ssZ",s=y.z(this),n=this.$H,i=this.$m,o=this.$M,c=r.weekdays,u=r.months,l=r.meridiem,p=function(e,r,s,n){return e&&(e[r]||e(t,a))||s[r].slice(0,n)},f=function(e){return y.s(n%12||12,e,"0")},m=l||function(e,t,r){var a=e<12?"AM":"PM";return r?a.toLowerCase():a};return a.replace(d,function(e,a){return a||function(e){switch(e){case"YY":return String(t.$y).slice(-2);case"YYYY":return y.s(t.$y,4,"0");case"M":return o+1;case"MM":return y.s(o+1,2,"0");case"MMM":return p(r.monthsShort,o,u,3);case"MMMM":return p(u,o);case"D":return t.$D;case"DD":return y.s(t.$D,2,"0");case"d":return String(t.$W);case"dd":return p(r.weekdaysMin,t.$W,c,2);case"ddd":return p(r.weekdaysShort,t.$W,c,3);case"dddd":return c[t.$W];case"H":return String(n);case"HH":return y.s(n,2,"0");case"h":return f(1);case"hh":return f(2);case"a":return m(n,i,!0);case"A":return m(n,i,!1);case"m":return String(i);case"mm":return y.s(i,2,"0");case"s":return String(t.$s);case"ss":return y.s(t.$s,2,"0");case"SSS":return y.s(t.$ms,3,"0");case"Z":return s}return null}(e)||s.replace(":","")})},p.utcOffset=function(){return-(15*Math.round(this.$d.getTimezoneOffset()/15))},p.diff=function(e,c,h){var u,d=this,l=y.p(c),p=_(e),f=(p.utcOffset()-this.utcOffset())*6e4,m=this-p,g=function(){return y.m(d,p)};switch(l){case o:u=g()/12;break;case n:u=g();break;case i:u=g()/3;break;case s:u=(m-f)/6048e5;break;case"day":u=(m-f)/864e5;break;case a:u=m/36e5;break;case r:u=m/6e4;break;case t:u=m/1e3;break;default:u=m}return h?u:y.a(u)},p.daysInMonth=function(){return this.endOf(n).$D},p.$locale=function(){return f[this.$L]},p.locale=function(e,t){if(!e)return this.$L;var r=this.clone(),a=w(e,t,!0);return a&&(r.$L=a),r},p.clone=function(){return y.w(this.$d,this)},p.toDate=function(){return new Date(this.valueOf())},p.toJSON=function(){return this.isValid()?this.toISOString():null},p.toISOString=function(){return this.$d.toISOString()},p.toString=function(){return this.$d.toUTCString()},l}(),k=$.prototype;return _.prototype=k,[["$ms",e],["$s",t],["$m",r],["$H",a],["$W","day"],["$M",n],["$y",o],["$D",c]].forEach(function(e){k[e[1]]=function(t){return this.$g(t,e[0],e[1])}}),_.extend=function(e,t){return e.$i||(e(t,$,_),e.$i=!0),_},_.locale=w,_.isDayjs=g,_.unix=function(e){return _(1e3*e)},_.en=f[p],_.Ls=f,_.p={},_}()},68575:(e,t,r)=>{"use strict";r.d(t,{$:()=>n});var a=r(27002),s=r(55511);class n extends a.g{async refreshToken(e){try{let t=await this.fetch("https://api.twitter.com/2/oauth2/token",{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded",Authorization:`Basic ${Buffer.from(`${process.env.TWITTER_CLIENT_ID}:${process.env.TWITTER_CLIENT_SECRET}`).toString("base64")}`},body:new URLSearchParams({grant_type:"refresh_token",refresh_token:e})}),{access_token:r,refresh_token:a,expires_in:s}=await t.json(),n=await this.fetch("https://api.twitter.com/2/users/me?user.fields=profile_image_url,username",{headers:{Authorization:`Bearer ${r}`}}),{data:i}=await n.json();return{id:i.id,accessToken:r,refreshToken:a,expiresIn:s,name:i.name,picture:i.profile_image_url,username:i.username}}catch(e){throw console.error("[TwitterEnhanced] Refresh token error:",e),Error(`Failed to refresh Twitter token: ${e instanceof Error?e.message:e}`)}}async generateAuthUrl(){let e=this.makeId(32),t=this.generateCodeVerifier(),r=this.generateCodeChallenge(t),a=new URLSearchParams({response_type:"code",client_id:process.env.TWITTER_CLIENT_ID,redirect_uri:"https://app.ewasl.com/api/social/callback/twitter",scope:this.scopes.join(" "),state:e,code_challenge:r,code_challenge_method:"S256"});return{url:`https://twitter.com/i/oauth2/authorize?${a.toString()}`,codeVerifier:t,state:e}}async authenticate(e){try{let t=await this.fetch("https://api.twitter.com/2/oauth2/token",{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded",Authorization:`Basic ${Buffer.from(`${process.env.TWITTER_CLIENT_ID}:${process.env.TWITTER_CLIENT_SECRET}`).toString("base64")}`},body:new URLSearchParams({grant_type:"authorization_code",code:e.code,redirect_uri:`https://app.ewasl.com/api/social/callback/twitter${e.refresh?`?refresh=${e.refresh}`:""}`,code_verifier:e.codeVerifier})}),{access_token:r,refresh_token:a,expires_in:s,scope:n}=await t.json();this.checkScopes(this.scopes,n.split(" "));let i=await this.fetch("https://api.twitter.com/2/users/me?user.fields=profile_image_url,username",{headers:{Authorization:`Bearer ${r}`}}),{data:o}=await i.json();return{id:o.id,accessToken:r,refreshToken:a,expiresIn:s,name:o.name,picture:o.profile_image_url,username:o.username}}catch(e){throw console.error("[TwitterEnhanced] Authentication error:",e),Error(`Twitter authentication failed: ${e instanceof Error?e.message:e}`)}}async post(e,t,r,a){try{let[e,...a]=r,s=[],n=[];e.media?.length&&(n=await this.uploadMedia(e.media,t));let i={text:e.message};n.length>0&&(i.media={media_ids:n});let o=await this.fetch("https://api.twitter.com/2/tweets",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${t}`},body:JSON.stringify(i)}),{data:c}=await o.json();s.push({id:e.id,postId:c.id,releaseURL:`https://twitter.com/i/web/status/${c.id}`,status:"posted"});let h=c.id;for(let e of a)try{let r=await this.fetch("https://api.twitter.com/2/tweets",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${t}`},body:JSON.stringify({text:e.message,reply:{in_reply_to_tweet_id:h}})}),{data:a}=await r.json();h=a.id,s.push({id:e.id,postId:a.id,releaseURL:`https://twitter.com/i/web/status/${a.id}`,status:"posted"})}catch(t){console.error("[TwitterEnhanced] Thread post error:",t),s.push({id:e.id,postId:"",releaseURL:"",status:"failed"})}return s}catch(e){throw console.error("[TwitterEnhanced] Post error:",e),Error(`Twitter posting failed: ${e instanceof Error?e.message:e}`)}}async uploadMedia(e,t){let r=[];for(let a of e)try{let e=await fetch(a.url);if(!e.ok)throw Error(`Failed to fetch media: ${e.statusText}`);let s=await e.arrayBuffer(),n=e.headers.get("content-type")||"image/jpeg",i=await this.fetch("https://upload.twitter.com/1.1/media/upload.json",{method:"POST",headers:{Authorization:`Bearer ${t}`,"Content-Type":"multipart/form-data"},body:new FormData().append("media",new Blob([s],{type:n}))}),o=await i.json();o.media_id_string&&r.push(o.media_id_string)}catch(e){console.error("[TwitterEnhanced] Media upload error:",e)}return r}generateCodeVerifier(){return s.randomBytes(32).toString("base64url")}generateCodeChallenge(e){return s.createHash("sha256").update(e).digest("base64url")}async getTweetMetrics(e,t){try{let r=await this.fetch(`https://api.twitter.com/2/tweets/${e}?tweet.fields=public_metrics,created_at`,{headers:{Authorization:`Bearer ${t}`}});return await r.json()}catch(e){return console.error("[TwitterEnhanced] Get tweet metrics error:",e),null}}async getUserMetrics(e,t){try{let r=await this.fetch(`https://api.twitter.com/2/users/${e}?user.fields=public_metrics`,{headers:{Authorization:`Bearer ${t}`}});return await r.json()}catch(e){return console.error("[TwitterEnhanced] Get user metrics error:",e),null}}constructor(...e){super(...e),this.identifier="twitter",this.name="Twitter/X",this.isBetweenSteps=!1,this.scopes=["tweet.read","tweet.write","users.read","media.upload","offline.access"]}}},80726:(e,t,r)=>{"use strict";r.d(t,{HK:()=>s,sY:()=>n});var a=r(66167);function s(){let e="https://nnxfzhxqzmriggulsudr.supabase.co",t=process.env.SUPABASE_SERVICE_ROLE_KEY||"placeholder-key";if(!e)throw Error("Missing NEXT_PUBLIC_SUPABASE_URL environment variable");return process.env.SUPABASE_SERVICE_ROLE_KEY||console.warn("SUPABASE_SERVICE_ROLE_KEY environment variable not set - using anon key as fallback"),(0,a.createClient)(e,t,{auth:{autoRefreshToken:!1,persistSession:!1}})}let n=s(),i=process.env.SUPABASE_SERVICE_ROLE_KEY||"placeholder-key";(0,a.createClient)("https://nnxfzhxqzmriggulsudr.supabase.co",i,{auth:{autoRefreshToken:!1,persistSession:!1}})},96606:(e,t,r)=>{"use strict";r.d(t,{i:()=>i});var a=r(27002),s=r(59406),n=r.n(s);class i extends a.g{async refreshToken(e){return{refreshToken:"",expiresIn:0,accessToken:"",id:"",name:"",picture:"",username:""}}async generateAuthUrl(){let e=this.makeId(6),t=this.makeId(10);return{url:`https://www.facebook.com/v20.0/dialog/oauth?client_id=${process.env.FACEBOOK_APP_ID}&redirect_uri=${encodeURIComponent("https://app.ewasl.com/api/facebook/callback")}&state=${e}&scope=${this.scopes.join(",")}`,codeVerifier:t,state:e}}async reConnect(e,t,r){try{let e=await this.fetchPageInformation(r,t);return{id:e.id,name:e.name,accessToken:e.access_token,refreshToken:e.access_token,expiresIn:n()().add(59,"days").unix()-n()().unix(),picture:e.picture,username:e.username}}catch(e){throw console.error("[FacebookEnhanced] ReConnect error:",e),Error(`Facebook reconnection failed: ${e instanceof Error?e.message:e}`)}}async authenticate(e){try{let t=await this.fetch(`https://graph.facebook.com/v20.0/oauth/access_token?client_id=${process.env.FACEBOOK_APP_ID}&redirect_uri=${encodeURIComponent(`https://app.ewasl.com/api/facebook/callback${e.refresh?`?refresh=${e.refresh}`:""}`)}&client_secret=${process.env.FACEBOOK_APP_SECRET}&code=${e.code}`),r=await t.json(),a=await this.fetch(`https://graph.facebook.com/v20.0/oauth/access_token?grant_type=fb_exchange_token&client_id=${process.env.FACEBOOK_APP_ID}&client_secret=${process.env.FACEBOOK_APP_SECRET}&fb_exchange_token=${r.access_token}&fields=access_token,expires_in`),{access_token:s}=await a.json(),i=await this.fetch(`https://graph.facebook.com/v20.0/me/permissions?access_token=${s}`),{data:o}=await i.json(),c=o.filter(e=>"granted"===e.status).map(e=>e.permission);this.checkScopes(this.scopes,c);let h=await this.fetch(`https://graph.facebook.com/v20.0/me?fields=id,name,picture&access_token=${s}`),{id:u,name:d,picture:{data:{url:l}}}=await h.json();return{id:u,name:d,accessToken:s,refreshToken:s,expiresIn:n()().add(59,"days").unix()-n()().unix(),picture:l,username:""}}catch(e){throw console.error("[FacebookEnhanced] Authentication error:",e),Error(`Facebook authentication failed: ${e instanceof Error?e.message:e}`)}}async getBusinessPages(e){try{let t=await this.fetch(`https://graph.facebook.com/v20.0/me/accounts?fields=id,username,name,picture.type(large)&access_token=${e}`),{data:r}=await t.json();return r.map(e=>({id:e.id,name:e.name,username:e.username,picture:e.picture?.data?.url,type:"page"}))}catch(e){return console.error("[FacebookEnhanced] Get business pages error:",e),[]}}async fetchPageInformation(e,t){try{let r=await this.fetch(`https://graph.facebook.com/v20.0/${t}?fields=username,access_token,name,picture.type(large)&access_token=${e}`),{id:a,name:s,access_token:n,username:i,picture:{data:{url:o}}}=await r.json();return{id:a,name:s,access_token:n,picture:o,username:i}}catch(e){throw console.error("[FacebookEnhanced] Fetch page information error:",e),e}}async post(e,t,r,a){try{let[a,...s]=r,n="",i="";if(a?.media?.[0]?.url?.includes("mp4")){let r=await this.fetch(`https://graph.facebook.com/v20.0/${e}/videos?access_token=${t}&fields=id,permalink_url`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({file_url:a.media[0].url,description:a.message,published:!0})}),{id:s,permalink_url:o}=await r.json();i="https://www.facebook.com/reel/"+s,n=s}else{let r=a?.media?.length?await Promise.all(a.media.map(async r=>{let a=await this.fetch(`https://graph.facebook.com/v20.0/${e}/photos?access_token=${t}`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({url:r.url,published:!1})}),{id:s}=await a.json();return{media_fbid:s}})):[],s=await this.fetch(`https://graph.facebook.com/v20.0/${e}/feed?access_token=${t}&fields=id,permalink_url`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({...r?.length?{attached_media:r}:{},message:a.message,published:!0})}),{id:o,permalink_url:c}=await s.json();i=c,n=o}let o=[{id:a.id,postId:n,releaseURL:i,status:"posted"}];for(let e of s)try{let r=await this.fetch(`https://graph.facebook.com/v20.0/${n}/comments?access_token=${t}&fields=id,permalink_url`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({...e.media?.length?{attachment_url:e.media[0].url}:{},message:e.message})}),a=await r.json();o.push({id:e.id,postId:a.id,releaseURL:a.permalink_url,status:"posted"})}catch(t){console.error("[FacebookEnhanced] Comment posting error:",t),o.push({id:e.id,postId:"",releaseURL:"",status:"failed"})}return o}catch(e){throw console.error("[FacebookEnhanced] Post error:",e),Error(`Facebook posting failed: ${e instanceof Error?e.message:e}`)}}async analytics(e,t,r){try{let a=n()().endOf("day").unix(),s=n()().subtract(r,"day").unix(),i=await this.fetch(`https://graph.facebook.com/v20.0/${e}/insights?metric=page_impressions_unique,page_posts_impressions_unique,page_post_engagements,page_daily_follows,page_video_views&access_token=${t}&period=day&since=${s}&until=${a}`),{data:o}=await i.json();return o?.map(e=>({label:"page_impressions_unique"===e.name?"Page Impressions":"page_post_engagements"===e.name?"Posts Engagement":"page_daily_follows"===e.name?"Page followers":"page_video_views"===e.name?"Videos views":"Posts Impressions",percentageChange:5,data:e?.values?.map(e=>({total:e.value,date:n()(e.end_time).format("YYYY-MM-DD")}))}))||[]}catch(e){return console.error("[FacebookEnhanced] Analytics error:",e),[]}}constructor(...e){super(...e),this.identifier="facebook",this.name="Facebook Page",this.isBetweenSteps=!0,this.scopes=["pages_show_list","business_management","pages_manage_posts","pages_manage_engagement","pages_read_engagement","read_insights"]}}}};