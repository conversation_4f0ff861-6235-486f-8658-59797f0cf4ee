exports.id=9908,exports.ids=[9908],exports.modules={867:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},4780:(e,t,r)=>{"use strict";r.d(t,{Yq:()=>n,cn:()=>a,uD:()=>i});var s=r(49384),o=r(82348);function a(...e){return(0,o.QP)((0,s.$)(e))}function n(e){return new Date(e).toLocaleDateString("ar-SA",{day:"numeric",month:"long",year:"numeric"})}function i(e){switch(e){case"TWITTER":return"تويتر";case"FACEBOOK":return"فيسبوك";case"INSTAGRAM":return"انستغرام";case"LINKEDIN":return"لينكد إن";case"TIKTOK":return"تيك توك";default:return e}}},7043:(e,t,r)=>{"use strict";r.d(t,{LanguageProvider:()=>o});var s=r(12907);let o=(0,s.registerClientReference)(function(){throw Error("Attempted to call LanguageProvider() from the server but LanguageProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Ewasl\\eWasl.com_Cursor_Digital_Ocean\\ewasl-app\\src\\contexts\\LanguageContext.tsx","LanguageProvider");(0,s.registerClientReference)(function(){throw Error("Attempted to call useLanguage() from the server but useLanguage is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Ewasl\\eWasl.com_Cursor_Digital_Ocean\\ewasl-app\\src\\contexts\\LanguageContext.tsx","useLanguage")},9555:(e,t,r)=>{"use strict";r.d(t,{Ro:()=>a,cE:()=>o});var s=r(43210);function o(e){let[t,r]=(0,s.useState)({renderTime:0,mountTime:0,updateCount:0,lastUpdate:0});(0,s.useRef)(0);let o=(0,s.useRef)(0);return(0,s.useRef)(0),o.current=performance.now(),t}function a(){let[e,t]=(0,s.useState)({});return e}},14311:(e,t,r)=>{"use strict";r.d(t,{_:()=>i,default:()=>n});var s=r(60687),o=r(43210);r(79481);let a=(0,o.createContext)(void 0);function n({children:e}){let[t,r]=(0,o.useState)(null),[n,i]=(0,o.useState)(!0),[l,d]=(0,o.useState)(null),c=async(e,t)=>{if(!l)return{error:Error("Supabase not initialized")};let{error:r}=await l.auth.signInWithPassword({email:e,password:t});return{error:r}},u=async(e,t,r)=>{if(!l)return{error:Error("Supabase not initialized")};let{error:s}=await l.auth.signUp({email:e,password:t,options:{data:{name:r}}});return{error:s}},m=async()=>{l&&await l.auth.signOut()};return(0,s.jsx)(a.Provider,{value:{user:t,loading:n,signIn:c,signUp:u,signOut:m},children:e})}let i=()=>{let e=(0,o.useContext)(a);if(void 0===e)throw Error("useSupabase must be used inside SupabaseProvider");return e}},14939:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},14947:(e,t,r)=>{"use strict";r.d(t,{Toaster:()=>v});var s=r(60687),o=r(20140),a=r(43210),n=r(47313),i=r(24224),l=r(11860),d=r(4780);let c=n.Kq,u=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)(n.LM,{ref:r,className:(0,d.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",e),...t}));u.displayName=n.LM.displayName;let m=(0,i.F)("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),p=a.forwardRef(({className:e,variant:t,...r},o)=>(0,s.jsx)(n.bL,{ref:o,className:(0,d.cn)(m({variant:t}),e),...r}));p.displayName=n.bL.displayName,a.forwardRef(({className:e,...t},r)=>(0,s.jsx)(n.rc,{ref:r,className:(0,d.cn)("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",e),...t})).displayName=n.rc.displayName;let h=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)(n.bm,{ref:r,className:(0,d.cn)("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",e),"toast-close":"",...t,children:(0,s.jsx)(l.A,{className:"h-4 w-4"})}));h.displayName=n.bm.displayName;let f=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)(n.hE,{ref:r,className:(0,d.cn)("text-sm font-semibold",e),...t}));f.displayName=n.hE.displayName;let g=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)(n.VY,{ref:r,className:(0,d.cn)("text-sm opacity-90",e),...t}));function v(){let{toasts:e}=(0,o.d)();return(0,s.jsxs)(c,{children:[e.map(function({id:e,title:t,description:r,action:o,...a}){return(0,s.jsxs)(p,{...a,children:[(0,s.jsxs)("div",{className:"grid gap-1",children:[t&&(0,s.jsx)(f,{children:t}),r&&(0,s.jsx)(g,{children:r})]}),o,(0,s.jsx)(h,{})]},e)}),(0,s.jsx)(u,{})]})}g.displayName=n.VY.displayName},16126:(e,t,r)=>{"use strict";r.d(t,{PerformanceProvider:()=>o});var s=r(12907);(0,s.registerClientReference)(function(){throw Error("Attempted to call PerformanceMonitor() from the server but PerformanceMonitor is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Ewasl\\eWasl.com_Cursor_Digital_Ocean\\ewasl-app\\src\\components\\ui\\performance-monitor.tsx","PerformanceMonitor");let o=(0,s.registerClientReference)(function(){throw Error("Attempted to call PerformanceProvider() from the server but PerformanceProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Ewasl\\eWasl.com_Cursor_Digital_Ocean\\ewasl-app\\src\\components\\ui\\performance-monitor.tsx","PerformanceProvider")},17316:(e,t,r)=>{"use strict";r.d(t,{PerformanceProvider:()=>a});var s=r(60687),o=r(43210);function a({children:e,enableMonitoring:t=!0}){let[r,a]=(0,o.useState)([]);return(0,s.jsx)(s.Fragment,{children:e})}r(9555)},20140:(e,t,r)=>{"use strict";r.d(t,{d:()=>m});var s=r(43210);let o={ADD_TOAST:"ADD_TOAST",UPDATE_TOAST:"UPDATE_TOAST",DISMISS_TOAST:"DISMISS_TOAST",REMOVE_TOAST:"REMOVE_TOAST"},a=0,n=new Map,i=(e,t)=>{switch(t.type){case o.ADD_TOAST:return{...e,toasts:[t.toast,...e.toasts].slice(0,5)};case o.UPDATE_TOAST:return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case o.DISMISS_TOAST:{let{toastId:r}=t;return r?p(r):e.toasts.forEach(e=>{p(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,open:!1}:e)}}case o.REMOVE_TOAST:if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},l=[],d={toasts:[]};function c(e){d=i(d,e),l.forEach(e=>{e(d)})}function u({...e}){let t=(a=(a+1)%Number.MAX_VALUE).toString(),r=()=>c({type:o.DISMISS_TOAST,toastId:t});return c({type:o.ADD_TOAST,toast:{...e,id:t,open:!0,onOpenChange:e=>{e||r()}}}),{id:t,dismiss:r,update:e=>c({type:o.UPDATE_TOAST,toast:{...e,id:t}})}}function m(){let[e,t]=s.useState(d);return s.useEffect(()=>(l.push(t),()=>{let e=l.indexOf(t);e>-1&&l.splice(e,1)}),[e]),{...e,toast:u,dismiss:e=>c({type:o.DISMISS_TOAST,toastId:e})}}function p(e){if(n.has(e))return;let t=setTimeout(()=>{n.delete(e),c({type:o.REMOVE_TOAST,toastId:e})},1e6);n.set(e,t)}},24919:(e,t,r)=>{"use strict";r.d(t,{PerformanceToggle:()=>a}),r(60687);var s=r(43210);r(9555);class o{async analyzeBundlePerformance(){return null}extractChunkName(e){let t=e.split("/");return t[t.length-1].split("?")[0]}addMetrics(e){this.metrics.push(e),this.metrics.length>this.maxMetrics&&this.metrics.shift()}getPerformanceTrends(){if(this.metrics.length<2)return{averageLoadTime:0,averageBundleSize:0,trend:"stable"};let e=this.metrics.slice(-10),t=this.metrics.slice(-20,-10),r=e.reduce((e,t)=>e+t.loadTime,0)/e.length,s=t.length>0?t.reduce((e,t)=>e+t.loadTime,0)/t.length:r,o=e.reduce((e,t)=>e+t.totalSize,0)/e.length,a=t.length>0?t.reduce((e,t)=>e+t.totalSize,0)/t.length:o,n="stable",i=(r-s)/s,l=(o-a)/a;return i>.1||l>.1?n="degrading":(i<-.1||l<-.1)&&(n="improving"),{averageLoadTime:r,averageBundleSize:o,trend:n}}getLargestChunks(e=5){return 0===this.metrics.length?[]:this.metrics[this.metrics.length-1].chunks.sort((e,t)=>t.size-e.size).slice(0,e)}formatSize(e){if(0===e)return"0 B";let t=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,t)).toFixed(2))+" "+["B","KB","MB","GB"][t]}generateReport(){if(0===this.metrics.length)return"No bundle metrics available";let e=this.metrics[this.metrics.length-1],t=this.getPerformanceTrends(),r=this.getLargestChunks();return`
Bundle Performance Report
========================
Total Bundle Size: ${this.formatSize(e.totalSize)}
Gzipped Size: ${this.formatSize(e.gzippedSize)}
Load Time: ${e.loadTime.toFixed(2)}ms
Trend: ${t.trend}

Largest Chunks:
${r.map(e=>`- ${e.name}: ${this.formatSize(e.size)}`).join("\n")}

Recommendations:
${this.generateRecommendations(e,t)}
    `.trim()}generateRecommendations(e,t){let r=[];e.totalSize>1048576&&r.push("- Consider code splitting to reduce initial bundle size"),e.loadTime>3e3&&r.push("- Optimize loading performance with lazy loading"),"degrading"===t.trend&&r.push("- Bundle size is increasing - review recent changes");let s=e.chunks.sort((e,t)=>t.size-e.size)[0];return s&&s.size>512e3&&r.push(`- Consider splitting large chunk: ${s.name}`),r.length>0?r.join("\n"):"- Bundle performance looks good!"}clearMetrics(){this.metrics=[]}constructor(){this.metrics=[],this.maxMetrics=50}}function a(){let[e,t]=(0,s.useState)(!1);return null}new o,r(4780)},25139:(e,t,r)=>{"use strict";r.d(t,{NavigationProvider:()=>o});var s=r(12907);let o=(0,s.registerClientReference)(function(){throw Error("Attempted to call NavigationProvider() from the server but NavigationProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Ewasl\\eWasl.com_Cursor_Digital_Ocean\\ewasl-app\\src\\contexts\\NavigationContext.tsx","NavigationProvider");(0,s.registerClientReference)(function(){throw Error("Attempted to call useNavigation() from the server but useNavigation is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Ewasl\\eWasl.com_Cursor_Digital_Ocean\\ewasl-app\\src\\contexts\\NavigationContext.tsx","useNavigation")},34393:(e,t,r)=>{"use strict";r.d(t,{LanguageProvider:()=>n});var s=r(60687),o=r(43210);let a=(0,o.createContext)(void 0);function n({children:e}){let[t,r]=(0,o.useState)("ar"),n="ar"===t;return(0,s.jsx)(a.Provider,{value:{language:t,setLanguage:e=>{r(e)},isRTL:n,direction:n?"rtl":"ltr"},children:e})}},36721:(e,t,r)=>{"use strict";r.d(t,{NavigationProvider:()=>n,c:()=>i});var s=r(60687),o=r(43210);let a=(0,o.createContext)(void 0);function n({children:e}){let[t,r]=(0,o.useState)(!1),[n,i]=(0,o.useState)("ar"),[l,d]=(0,o.useState)(!1),[c,u]=(0,o.useState)(!1),m=(0,o.useCallback)(()=>{r(e=>!e)},[]),p=(0,o.useCallback)(()=>{r(!1)},[]),h=(0,o.useCallback)(()=>{r(!0)},[]),f=(0,o.useCallback)(e=>{},[l,t]);return(0,s.jsx)(a.Provider,{value:{isSidebarOpen:t,language:n,isMobile:l,setLanguage:f,toggleSidebar:m,closeSidebar:p,openSidebar:h},children:e})}function i(){let e=(0,o.useContext)(a);if(void 0===e)throw Error("useNavigation must be used within a NavigationProvider");return e}},45399:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>f,metadata:()=>h});var s=r(37413),o=r(35759),a=r.n(o),n=r(79737),i=r(6931),l=r(56109);function d(){return(0,s.jsxs)("div",{className:"hidden",children:[(0,s.jsx)("div",{className:"bg-gradient-to-br from-blue-50/30 via-white to-purple-50/30"}),(0,s.jsx)("div",{className:"bg-gradient-to-r from-blue-600 to-purple-600"}),(0,s.jsx)("div",{className:"bg-gradient-to-r from-blue-700 to-purple-700"}),(0,s.jsx)("div",{className:"bg-gradient-to-r from-emerald-50 to-teal-50"}),(0,s.jsx)("div",{className:"bg-gradient-to-r from-emerald-500 to-teal-500"}),(0,s.jsx)("div",{className:"shadow-lg hover:shadow-xl"}),(0,s.jsx)("div",{className:"shadow-sm"}),(0,s.jsx)("div",{className:"backdrop-blur-sm bg-white/70"}),(0,s.jsx)("div",{className:"bg-white/50"}),(0,s.jsx)("div",{className:"rounded-2xl rounded-xl rounded-lg rounded-full"}),(0,s.jsx)("div",{className:"text-blue-600 text-purple-600 text-emerald-600 text-orange-600 text-red-600"}),(0,s.jsx)("div",{className:"bg-blue-500/10 bg-emerald-500/10 bg-purple-500/10 bg-orange-500/10 bg-red-500/10"}),(0,s.jsx)("div",{className:"border-blue-200/50 border-emerald-200/50 border-purple-200/50 border-orange-200/50 border-red-200/50"}),(0,s.jsx)("div",{className:"transition-all duration-300 hover:-translate-y-1"}),(0,s.jsx)("div",{className:"transition-shadow duration-200"}),(0,s.jsx)("div",{className:"bg-clip-text text-transparent"}),(0,s.jsx)("div",{className:"hover:border-blue-300 hover:border-emerald-300"}),(0,s.jsx)("div",{className:"hover:bg-gradient-to-r hover:from-blue-50/50 hover:to-purple-50/30"}),(0,s.jsx)("div",{className:"hover:from-emerald-50/50 hover:to-teal-50/30"}),(0,s.jsx)("div",{className:"text-emerald-700 bg-emerald-100 text-red-700 bg-red-100"}),(0,s.jsx)("div",{className:"p-6 p-4 p-3 p-2 px-4 py-2 px-2 py-1"}),(0,s.jsx)("div",{className:"gap-6 gap-4 gap-3 gap-2 gap-1"}),(0,s.jsx)("div",{className:"space-y-8 space-y-6 space-y-4 space-y-3 space-y-2"}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 lg:grid-cols-7"}),(0,s.jsx)("div",{className:"flex items-center justify-between items-start items-baseline"}),(0,s.jsx)("div",{className:"text-3xl text-xl text-lg text-sm text-xs font-bold font-semibold font-medium"}),(0,s.jsx)("div",{className:"leading-relaxed tracking-tight"})]})}var c=r(7043),u=r(25139),m=r(16126),p=r(74216);r(61135),r(69440),r(44524),Promise.all([r.e(9165),r.e(4274)]).then(r.bind(r,54274)).then(({initializeServer:e})=>{e()});let h={title:"eWasl Social Scheduler",description:"منصة إدارة وسائل التواصل الاجتماعي مع دعم متميز للغة العربية"};function f({children:e}){return(0,s.jsx)("html",{lang:"ar",dir:"rtl",children:(0,s.jsx)("body",{className:`${a().variable} antialiased bg-background text-foreground`,children:(0,s.jsx)(c.LanguageProvider,{children:(0,s.jsx)(u.NavigationProvider,{children:(0,s.jsx)(l.default,{children:(0,s.jsxs)(m.PerformanceProvider,{children:[(0,s.jsx)(d,{}),e,(0,s.jsx)(n.Toaster,{}),(0,s.jsx)(i.Toaster,{position:"top-right"}),(0,s.jsx)(p.PerformanceToggle,{})]})})})})})})}},45730:(e,t,r)=>{Promise.resolve().then(r.bind(r,52581)),Promise.resolve().then(r.bind(r,14311)),Promise.resolve().then(r.bind(r,24919)),Promise.resolve().then(r.bind(r,17316)),Promise.resolve().then(r.bind(r,14947)),Promise.resolve().then(r.bind(r,34393)),Promise.resolve().then(r.bind(r,36721))},56109:(e,t,r)=>{"use strict";r.d(t,{default:()=>o});var s=r(12907);let o=(0,s.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Ewasl\\\\eWasl.com_Cursor_Digital_Ocean\\\\ewasl-app\\\\src\\\\components\\\\auth\\\\supabase-provider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Ewasl\\eWasl.com_Cursor_Digital_Ocean\\ewasl-app\\src\\components\\auth\\supabase-provider.tsx","default");(0,s.registerClientReference)(function(){throw Error("Attempted to call useSupabase() from the server but useSupabase is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Ewasl\\eWasl.com_Cursor_Digital_Ocean\\ewasl-app\\src\\components\\auth\\supabase-provider.tsx","useSupabase")},61135:()=>{},69440:()=>{},70058:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=70058,e.exports=t},74216:(e,t,r)=>{"use strict";r.d(t,{PerformanceToggle:()=>o});var s=r(12907);(0,s.registerClientReference)(function(){throw Error("Attempted to call PerformanceDashboard() from the server but PerformanceDashboard is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Ewasl\\eWasl.com_Cursor_Digital_Ocean\\ewasl-app\\src\\components\\ui\\performance-dashboard.tsx","PerformanceDashboard");let o=(0,s.registerClientReference)(function(){throw Error("Attempted to call PerformanceToggle() from the server but PerformanceToggle is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Ewasl\\eWasl.com_Cursor_Digital_Ocean\\ewasl-app\\src\\components\\ui\\performance-dashboard.tsx","PerformanceToggle")},77274:(e,t,r)=>{Promise.resolve().then(r.bind(r,6931)),Promise.resolve().then(r.bind(r,56109)),Promise.resolve().then(r.bind(r,74216)),Promise.resolve().then(r.bind(r,16126)),Promise.resolve().then(r.bind(r,79737)),Promise.resolve().then(r.bind(r,7043)),Promise.resolve().then(r.bind(r,25139))},79481:(e,t,r)=>{"use strict";r.d(t,{U:()=>n,x:()=>i});var s=r(35622);let o=null;async function a(){if(o)return o;try{let e=await fetch("/api/config/supabase");if(!e.ok)throw Error(`Failed to fetch config: ${e.status}`);let t=await e.json();return o=t,t}catch(e){throw console.error("[Supabase Client] Failed to fetch dynamic config:",e),e}}function n(){let e="https://nnxfzhxqzmriggulsudr.supabase.co",t="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5ueGZ6aHhxem1yaWdndWxzdWRyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIwNjg3NjgsImV4cCI6MjA2NzY0NDc2OH0.1qBlXLOqGtJX_LFRC7XihOpwCtD1U1I2_jdyJ8sq_8w";return e&&t&&!e.includes("placeholder")&&!t.includes("placeholder")?(0,s.createBrowserClient)(e,t):(0,s.createBrowserClient)("https://placeholder.supabase.co","placeholder-key")}async function i(){let e="https://nnxfzhxqzmriggulsudr.supabase.co",t="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5ueGZ6aHhxem1yaWdndWxzdWRyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIwNjg3NjgsImV4cCI6MjA2NzY0NDc2OH0.1qBlXLOqGtJX_LFRC7XihOpwCtD1U1I2_jdyJ8sq_8w";if(e&&t&&!e.includes("placeholder")&&!t.includes("placeholder"))return(0,s.createBrowserClient)(e,t);let r=await a();return(0,s.createBrowserClient)(r.url,r.anonKey)}},79737:(e,t,r)=>{"use strict";r.d(t,{Toaster:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Ewasl\\eWasl.com_Cursor_Digital_Ocean\\ewasl-app\\src\\components\\ui\\toaster.tsx","Toaster")}};